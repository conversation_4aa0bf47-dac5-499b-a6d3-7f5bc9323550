# -*- coding: utf-8 -*-
import base64

from markupsafe import Markup
from odoo import _, api, fields, models
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_compare, float_is_zero


class SaleOrder(models.Model):
    _inherit = "sale.order"

    archieved_account_move_ids = fields.One2many(
        "account.move", "achieved_sale_order_id", string="Archieve Account Move"
    )
    count_achieved_move = fields.Integer(
        "Count Archieved Move", compute="_compute_count_achieved_move"
    )
    customer_type = fields.Selection(related="partner_id.customer_type")
    is_rewrite = fields.Boolean("Is Rewrite", default=False, copy=False)

    def action_view_achieved_move_total(self):
        # return the move lines of the achieved move with group by account context
        self.ensure_one()
        if not self.archieved_account_move_ids:
            return
        archieved_account_move_ids = self.archieved_account_move_ids.filtered(
            lambda m: m.state == "posted"
        )
        if archieved_account_move_ids:
            return {
                "name": _("Archieve Moves Total"),
                "type": "ir.actions.act_window",
                "res_model": "account.move.line",
                "view_mode": "list",
                "domain": [("id", "in", archieved_account_move_ids.line_ids.ids)],
                "context": {"group_by": "account_id"},
            }
        # if no confirmed move return a warning
        raise UserError(_("There is no confirmed move to show."))

    def action_confirm(self):
        res = super(SaleOrder, self).action_confirm()
        self.create_archieved_input_sales_move()
        return res

    def _compute_count_achieved_move(self):
        for rec in self:
            rec.count_achieved_move = len(rec.archieved_account_move_ids)

    def action_view_achieved_move(self):
        self.ensure_one()
        if len(self.archieved_account_move_ids) > 1:
            action = {
                "name": _("Archieve Account Move"),
                "type": "ir.actions.act_window",
                "res_model": "account.move",
                "view_mode": "list,form",
                "domain": [("id", "in", self.archieved_account_move_ids.ids)],
            }
        else:
            action = {
                "name": _("Archieve Account Move"),
                "type": "ir.actions.act_window",
                "res_model": "account.move",
                "view_mode": "form",
                "res_id": self.archieved_account_move_ids.id,
                "domain": [("id", "in", self.archieved_account_move_ids.ids)],
            }
        action["context"] = {"source_sale_id": self.id}
        return action

    def action_process_order(self, force_close=False):
        res = super(SaleOrder, self).action_process_order(force_close=force_close)
        if self.state == "sale":
            self.order_line.write({"state": "sale"})
            if self.invoice_status == "to invoice":
                self._create_invoices()
        if self.archieved_account_move_ids or self.state != "sale":
            return res
        self.create_archieved_input_sales_move()
        return res

    def _get_account_value(self):
        # this function will return the account value for the achieved sales move
        # it will return the dictionary of account_id and balance
        # considering the existing achieved sales move
        self.ensure_one()
        if not self.fiscal_position_id:
            return
        fp = self.fiscal_position_id
        # get the account from fiscal position
        # if any of the account is not set then raise error
        if (
            not fp.account_credit_achieved_sale_id
            or not fp.account_credit_offset_ac_id
            or not fp.account_credit_wip_cost_id
            or not fp.account_credit_wip_gp_id
            or
            # not fp.account_credit_gst_collected_id or
            not fp.account_debit_cost_sale_id
            or not fp.account_debit_ach_gross_profit_id
            or not fp.account_debit_achsal_est_profit_id
            or not fp.account_debit_wip_cont_ach_sale_id
            # not fp.account_debit_gst_suspense_ex_cus_dep_id
        ):
            raise UserError(_("Please set all the accounts in fiscal position."))
        floor_cost = sum(
            line.product_uom_qty * line.floor_cost
            for line in self.order_line.filtered(lambda l: l.product_id)
        )
        # Put all the account on a Dict
        vals = {
            fp.account_credit_achieved_sale_id: -self.amount_untaxed,
            fp.account_debit_cost_sale_id: floor_cost,
            fp.account_credit_offset_ac_id: -self.margin,
            fp.account_debit_ach_gross_profit_id: self.amount_untaxed - floor_cost,
            fp.account_debit_achsal_est_profit_id: self.margin,
            fp.account_debit_wip_cont_ach_sale_id: self.amount_untaxed,
            fp.account_credit_wip_cost_id: -floor_cost,
            fp.account_credit_wip_gp_id: -(self.amount_untaxed - floor_cost),
        }
        map_vals = {}
        for account_id, balance in vals.items():
            account_id = self.fiscal_position_id.map_account(
                account_id, False, self.partner_invoice_id.state_id
            )
            map_vals[account_id] = balance
        vals = map_vals
        move_ids = self.archieved_account_move_ids.filtered(
            lambda m: m.state != "cancel"
        )
        if move_ids:
            for move in move_ids:
                for line in move.invoice_line_ids:
                    if line.account_id in vals:
                        vals[line.account_id] -= line.balance
                    # else:balance
                    #     vals[line.account_id.id] = line.
        # check if all the vals is 0
        vals = {
            k: v for k, v in vals.items() if not float_is_zero(v, precision_digits=2)
        }
        # get the rewrite context
        # if not self.is_rewrite:
        #     vals[fp.account_credit_gst_collected_id.id] = -self.amount_tax
        #     vals[fp.account_debit_gst_suspense_ex_cus_dep_id.id] = self.amount_tax

        return vals

    def create_archieved_input_sales_move(self):
        # Create a new move
        vals_list = []
        for rec in self:
            # find the journal Achieved Sales Journal
            journal_id = self.env["account.journal"].search(
                [
                    ("name", "=ilike", "Achieved Sales Journal"),
                    ("company_id", "=", rec.company_id.id),
                ],
                limit=1,
            )
            if not journal_id:
                raise UserError(
                    _("Please create a journal with name Achieved Sales Journal.")
                )
            vals = {
                "branch_id": rec.branch_id.id,
                "company_id": rec.company_id.id,
                "journal_id": journal_id.id,
                "achieved_sale_order_id": rec.id,
                "ref": rec.name,
                "move_type": "entry",
            }
            account_vals = rec._get_account_value()
            if not account_vals:
                continue
            if account_vals:
                counter = 1
                invoice_line_ids = []
                for account_id, balance in account_vals.items():
                    invoice_line_ids += [
                        (
                            0,
                            0,
                            {
                                "name": "Line " + str(counter),
                                "account_id": account_id.id,
                                "balance": balance,
                            },
                        )
                    ]
                    counter += 1
            if invoice_line_ids:
                vals["invoice_line_ids"] = invoice_line_ids
            if vals:
                vals_list.append(vals)
        moves = self.env["account.move"].create(vals_list)
        moves.action_post()

    # def _get_invoiceable_lines(self, final=False):
    #     for line in self.order_line:
    #         if line.state == "sale" and not line.display_type:
    #             if line.product_id.invoice_policy == "order":
    #                 line.qty_to_invoice = line.product_uom_qty - line.qty_invoiced
    #             else:
    #                 line.qty_to_invoice = line.qty_delivered - line.qty_invoiced
    #         else:
    #             line.qty_to_invoice = 0
    #     res = super()._get_invoiceable_lines(final=final)
    #     return res

    def button_start_rewrite(self):
        return {
            "name": _("Rewrite Reason"),
            "type": "ir.actions.act_window",
            "res_model": "sale.rewrite.reason.wizard",
            "view_mode": "form",
            "target": "new",
            # 'context': {'default_sale_order_id': self.id},
        }

    def start_rewrite(self, rewrite_reason):
        self.ensure_one()
        # post a log note to the chatter
        self.message_post(
            body=_(
                "The order is being rewritten."
                + Markup("<br />Reason: %s" % rewrite_reason)
            )
        )
        self.write(
            {
                # 'state': 'draft',
                "is_rewrite": True,
                "signature": False,
                "locked": False,
            }
        )
        # when we rewrite, we need to cancel the picking

    def check_valid_rewrite(self):
        self.ensure_one()
        if self.order_line and sum(self.order_line.mapped("product_uom_qty")) == 0:
            raise UserError(_("Order Lines have no quantity, cancel the Sales Order."))

        purchase_order_ids = self._get_purchase_orders()
        if not purchase_order_ids:
            purchase_order_ids = self.env["purchase.order"].search(
                [("origin", "ilike", self.name)]
            )
        else:
            additional_po = self.env["purchase.order"].search(
                [("origin", "ilike", self.name)]
            )
            purchase_order_ids |= additional_po
        if not purchase_order_ids:
            return
        mto_lines = self.order_line.filtered(
            lambda l: l.route_id and "mto" in l.route_id.name.lower()
        )
        if not mto_lines:
            return
        for product in mto_lines.mapped("product_id"):
            so_qty = sum(
                mto_lines.filtered(lambda l: l.product_id == product).mapped(
                    "product_uom_qty"
                )
            )
            po_line = purchase_order_ids.mapped("order_line").filtered(
                lambda l: l.product_id == product and l.state in ["purchase", "done"]
            )
            po_qty = sum(po_line.mapped("product_qty"))
            if so_qty < po_qty:
                raise UserError(
                    _(
                        "Product '%s': Quantity in SO (%s) is less than total in confirmed PO (%s).\n"
                        "Please update the PO before finishing the rewrite."
                    )
                    % (product.display_name, so_qty, po_qty)
                )

    def button_finish_rewrite(self):
        self.ensure_one()
        self.check_valid_rewrite()
        # self.picking_ids.filtered(lambda p: p.state in ['draft', 'confirmed', 'waiting']).move_ids.unlink()
        # self.action_confirm()
        # we need to create a new achived move for the rewritten order
        if self.state != "cancel":
            self.create_archieved_input_sales_move()
        self.is_rewrite = False
        self.locked = True
        sum_minimum_deposit = sum(self.order_line.mapped("minimum_deposit"))
        sum_downpayment_amount = sum(
            self.downpayment_ids.filtered(
                lambda x: x.state in ["paid", "in_process"]
            ).mapped("amount_signed")
        )
        self.prepayment_amount_allocated = sum_minimum_deposit - sum_downpayment_amount
        self.message_post(body=_("The order has been rewritten."))

    # def _compute_can_see_review_buttons(self):
    #     res = super(SaleOrder, self)._compute_can_see_review_buttons()
    #     for order in self:
    #         order.can_see_review_buttons = (
    #             order.can_see_review_buttons and not order.is_rewrite
    #         )
    #     return res


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    # @api.depends('qty_invoiced', 'qty_delivered', 'product_uom_qty', 'state')
    # def _compute_qty_to_invoice(self):
    #     """
    #     Compute the quantity to invoice. If the invoice policy is order, the quantity to invoice is
    #     calculated from the ordered quantity. Otherwise, the quantity delivered is used.
    #     """
    #     res = super(SaleOrderLine, self)._compute_qty_to_invoice()
    #     for line in self:
    #         if line.state == 'complete' and not line.display_type:
    #             if line.product_id.invoice_policy == 'order':
    #                 line.qty_to_invoice = line.product_uom_qty - line.qty_invoiced
    #             else:
    #                 line.qty_to_invoice = line.qty_delivered - line.qty_invoiced
    #         else:
    #             line.qty_to_invoice = 0
    #     return res
    # floor_cost = fields.Float()
    # state_id = fields.Many2one(
    #     "res.country.state", string="State", compute="_compute_state_id"
    # )

    # @api.depends("order_id.fiscal_position_id", "order_id.partner_invoice_id")
    # def _compute_state_id(self):
    #     for rec in self:
    #         online_sale_fiscal = self.env.ref(
    #             "modula_fiscal_position.online_sales_fiscal_position"
    #         )
    #         fiscal_position = rec.order_id.fiscal_position_id
    #         partner_invoice = rec.order_id.partner_invoice_id
    #         if (
    #             fiscal_position
    #             and fiscal_position == online_sale_fiscal
    #             and partner_invoice
    #             and partner_invoice.state_id
    #         ):
    #             rec.state_id = partner_invoice.state_id
    #         else:
    #             rec.state_id = False
