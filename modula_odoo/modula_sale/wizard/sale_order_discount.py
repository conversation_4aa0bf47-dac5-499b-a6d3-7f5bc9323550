from odoo import Command, _, api, fields, models
from odoo.exceptions import ValidationError


class SaleOrderDiscount(models.TransientModel):
    _inherit = "sale.order.discount"

    def _prepare_discount_line_values(self, product, amount, taxes, description=None):
        # self.ensure_one()
        # vals = {
        #     'order_id': self.sale_order_id.id,
        #     'product_id': product.id,
        #     'sequence': 999,
        #     'price_unit': -amount,
        #     'tax_id': [Command.set(taxes.ids)],
        # }
        # if description:
        #     # If not given, name will fallback on the standard SOL logic (cf. _compute_name)
        #     vals['name'] = description
        # return vals
        res = super()._prepare_discount_line_values(product, amount, taxes, description)
        if not taxes:
            tax_ids = self.env["account.tax"].search(
                [
                    ("type_tax_use", "=", "sale"),
                    ("description", "=", "GST Sales"),
                    ("company_id", "=", self.company_id.id),
                ],
                limit=1,
            )
            amount = amount * (100 / (100 + tax_ids.amount))
            # res['price_unit'] = amount
            # res['tax_id'] = [Command.set(tax_ids.ids)]
            res.update({"price_unit": -amount, "tax_id": [Command.set(tax_ids.ids)]})
        return res

    def action_apply_discount(self):
        # order_lines = self.sale_order_id.order_line
        # error_messages = []
        # if self.discount_type in ["so_discount", "sol_discount"] and self.discount_percentage:
        #     discount_percentage = self.discount_percentage*100
        #     for line in order_lines:
        #         min_discount = line._get_min_discount_limit_from_product_id_employee_id(
        #             line.product_id.id, line.order_id.employee_id.id
        #         )
        #         if line.is_this_line_need_approve(line.product_id.id, line.order_id.employee_id.id, discount_percentage):
        #             error_messages.append(_("%s: Discount over %s%%, not approved") % (line.product_id.name, min_discount))
        # elif self.discount_type == "amount" and self.discount_amount:
        #     discount_amount = self.discount_amount
        #     percentage = (discount_amount * 100) / self.sale_order_id.amount_untaxed
        #     for line in order_lines:
        #         min_discount = line._get_min_discount_limit_from_product_id_employee_id(
        #             line.product_id.id, line.order_id.employee_id.id)
        #         if line.is_this_line_need_approve(line.product_id.id, line.order_id.employee_id.id, percentage):
        #             error_messages.append(_("%s: Discount over %s%%, not approved") % (line.product_id.name, min_discount))

        # if error_messages:
        #     error_messages.insert(0, "Discount Exceeds Limit")
        #     if self.discount_type == "amount":
        #         error_messages.insert(1, "Total discount: %s %s (%.2f%%) \n" % (discount_amount,self.sale_order_id.currency_id.symbol, percentage))
        #     raise ValidationError("\n".join(error_messages))
        return super().action_apply_discount()

    def _create_discount_lines(self):
        res = super()._create_discount_lines()
        res._compute_amount()
        return res


    def action_apply_discount_with_validate(self):
        # self.action_apply_discount()
        return {
            "type": "ir.actions.client",
            "tag": "action_apply_discount_with_validate",
            "params": {
                "sale_order_id": self.sale_order_id.id,
                "discount_type": self.discount_type,
                "discount_percentage": self.discount_percentage,
                "discount_amount": self.discount_amount,
                "order_lines": self.sale_order_id.order_line.ids,
            },
        }
