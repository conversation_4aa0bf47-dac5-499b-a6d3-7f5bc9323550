from odoo import Command, _, api, fields, models
from odoo.exceptions import ValidationError


class SaleOrderDiscount(models.TransientModel):
    _inherit = "sale.order.discount"

    def _prepare_discount_line_values(self, product, amount, taxes, description=None):
        # self.ensure_one()
        # vals = {
        #     'order_id': self.sale_order_id.id,
        #     'product_id': product.id,
        #     'sequence': 999,
        #     'price_unit': -amount,
        #     'tax_id': [Command.set(taxes.ids)],
        # }
        # if description:
        #     # If not given, name will fallback on the standard SOL logic (cf. _compute_name)
        #     vals['name'] = description
        # return vals
        res = super()._prepare_discount_line_values(product, amount, taxes, description)
        if not taxes:
            tax_ids = self.env["account.tax"].search(
                [
                    ("type_tax_use", "=", "sale"),
                    ("description", "=", "GST Sales"),
                    ("company_id", "=", self.company_id.id),
                ],
                limit=1,
            )
            amount = amount * (100 / (100 + tax_ids.amount))
            # res['price_unit'] = amount
            # res['tax_id'] = [Command.set(tax_ids.ids)]
            res.update({"price_unit": -amount, "tax_id": [Command.set(tax_ids.ids)]})
        return res

    def action_apply_discount(self):
        if self.sale_order_id.check_session_owner_is_store_manager():
            self = self.with_context(approve_discount=True)
        return super().action_apply_discount()

    def _create_discount_lines(self):
        res = super()._create_discount_lines()
        res._compute_amount()
        return res


    def action_validate_with_employee_selection(self):
        if self.discount_type in ["so_discount", "sol_discount"]:
            discount_percentage = self.discount_percentage*100
        else:
            discount_percentage = (self.discount_amount * 100) / self.sale_order_id.amount_untaxed
        return {
            "type": "ir.actions.client",
            "tag": "action_validate_with_employee_selection",
            "params": {
                "model": "sale.order",
                "sale_order_id": self.sale_order_id.id,
                "discount_type": self.discount_type,
                "discount_percentage": discount_percentage,
                "discount_amount": self.discount_amount,
                "order_lines": self.sale_order_id.order_line.read(['id', 'product_id', 'discount']),
            },
        }
