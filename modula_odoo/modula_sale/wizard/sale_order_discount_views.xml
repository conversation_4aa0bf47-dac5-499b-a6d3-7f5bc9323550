<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="sale_order_line_wizard_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.line.wizard.form.inherit</field>
        <field name="model">sale.order.discount</field>
            <field name="inherit_id" ref="sale.sale_order_line_wizard_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('row')]" position="replace">
                    <field name="discount_type" widget="radio" invisible="1"/>
                    <group/>
                    <group col="4">
                        <label for="discount_amount" string="Discount" invisible="discount_type != 'amount'"/>
                        <field name="discount_amount" invisible="discount_type != 'amount'" nolabel="1"/>
                        <label for="discount_percentage"
                                string="Discount"
                                invisible="discount_type not in ('so_discount', 'sol_discount')"/>
                        <field name="discount_percentage"
                                invisible="discount_type not in ('so_discount', 'sol_discount')"
                                widget="percentage" nolabel="1"/>
                    </group>
                </xpath>
                <xpath expr="//button[@name='action_apply_discount']" position="after">
                    <button type="object" string="Apply With Validate" name="action_apply_discount_with_validate" class="btn btn-primary" data-hotkey="q"/>
                </xpath>
            </field>
        </record>

</odoo>
