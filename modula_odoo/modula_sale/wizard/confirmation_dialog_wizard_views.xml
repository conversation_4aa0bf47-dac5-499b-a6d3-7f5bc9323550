<?xml version ="1.0" encoding="utf-8"?>
<odoo>

    <record id="default_underlay_wizard_form_view" model="ir.ui.view">
        <field name="name">confirmation.dialog.wizard.form.view</field>
        <field name="model">confirmation.dialog.wizard</field>
        <field name="arch" type="xml">
            <form class="default_underlay">
                <field name="description" readonly="1"/>
                <footer>
                    <button string="Yes, add it!" name="action_add_default_underlay" type="object" class="oe_highlight"/>
                    <button string="No thank you." name="action_without_default_underlay" type="object" class="btn btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="confirmation_dialog_wizard_form_view" model="ir.ui.view">
        <field name="name">confirmation.dialog.wizard.form.view</field>
        <field name="model">confirmation.dialog.wizard</field>
        <field name="arch" type="xml">
            <form class="default_underlay">
                <p>Do you really want to delete "<field name="attachment_id" readonly="1" options="{'no_open': True,}"/>"?</p>
                <footer>
                    <button string="Ok" name="action_confirmation_dialog" type="object" class="oe_highlight" context="{'is_document': True}"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="sale_info_dialog_wizard_form_view" model="ir.ui.view">
        <field name="name">sale.info.dialog.wizard.form.view</field>
        <field name="model">confirmation.dialog.wizard</field>
        <field name="arch" type="xml">
            <form class="info_sale_popup">
                <div class="container">
                    <div class="info-circle">i</div>
                    <div class="mt-4">
                        <div groups="account.group_account_manager">
                            <label for="retail_margin"/>:
                            <div class="text-nowrap">
                                <field name="retail_margin" class="oe_inline" readonly="1"/>
                                <field name="retail_amount_untaxed" invisible="1"/>
                                <span class="oe_inline" invisible="retail_amount_untaxed == 0">
                                    (<field name="retail_margin_percent" nolabel="1" class="oe_inline" widget="percentage" readonly="1"/>)
                                </span>
                            </div>
                        </div>
                        <div groups="base.group_user">
                            <label for="margin_floor"/>:
                            <div class="text-nowrap">
                                <field name="margin_floor" class="oe_inline" readonly="1"/>
                                <field name="retail_amount_untaxed" invisible="1"/>
                                <span class="oe_inline" invisible="retail_amount_untaxed == 0">
                                    (<field name="margin_floor_percent" nolabel="1" class="oe_inline" widget="percentage" readonly="1"/>)
                                </span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label for="retail_discount"/>:
                        <div class="text-nowrap">
                            <field name="retail_discount" class="oe_inline" readonly="1"/>
                        </div>
                    </div>
                    <div>
                        <label for="minimum_deposit_total" string="Deposit Amount Required"/>:
                        <div class="text-nowrap">
                            <field name="minimum_deposit_total" class="oe_inline" readonly="1"/>
                        </div>
                    </div>
                    <field name="currency_id" invisible="1"/>
                    <footer>
                        <button string="Ok" class="btn-secondary" special="cancel"/>
                    </footer>
                </div>
            </form>
        </field>
    </record>

</odoo>
