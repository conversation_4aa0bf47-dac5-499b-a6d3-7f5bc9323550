# -*- coding: utf-8 -*-
{
    "name": "Modula - Retail Sales",
    "version": "********.0",
    "category": "Sale",
    "description": """
Sales, Stores & Operations Portal
    """,
    "author": "ITMS Group",
    "website": "http://www.itmsgroup.com.au",
    "depends": [
        "base",
        "stock",
        "web",
        "sale",
        "product",
        "contacts",
        "modula_contact_extended",
        "modula_contact_name_separation",
        "modula_contact",
        "sale_subscription",
        "crm",
        "sale_stock",
        "modula_product",
        "account",
        "hr",
        "sale_margin",
        "survey",
        "modula_downpayment",
        "modula_sale_employee_selection",
        "stock_delivery",
        "modula_branch",
        "modula_product_variant_sale_price",
    ],
    "sequence": 0,
    "data": [
        # ============================================================
        # SECURITY SETTING - GROUP - PROFILE
        # ============================================================
        # 'security/',
        "security/ir.model.access.csv",
        "security/security.xml",
        # ============================================================
        # DATA
        # ============================================================
        # 'data/',
        "data/hr_job_data.xml",
        "data/ir_action_data.xml",
        "data/terms_conditions_data.xml",
        "data/data.xml",
        # ============================================================
        # WIZARD
        # ============================================================
        # 'wizard/',
        "wizard/confirmation_dialog_wizard_views.xml",
        "wizard/sale_order_cancel_views.xml",
        "wizard/sale_order_discount_views.xml",
        # ============================================================
        # VIEWS
        # ============================================================
        # 'views/',
        "views/sale_order_views.xml",
        "views/sale_order_custom_views.xml",
        "views/product_views.xml",
        "views/res_partner_views.xml",
        "views/default_charges_views.xml",
        "views/sale_order_portal_templates.xml",
        "views/survey_question_views.xml",
        "views/checklist_checklist_views.xml",
        "views/product_category_views.xml",
        "views/terms_conditions_views.xml",
        "views/sale_report_view.xml",
        "views/sale_order_line_views.xml",
        "views/hr_employee_views.xml",
        "views/stock_picking_views.xml",
        "views/templates.xml",
        "views/res_config_settings_views.xml",
        # ============================================================
        # MENU
        # ============================================================
        # 'menu/',
        "menu/menu.xml",
        "report/sale_order_report.xml",
    ],
    "assets": {
        "web.assets_backend": [
            ("include", "web._assets_core"),
            "modula_sale/static/src/scss/popup.scss",
            "modula_sale/static/src/scss/style_view_form.scss",
            "modula_sale/static/src/js/tax_totals_field.js",
            "modula_sale/static/src/views/**/*",
            "modula_sale/static/src/stock/widgets/qty_at_date_widget_sale.xml",
            "modula_sale/static/src/stock/widgets/qty_at_date_widget_sale.js",
            "modula_sale/static/src/stock/widgets/qty_at_date_widget_product.js",
        ],
        "web.assets_frontend": [
            "modula_sale/static/src/core/**/*",
            "modula_sale/static/src/scss/style_frontend.scss",
            "modula_sale/static/src/js/sale_portal_prepayment.js",
            "modula_sale/static/lib/inputmask/inputmask.min.js",
            "modula_sale/static/src/js/checklist_website.js",
            "modula_sale/static/src/js/checkout.js",
        ],
        "web.assets_common": [
            "modula_sale/static/lib/inputmask/inputmask.min.js",
        ],
        "web._assets_core": [
            # core
            "modula_sale/static/src/core/**/*",
        ],
        "web._assets_primary_variables": [
            (
                "before",
                "web/static/src/scss/primary_variables.scss",
                "modula_sale/static/src/scss/primary_variables.scss",
            ),
        ],
        # ========= Dark Mode =========
        "web.dark_mode_variables": [
            (
                "before",
                "modula_sale/static/src/scss/primary_variables.scss",
                "modula_sale/static/src/scss/primary_variables.dark.scss",
            ),
        ],
        "web.report_assets_common": [
            "modula_sale/static/src/report/report.scss",
        ],
    },
    "test": [],
    "demo": [],
    "installable": True,
    "active": False,
    "application": True,
    "license": "LGPL-3",
}
