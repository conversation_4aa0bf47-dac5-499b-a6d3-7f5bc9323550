<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_inherit_sale_order" inherit_id="sale.report_saleorder_document">
        <xpath expr="//table//th[@name='th_description']" position="before">
            <th class="text-start">Product</th>
        </xpath>
        <xpath expr="//table//td[@name='td_name']" position="before">
            <td name="td_product"><span t-field="line.product_id.name"></span></td>
        </xpath>
        <xpath expr="//div[hasclass('page')]" position="inside">
            <div class="oe_structure"/>
            <h5>Direct Deposit Details</h5>
            <t t-set="bank_detail" t-value="doc.company_id and doc.company_id.partner_id and doc.company_id.partner_id.bank_ids.filtered(lambda x: x.customer_deposits)[0] or []"/>
            <div class="row mb-4 table_banking_details">
                <div class="col-6">
                    <strong>Account Name</strong>
                    <div t-field="bank_detail.account_title"></div>
                </div>
                <div class="col-3">
                    <strong>BSB</strong>
                    <div t-field="bank_detail.aba_bsb"></div>
                </div>
                <div class="col-3">
                    <strong>Account Number</strong>
                    <div t-field="bank_detail.acc_number"></div>
                </div>
            </div>
            <div class="oe_structure"/>
        </xpath>
        <xpath expr="//div[@name='so_total_summary']" position="replace">
            <div class="clearfix" name="so_total_summary">
                <div id="total" class="row mt-n3" name="total">
                    <div t-attf-class="#{'col-6' if report_type != 'html' else 'col-sm-7 col-md-6'} ms-auto">
                        <table class="o_total_table table table-borderless">
                            <!-- Tax totals -->
                            <t t-call="modula_sale.document_tax_totals_template_modula">
                                <t t-set="tax_totals" t-value="doc.tax_totals"/>
                                <t t-set="currency" t-value="doc.currency_id"/>
                                <t t-set="sale_order" t-value="doc"/>
                            </t>
                        </table>
                    </div>
                </div>
            </div>
        </xpath>
    </template>

    <template id="document_tax_totals_template_modula">
        <!--
            Generic template to display tax totals in pdf reports.
            Used by invoices, SO and PO.

            ARGUMENTS:
            - currency: The res.currency to use.
            - tax_totals: dict in the form generated by account.move's _get_tax_totals.
        -->
        <t t-set="same_tax_base" t-value="tax_totals['same_tax_base']"/>
        <t t-foreach="tax_totals['subtotals']" t-as="subtotal">
            <tr class="o_subtotal">
                <td>
                    <span t-out="subtotal['name']">Untaxed Amount</span>
                </td>
                <td class="text-end">
                    <span t-att-class="oe_subtotal_footer_separator"
                            t-out="subtotal['base_amount_currency']"
                            t-options='{"widget": "monetary", "display_currency": currency}'
                    >27.00</span>
                </td>
            </tr>

            <t t-foreach="subtotal['tax_groups']" t-as="tax_group">
                <tr class="o_taxes">
                    <t t-if="same_tax_base or tax_group['display_base_amount_currency'] is None">
                        <td>
                            <span class="text-nowrap" t-out="tax_group['group_name']">Tax 15%</span>
                        </td>
                        <td class="text-end o_price_total">
                            <span class="text-nowrap"
                                    t-out="tax_group['tax_amount_currency']"
                                    t-options='{"widget": "monetary", "display_currency": currency}'
                            >1.05</span>
                        </td>
                    </t>
                    <t t-else="">
                        <td>
                            <span t-out="tax_group['group_name']">Tax 15%</span>
                            <span> on </span>
                            <span class="text-nowrap"
                                    t-out="tax_group['display_base_amount_currency']"
                                    t-options='{"widget": "monetary", "display_currency": currency}'
                            >27.00</span>
                        </td>
                        <td class="text-end o_price_total">
                            <span class="text-nowrap"
                                    t-out="tax_group['tax_amount_currency']"
                                    t-options='{"widget": "monetary", "display_currency": currency}'
                            >4.05</span>
                        </td>
                    </t>
                </tr>
            </t>
        </t>

        <tr t-if="'cash_rounding_base_amount_currency' in tax_totals">
            <td>Rounding</td>
            <td class="text-end">
                <span t-out="tax_totals['cash_rounding_base_amount_currency']"
                        t-options='{"widget": "monetary", "display_currency": currency}'
                >0</span>
            </td>
        </tr>

        <!--Total amount with all taxes-->
        <tr class="o_total">
            <td><strong>Total</strong></td>
            <td class="text-end">
                <strong t-out="tax_totals['total_amount_currency']"
                        t-options='{"widget": "monetary", "display_currency": currency}'
                >31.05</strong>
            </td>
        </tr>
        <!-- Paid information -->
        <t t-set="downpayment_ids" t-value="sale_order.downpayment_ids.filtered(lambda x: x.state in ['paid', 'in_process'])"/>
        <t t-foreach="downpayment_ids" t-as="downpayment">
            <tr class="o_subtotal">
                <td><i>Paid on <t t-esc="downpayment.date" t-options='{"widget": "date"}'/></i></td>
                <td class="text-end">
                    <span t-out="downpayment.amount_signed"
                            t-options='{"widget": "monetary", "display_currency": currency}'
                    >31.05</span>
                </td>
            </tr>
        </t>
        <tr class="o_subtotal">
            <td><strong>Amount Due</strong></td>
            <td class="text-end">
                <strong t-out="sale_order.remaining_amount"
                        t-options='{"widget": "monetary", "display_currency": currency}'
                >31.05</strong>
            </td>
        </tr>
    </template>
</odoo>
