# -*- coding: utf-8 -*-
import base64
import json
from collections import defaultdict
from datetime import datetime, time, timedelta

import magic
from odoo import Command, _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.http import request
from odoo.tools import (
    SQL,
    float_compare,
    float_is_zero,
    float_round,
    formatLang,
    is_html_empty,
)
from odoo.tools.mail import html_keep_url
from pytz import UTC, timezone

PAYMENT_STATE_SELECTION = [
    ("not_paid", "Not Paid"),
    ("in_payment", "In Payment"),
    ("paid", "Paid"),
    ("partial", "Partially Paid"),
    ("reversed", "Reversed"),
    ("invoicing_legacy", "Invoicing App Legacy"),
]


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    sqmeters = fields.Float("Sq. Meters")
    product_categ_id = fields.Many2one(
        "product.category",
        string="Product Category",
        related="product_template_id.categ_id",
    )
    charges_product = fields.Selection(
        [
            ("product", "Product"),
            ("charges", "Charges"),
        ],
        default="product",
    )
    floor_cost = fields.Float(
        string="Floor Cost",
        compute="_compute_floor_cost",
        digits="Product Price",
        store=True,
        readonly=False,
        copy=False,
        precompute=True,
        groups="base.group_user",
    )
    is_charge = fields.Boolean("Is Charge", related="product_template_id.is_charge")
    is_retail = fields.Boolean("Is Retail", default=False)
    discount = fields.Float(string="Discount (%)", digits=(16, 2))
    delivery_method = fields.Selection(
        [
            ("pickup", "Pickup"),
            ("delivery", "Delivery"),
        ],
        string="Delivery Method",
    )
    is_subcontract_service = fields.Boolean("Service?", default=False)
    service_detail_doc = fields.Binary(string="Service Detail Image")
    product_code = fields.Char("Product Code", related="product_id.default_code")
    name = fields.Text(inverse="_inverse_name")
    margin_floor = fields.Float(
        "Floor Margin",
        compute="_compute_margin_floor",
        digits="Product Price",
        store=True,
        groups="base.group_user",
        precompute=True,
    )
    margin_floor_percent = fields.Float(
        "Floor Margin (%)",
        compute="_compute_margin_floor",
        store=True,
        groups="base.group_user",
        precompute=True,
    )
    margin = fields.Float()
    margin_percent = fields.Float(string="Margin %")
    purchase_price = fields.Float(groups="account.group_account_manager")
    # temporary logic moved from other modules
    location_id = fields.Many2one(
        "stock.location",
        "Source Location",
        readonly=False,
        help="If a source location is selected, "
        "it will be used to define the route. "
        "Otherwise, it will get the location of "
        "the sale order",
    )
    procurement_group_id = fields.Many2one(
        "procurement.group", "Procurement group", copy=False
    )

    prod_parent_categ_id = fields.Many2one(
        "product.category",
        "Product Parent Category",
        compute="_compute_prod_parent_categ_id",
        store=True,
    )
    product_categ_id = fields.Many2one(
        "product.category",
        string="Product Category",
        related="product_template_id.categ_id",
    )

    over_discount_limit = fields.Boolean(
        string="Over Discount Limit",
        compute="_compute_over_discount_limit",
        inverse="_inverse_over_discount_limit",
        store=True,
        readonly=False,
    )
    discount_limit_approved = fields.Float()
    employee_approved_discount = fields.Many2one(
        "hr.employee", string="Employee Approved Discount"
    )

    @api.depends("discount")
    def _compute_over_discount_limit(self):
        for line in self:
            over_discount_limit = False
            min_discount = line._get_min_discount_limit()

            # Validate discount against limit
            if min_discount is not False and line.discount > min_discount:
                over_discount_limit = True
            line.over_discount_limit = over_discount_limit

    def _inverse_over_discount_limit(self):
        for line in self:
            sale_order = line.order_id
            if all(line.over_discount_limit == False for line in sale_order.order_line):
                sale_order.need_approve = False
            else:
                sale_order.need_approve = True

    def _get_min_discount_limit(self):
        product_discount_limit = self.product_id.categ_id._get_discount_limit()
        employee_discount_limit = self.order_id.employee_id._get_discount_limit()
        min_discount = (
            min(product_discount_limit, employee_discount_limit)
            if product_discount_limit and employee_discount_limit
            else product_discount_limit or employee_discount_limit
        )
        return min_discount

    def _get_min_discount_limit_from_product_id_employee_id(
        self, product_id, employee_id
    ):
        product = self.env["product.product"].browse(product_id)
        employee = self.env["hr.employee"].browse(employee_id)
        product_discount_limit = product.categ_id._get_discount_limit()
        employee_discount_limit = employee._get_discount_limit()
        min_discount = (
            min(product_discount_limit, employee_discount_limit)
            if product_discount_limit and employee_discount_limit
            else product_discount_limit or employee_discount_limit
        )
        return min_discount

    @api.depends("product_categ_id")
    def _compute_prod_parent_categ_id(self):
        for rec in self:
            charge_base = (
                self.env["product.category"]
                .sudo()
                .search([("name", "=", "Charges"), ("is_charge", "=", True)], limit=1)
            )
            if rec.product_categ_id:
                parent = rec.product_categ_id
                if parent.is_charge and charge_base:
                    rec.prod_parent_categ_id = charge_base.id
                    continue
                while parent.parent_id:
                    parent = parent.parent_id
                    if parent.is_charge and charge_base:
                        parent = charge_base
                        break
                rec.prod_parent_categ_id = parent.id
            else:
                rec.prod_parent_categ_id = False

    @api.depends("product_id", "product_template_id")
    def _compute_name(self):
        if self.product_template_id and not self.product_id:
            self.product_id = self.product_template_id.product_variant_id
        super()._compute_name()

    @api.depends("price_subtotal", "product_uom_qty", "floor_cost")
    def _compute_margin_floor(self):
        for line in self:
            line.margin_floor = line.price_subtotal - (
                line.floor_cost * line.product_uom_qty
            )
            line.margin_floor_percent = (
                line.price_subtotal and line.margin_floor / line.price_subtotal
            )

    @api.constrains("discount", "discount_limit_approved", "employee_approved_discount")
    def _check_discount_limit(self):
        for line in self:
            if (
                line.employee_approved_discount
                and line.discount_limit_approved
                and line.discount != line.discount_limit_approved
            ):
                raise ValidationError(
                    "Discount cannot be different than the approved discount which is %s"
                    % line.discount_limit_approved
                )

    def is_this_line_need_approve(self, product_id, employee_id, discount=None):
        if discount is None:
            discount = self.discount
        if not product_id:
            product_id = self.product_id.id
        if not employee_id:
            employee_id = self.order_id.employee_id.id

        min_discount = self._get_min_discount_limit_from_product_id_employee_id(
            product_id, employee_id
        )
        if min_discount is not False and discount > min_discount:
            if (
                self.discount_limit_approved
                and discount != self.discount_limit_approved
            ):
                return True
            elif not self.discount_limit_approved:
                return True
            else:
                return False
        return False

    def _inverse_name(self):
        for line in self:
            if line.is_subcontract_service:
                po_line = line._get_po_line()
                if po_line:
                    po_line.name = line.name
                invoice_line = line._get_invoice_lines()
                if invoice_line:
                    invoice_line.name = line.name

    def _get_po_line(self):
        return self.purchase_line_ids

    def edit_service_note(self):
        # self.ensure_one()
        return {
            "name": "Edit Service Note",
            "type": "ir.actions.act_window",
            "res_model": "sale.order.line",
            "view_mode": "form",
            "res_id": self.id,
            "view_id": self.env.ref(
                "modula_sale.view_sales_order_service_note_form"
            ).id,
            "target": "new",
        }

    def write(self, vals):
        if self.env.context.get('approve_discount', False):
            vals.update({
                'over_discount_limit': True,
                'employee_approved_discount': request.session.get('manager_approve', False)
            })
            request.session.pop('manager_approve', None)
        if vals.get('discount'):
            vals.update({
                'discount_limit_approved': vals['discount'],
            })
        return super().write(vals)


    # @api.onchange("is_subcontract_service")
    # def _onchange_is_subcontract_service(self):
    #     if self.is_subcontract_service:
    #         # find the 'Service' route
    #         route = self.env["stock.route"].search(
    #             [("name", "=", "Service"), ("company_id", "=", self.company_id.id)],
    #             limit=1,
    #         )
    #         if route:
    #             self.route_id = route.id
    #         self.name = self.order_id.name + " - Service"
    #     else:
    #         self._compute_name()
    #         self.route_id = False

    @api.depends("product_id", "company_id", "currency_id", "product_uom")
    def _compute_floor_cost(self):
        for line in self:
            if not line.product_id:
                line.floor_cost = 0.0
                continue
            line = line.with_company(line.company_id)
            floor_cost = line.product_id.floor_cost
            if floor_cost == 0 or line.product_id.is_sku:
                floor_cost = line.product_template_id.floor_cost

            # Convert the cost to the line UoM
            product_cost = line.product_id.uom_id._compute_price(
                floor_cost,
                line.product_uom,
            )

            line.floor_cost = line._convert_to_sol_currency(
                product_cost, line.product_id.cost_currency_id
            )

    # def _compute_invoice_status(self):
    #     res = super()._compute_invoice_status()
    #     precision = self.env["decimal.precision"].precision_get(
    #         "Product Unit of Measure"
    #     )
    #     for line in self:
    #         if line.state in BEFORE_INPUT_STATE:
    #             line.invoice_status = "no"
    #         elif not float_is_zero(line.qty_to_invoice, precision_digits=precision):
    #             line.invoice_status = "to invoice"
    #         elif line.is_downpayment and line.untaxed_amount_to_invoice == 0:
    #             line.invoice_status = "invoiced"
    #         elif (
    #             float_compare(
    #                 line.qty_invoiced, line.product_uom_qty, precision_digits=precision
    #             )
    #             >= 0
    #         ):
    #             line.invoice_status = "invoiced"
    #     return res

    def _compute_qty_to_invoice(self):
        """
        Compute the quantity to invoice. If the invoice policy is order, the quantity to invoice is
        calculated from the ordered quantity. Otherwise, the quantity delivered is used.
        """
        for line in self:
            if line.state not in ("draft", "sent") and not line.display_type:
                if line.product_id.invoice_policy == "order":
                    line.qty_to_invoice = line.product_uom_qty - line.qty_invoiced
                else:
                    line.qty_to_invoice = line.qty_delivered - line.qty_invoiced
            else:
                line.qty_to_invoice = 0

    def _prepare_procurement_group_vals(self):
        vals = super()._prepare_procurement_group_vals()
        # for compatibility with sale_quotation_sourcing
        if self._get_procurement_group_key()[0] == 10:
            if self.location_id:
                vals["name"] += "/" + self.location_id.name
        return vals

    def _get_procurement_group_key(self):
        """Return a key with priority to be used to regroup lines in multiple
        procurement groups

        """
        priority = 10
        key = 8, self.order_id.id
        # Check priority
        if key[0] >= priority:
            return key
        location_id = (
            self.location_id.id if self.location_id else self.order_id.location_id.id
        )
        return priority, location_id

    def _get_procurement_group(self):
        super()._get_procurement_group()
        return self.procurement_group_id or False

    def _get_protected_fields(self):
        # remove 'name'
        res = super()._get_protected_fields()
        res.remove("name")
        return res

    def action_open_available_stock(self):
        """
        Open the available stock for the sale order lines
        """
        return True
