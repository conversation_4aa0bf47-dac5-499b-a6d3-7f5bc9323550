from odoo import _, api, models


class ResUser(models.Model):
    _inherit = "res.users"

    @api.model
    def signup(self, values, token=None):
        res = super().signup(values, token)
        if "email" in values or "login" in values:
            email = values.get("email") or values.get("login")
            user = self.sudo().search([("email", "=", email)], limit=1)
            if user and user.partner_id:
                retail_type = self.env.ref(
                    "modula_contact_extended.res_partner_type_dom",
                    raise_if_not_found=False,
                )
                user.partner_id.write(
                    {"customer": True, "customer_type_id": retail_type.id}
                )
        return res
