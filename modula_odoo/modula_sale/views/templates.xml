<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="thank_you_contact_back" name="Thank You Contact Back" active="True">
        <t t-call="website_sale.checkout_layout">
            <h3 class="mb-4">Thank you for your order!</h3>
            <p>We will contact you as soon as possible.</p>
        </t>
    </template>

    <!-- /shop/checklist route -->
    <template id="checklist" name="Checklist" active="True">
        <t t-call="website_sale.checkout_layout">
            <t t-set="show_shorter_cart_summary" t-value="True"/>
            <t t-set="show_footer" t-value="True"/>
            <t t-set="redirect" t-valuef="/shop/checklist"/>
            <t t-set="oe_structure">
                <!-- This is the drag-and-drop area for website building blocs at the end of each
                     checkout page. This is append at the of the page in `checkout_layout`. The
                     templates created in the database to store blocs are hooked using XPath on the
                     `oe_struture` element ID. Therefore, we can't use dynamic IDs (like with
                     t-att-id) and each template needs to define a div element. -->
                <div class="oe_structure" id="oe_structure_website_sale_checklist_1"/>
            </t>

            <h3 class="mb-4">Delivery Details</h3>

            <form action="/shop/checklist/submit" method="post" class="checkout_autoformat o_wsale_checklist_fill checklist_form">

                <div>
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" t-nocache="The csrf token must always be up to date."/>
                    <t t-foreach="checklist_lines" t-as="checklist">
                        <div class="checklist-wrap" t-attf-data-invisible="{{ checklist['invisible'] }}">
                            <div class="checklist-block" t-attf-data-required="{{ checklist['required'] }}"
                                t-attf-data-trigger-answers="{{ checklist['trigger_answers'] }}">
                                <h6 t-esc="checklist['name']"/>
                                <p t-esc="checklist['title']"/>
                                <!-- Input text -->
                                <t t-if="checklist['type_checklist'] == 'char_box'">
                                    <input type="text" t-attf-name="{{checklist['id']}}" class="form-control" t-att-value="checklist['value']"/>
                                </t>

                                <!-- Textarea -->
                                <t t-elif="checklist['type_checklist'] == 'text_box'">
                                    <textarea t-attf-name="{{checklist['id']}}" class="form-control"><t t-esc="checklist['value']"/></textarea>
                                </t>

                                <!-- Date -->
                                <t t-elif="checklist['type_checklist'] == 'date'">
                                    <input type="date" t-attf-name="{{checklist['id']}}" class="form-control" t-att-value="checklist['value']"/>
                                </t>
                                <t t-elif="checklist['type_checklist'] == 'simple_choice'">
                                    <t t-foreach="checklist['suggested_answers']" t-as="answer">
                                        <label>
                                            <input type="radio"
                                                t-attf-name="{{checklist['id']}}"
                                                t-att-checked="'checked' if answer['value'] == checklist['value'] else None"
                                                t-att-value="answer['value']"
                                                t-att-required="checklist['required']"/>
                                            <span t-esc="answer['value']"/>
                                        </label>
                                    </t>
                                </t>
                                <t t-elif="checklist['type_checklist'] == 'multiple_choice'">
                                    <t t-foreach="checklist['suggested_answers']" t-as="answer">
                                        <label>
                                            <input type="checkbox"
                                                t-attf-name="{{checklist['id']}}_{{answer['id']}}"
                                                t-att-checked="'checked' if checklist['value'] and answer['value'] in checklist['value'].split(',') else None"
                                                t-att-value="answer['value']"/>
                                            <span t-esc="answer['value']"/>
                                        </label>
                                    </t>
                                </t>
                            </div>
                        </div>
                    </t>
                </div>
                <button id="save_checklist" class="btn btn-primary" style="visibility: hidden;">Submit</button>
            </form>

            <div class="oe_structure" id="oe_structure_website_sale_checklist_2"/>
        </t>
    </template>
</odoo>
