<?xml version ="1.0" encoding="utf-8"?>
<odoo>
    <!-- <record id="view_order_form_inherit_hide_shipping_site_address" model="ir.ui.view">
        <field name="name">sale.order.site.address.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="modula_install_address.view_order_form_inherit_hide_shipping"/>
        <field name="arch" type="xml">
            <group name="site_address" position="attributes">
                <attribute name="invisible">sale_type == 'retail'</attribute>
            </group>
            <group name="site_address" position="after">
                <field name="sale_type" invisible="1"/>
            </group>
            <group name="site_address" position="inside">
                <field name="amount_untaxed" invisible="1"/>
            </group>
        </field>
    </record> -->

    <record id="view_order_form_inherit" model="ir.ui.view">
		<field name="name">sale.order.form.view.inherit</field>
		<field name="model">sale.order</field>
		<field name="inherit_id" ref="sale.view_order_form"/>
        <field name="priority" eval="100"/>
		<field name="arch" type="xml">
            <field name="signature" position="replace">
                <field name="signature" widget="image" style="max-width: 50%;"/>
            </field>
            <xpath expr="//field[@name='order_line']/list/field[@name='price_subtotal'][2]" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='product_uom'][2]" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <button name="action_preview_sale_order" position="attributes">
                <attribute name="string">View Invoice &amp; Pay</attribute>
            </button>
            <field name="product_template_id" position="attributes">
                <attribute name="options">{'no_create': True, 'no_create_edit': True, 'no_open': True}</attribute>
            </field>
            <xpath
                expr="//field[@name='validity_date']"
                position="before"
            >
                <field name="location_id" string="Location"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="after">
                <!-- <widget name="web_ribbon" title="Paid" invisible="payment_state != 'paid'"/>
                <widget name="web_ribbon" title="Partially Paid" invisible="payment_state != 'partial'"/> -->
                <field name="is_sales_admin" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="context">{'form_view_ref': 'modula_contact.view_partner_customer_child_form', 'search_street': True, 'default_type': 'contact', 'search_by_name': True, 'default_customer': True, 'show_address': 1, 'show_vat': True}</attribute>
                <!-- <attribute name="widget">res_partner_many2one</attribute> -->
            </xpath>

            <!-- <xpath expr="//button[@id='create_invoice']" position="attributes">
                <attribute name="invisible">is_subscription and state != 'sale' or invoice_status != 'to invoice' or is_home_approvals</attribute>
            </xpath>
            <xpath expr="//button[@id='create_invoice_percentage']" position="attributes">
                <attribute name="invisible">is_subscription or invoice_status != 'no' or state != 'sale' or is_home_approvals</attribute>
            </xpath>
            <xpath expr="//button[@id='create_invoice_sub']" position="attributes">
                <attribute name="invisible">not is_subscription or state != 'sale' or invoice_status == 'to invoice' or is_home_approvals</attribute>
            </xpath> -->
            <xpath expr="//page[@name='order_lines']//list//field[@name='price_unit']" position="attributes">
                <attribute name="readonly">qty_invoiced &gt; 0 or not parent.is_state_before_input</attribute>
            </xpath>

            <!-- <field name="validity_date" position="attributes">
                <attribute name="invisible">state in ('sale', 'cancel', 'invoice_review', 'ins_manager_review', 'final_lay', 'complete', 'credit_check', 'rejected')</attribute>
            </field>
            <xpath expr="//group[@name='order_details']/div[1]" position="attributes">
                <attribute name="invisible">state in ('sale', 'cancel', 'invoice_review', 'ins_manager_review', 'final_lay', 'complete', 'credit_check', 'rejected')</attribute>
            </xpath>
            <xpath expr="//group[@name='order_details']/field[@name='date_order'][1]" position="attributes">
                <attribute name="invisible">state in ('sale', 'cancel', 'invoice_review', 'ins_manager_review', 'final_lay', 'complete', 'credit_check', 'rejected')</attribute>
            </xpath> -->

            <group name="order_details" position="inside">
                <field name="sale_type" invisible="1"/>
                <field name="is_home_approvals" invisible="1"/>
            </group>
			<page name='order_lines' position="inside">
                <notebook>
                    <page string="Delivery Checklist" name="checklist" invisible="not is_checklist_mandatory">
                        <div class="o_field_custom checklist_form_custom" id="checklist_form">
                            <field name="checklist_line_ids" invisible="1"/>
                            <field name="checklist_input_html"/>
                            <field name="checklist_input_text" invisible="0" style="visibility: hidden;"/>
                            <script type="text/javascript" src="/modula_sale/static/src/js/checklist_field.js"/>
                        </div>
                    </page>
                </notebook>
			</page>
            <xpath expr="//field[@name='order_line']/list/field[@name='price_total']" position="attributes">
                <attribute name="optional">show</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='price_total'][2]" position="attributes">
                <attribute name="optional">show</attribute>
            </xpath>
            <!-- <xpath expr="//header//button[1]" position="before">
                <field name="can_see_review_buttons" invisible="1"/>
                <button
                    name="action_process_order"
                    string="Validate"
                    type="object" class="btn btn-primary"
                    invisible="not can_see_review_buttons or
                        state in ('cancel', 'complete')"/>
                <button
                    name="action_reject_order"
                    string="Decline"
                    type="object" class="btn btn-secondary"
                    invisible="not can_see_review_buttons or
                        state in ('draft', 'sent', 'cancel', 'complete', 'achieved', 'in_progress')"/>
            </xpath> -->
            <!-- <xpath expr="//header" position="inside">
                <button
                    name="action_reject_job"
                    string="Reject Job"
                    type="object" class="btn btn-secondary"
                    invisible="sale_type != 'builder'"/>
            </xpath> -->
            <field name="user_id" position="after">
                <field name="employee_id" readonly="True"/>
                <!-- <field name="builder_hide_pricing" invisible="1"/> -->
            </field>
            <field name="partner_invoice_id" position="before">
                <field name="payment_term_id" position="move"/>
            </field>
            <!-- <field name="partner_shipping_id" position="after"> -->
                <!-- <field name="client_order_ref" invisible="context.get('is_come_from_builder_app') != True"/> -->
                <!-- <field name="user_id" widget="many2one_avatar_user" invisible="context.get('is_come_from_builder_app') != True"/> -->
                <!-- <field name="employee_id" invisible="context.get('is_come_from_builder_app') != True"/> -->
                <!-- <field name="total_price_builder" string="Total price (inc GST)" invisible="context.get('is_come_from_builder_app') != True"/> -->
                <!-- <field name="related_orders_ids" widget="many2many_tags" invisible="sale_type != 'builder'"/> -->
            <!-- </field> -->
            <!-- <field name="state" position="attributes">
                <attribute name="statusbar_visible">draft,sent</attribute>
            </field> -->
            <!-- <button id="action_confirm" position="attributes">
                <attribute name="invisible">1</attribute>
            </button> -->
            <!-- <page name="other_information" position="attributes">
                <attribute name="invisible">sale_type == 'builder'</attribute>
            </page> -->
            <xpath expr="//header//button[@name='action_confirm'][1]" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@name='action_confirm'][2]" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@name='action_quotation_send'][2]" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@name='action_quotation_send'][3]" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@name='button_create_down_payment']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@id='create_invoice']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@id='create_invoice_sub']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@id='create_invoice_percentage']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//header//button[@name='action_unlock']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//header//button[@name='action_lock']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='order_lines']//create[@name='add_product_control']" position="after">
                <create name="add_charge_control" string="Add a charge" context="{'default_is_charge': True}"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='product_template_id']" position="attributes">
                <attribute name="context">{'is_charge': is_charge,'partner_id': parent.partner_id,'quantity': product_uom_qty,'pricelist': parent.pricelist_id,'uom':product_uom,'company_id': parent.company_id,'default_list_price': price_unit,'default_description_sale': name}</attribute>
                <attribute name="domain">[('sale_ok', '=', True), ('is_charge', '=', is_charge)]</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='product_template_id']" position="after">
                <field name="is_charge" column_invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='name']" position="attributes">
                <attribute name="invisible">is_subcontract_service</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='product_packaging_qty']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='qty_invoiced']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='product_packaging_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='route_id']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>
            <!-- <xpath expr="//field[@name='order_line']/list//field[@name='product_uom']" position="before">
                <button class="btn btn-link fa fa-list mt-1" name="action_open_available_stock" column_invisible="parent.state not in ('draft', 'sent')" type="object"/>
            </xpath> -->
            <xpath expr="//field[@name='order_line']/list" position="inside">
                <field name="location_id" optional="hide" readonly="1"/>
                <field name="delivery_method" optional="hide" required="not is_service and not display_type and not is_downpayment" invisible="is_service"/>
            </xpath>
            <xpath expr="//page[@name='other_information']//field[@name='invoice_status']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>

            <!-- move fields in md task 8737811321 -->
            <xpath expr="//field[@name='branch_id']" position="attributes">
                <attribute name="invisible">not is_sales_admin</attribute>
            </xpath>
            <xpath expr="//field[@name='branch_id']" position="after">
                <field name="branch_id" invisible="is_sales_admin" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}" readonly="locked or is_rewrite"/>
                <xpath expr="//field[@name='employee_id']" position="move"/>
            </xpath>
            <xpath expr="//field[@name='delivery_status']" position="after">
                <xpath expr="//form[1]/sheet[1]/group[@name='sale_header']/group[@name='order_details']/field[@name='location_id']" position="move"/>
                <xpath expr="//field[@name='carrier']" position="move"/>
                <xpath expr="//field[@name='transport_note']" position="move"/>
            </xpath>


            <!-- update readonly fields -->
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True</attribute>
            </xpath>
            <xpath expr="//field[@name='payment_term_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='branch_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//button[@name='action_open_discount_wizard']" position="attributes">
                <attribute name="invisible">locked == True or is_rewrite == True</attribute>
            </xpath>
            <!-- <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='order_lines']/div[@name='so_button_below_order_lines']/button[@name='action_open_delivery_wizard']" position="attributes">
                <attribute name="invisible">is_all_service or not order_line or delivery_set or locked == True</attribute>
            </xpath> -->
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='team_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='fiscal_position_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='incoterm']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='incoterm_location']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='commitment_date']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='other_information']/group[1]/group[@name='sale_shipping']/field[@name='location_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <xpath expr="//field[@name='carrier']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">locked == True or is_rewrite == True</attribute>
            </xpath>
            <!-- <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='order_lines']/div[@name='so_button_below_order_lines']/button[@name='action_open_delivery_wizard'][3]" position="attributes">
                <attribute name="invisible">is_all_service or recompute_delivery_price or not delivery_set or locked == True</attribute>
            </xpath> -->
            <xpath expr="//form[1]/sheet[1]/group[@name='sale_header']/group[@name='order_details']/div[not(@name)][3]/field[@name='pricelist_id']" position="attributes">
                <attribute name="force_save">1</attribute>
                <attribute name="readonly">state in ["cancel", "sale"] and subscription_state not in ["3_progress", "4_paused"] and ( not is_rewrite )</attribute>
            </xpath>
 		</field>
	</record>

    <record id="sale_subscription_sale_order_line_form_inherit" model="ir.ui.view">
		<field name="name">sale.subscription.sale.order.line.form.inherit</field>
		<field name="model">sale.order</field>
		<field name="inherit_id" ref="sale_subscription.sale_subscription_order_view_form"/>
		<field name="arch" type="xml">
            <xpath expr="//page[@name='notes']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@name='plan_label']" position="attributes">
                <!-- <attribute name="invisible">not plan_id and state in ['sale', 'cancel'] or context.get('is_come_from_builder_app') == True</attribute> -->
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[@name='plan_block']" position="attributes">
                <!-- <attribute name="invisible">not plan_id and state in ['sale', 'cancel'] or context.get('is_come_from_builder_app') == True</attribute> -->
                <attribute name="invisible">1</attribute>
            </xpath>
 		</field>
	</record>

    <record id="sale_margin_sale_order_line_form_inherit" model="ir.ui.view">
		<field name="name">sale.margin.sale.order.line.form.inherit</field>
		<field name="model">sale.order</field>
		<field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line_form"/>
		<field name="arch" type="xml">
            <!-- <xpath expr="//field[@name='order_line']/list//field[@name='purchase_price']" position="attributes"> -->
                <!-- <attribute name="column_invisible">parent.builder_hide_pricing</attribute> -->
                <!-- <attribute name="readonly">not parent.is_state_before_input</attribute>
            </xpath> -->
            <xpath expr="//field[@name='order_line']/list//field[@name='purchase_price']" position="after">
                <field name="floor_cost" readonly="parent.state not in ('draft', 'sent')" optional="hide"/>
            </xpath>
 		</field>
	</record>

    <record id="view_order_tree_inherit" model="ir.ui.view">
        <field name="name">view.order.tree.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.sale_order_tree"/>
        <field name="arch" type="xml">
            <!-- <field name="invoice_status" position="after">
                <field name="payment_state" optional="hide"
                    decoration-info="payment_state in ('partial','in_payment')"
                    decoration-success="payment_state == 'paid'"
                    decoration-danger="payment_state in ('reversed','not_paid')"
                    widget="badge" />
            </field> -->
            <field name="partner_id" position="after">
                <field name="sale_type" readonly="1" optional="show" widget="badge" decoration-info="sale_type == 'retail'" decoration-primary="sale_type == 'inter-com'"/>
            </field>
            <field name="date_order" position="after">
            </field>
            <!-- <field name="amount_total" position="after">
                <field name="margin_string" string="Margin" optional="show" column_invisible="context.get('is_come_from_builder_app') != True" groups="account.group_account_manager"/>
                <field name="margin_floor_string" string="Floor Margin" optional="show" column_invisible="context.get('is_come_from_builder_app') != True"/>
            </field>
            <field name="client_order_ref" position="attributes">
                <attribute name="column_invisible">context.get('is_come_from_builder_app') == True</attribute>
            </field> -->
            <!-- <field name="partner_id" position="after">
                <field name="client_order_ref" column_invisible="context.get('is_come_from_builder_app') != True" optional="hide"/>
                <field name="contact_address_complete" column_invisible="context.get('is_come_from_builder_app') != True" optional="show"/>
            </field> -->
            <field name="user_id" position="before">
                <field name="create_uid" optional="show"/>
                <field name="employee_id" optional="show"/>
            </field>
            <field name="amount_total" position="after">
                <field name="remaining_amount" optional="show"/>
            </field>
            <field name="state" position="after">
                <field name="delivery_status" optional="show"/>
            </field>
        </field>
    </record>

    <record id="view_sales_order_filter_inherit" model="ir.ui.view">
        <field name="name">sale.order.view.search.inherit.sale.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_sales_order_filter"/>
        <field name="arch" type="xml">
            <filter name="salesperson" position="replace">
                <filter string="Employee" name="employee" domain="[]" context="{'group_by': 'employee_id'}"/>
            </filter>
            <group position="inside">
                <filter
                    string="Sale Type"
                    name="group_by_sale_type"
                    domain="[]"
                    context="{'group_by': 'sale_type'}"/>
            </group>
        </field>
    </record>
    <record id="view_sales_order_service_note_form" model="ir.ui.view">
        <field name="name">view.sales.order.service.note.form</field>
        <field name="model">sale.order.line</field>
        <field name="priority">10000</field>
        <field name="arch" type="xml">
            <form string="Service Note">
				<sheet string="Service Note">
                    <field name="name" />
                    <group>
                        <field name="service_detail_doc" widget="image"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Margin Floor -->
    <record model="ir.ui.view" id="sale_margin_floor_sale_order">
        <field name="name">sale.order.margin.floor.view.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="priority" eval="15"/>
        <field name="arch" type="xml">
            <!-- <xpath expr="//field[@name='amount_total']" position="after">
                <div class="d-flex float-end" groups="base.group_user">
                    <button class="btn" name="get_information" type="object" icon="fa-info-circle"/>
                </div>
            </xpath> -->
           <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="after">
                <field name="margin_floor" optional="hide" groups="base.group_user"/>
                <field name="margin_floor_percent"
                    groups="base.group_user"
                    invisible="price_subtotal == 0"
                    optional="hide" widget="percentage"/>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_margin_group_account_sale_order">
        <field name="name">sale.order.margin.account.view.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_margin.sale_margin_sale_order"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='sale_total']/div" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </xpath>
            <xpath expr="//field[@name='margin']/parent::div/parent::div" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_margin_group_account_sale_order_line">
        <field name="name">sale.order.line.margin.account.view.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line"/>
        <field name="arch" type="xml">
            <field name="purchase_price" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_margin_group_account_sale_order_line_form">
        <field name="name">sale.order.line.margin.account.view.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line_form"/>
        <field name="arch" type="xml">
            <field name="purchase_price" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </field>
            <field name="margin" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </field>
            <field name="margin_percent" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_margin_floor_sale_order_pivot">
        <field name="name">sale.order.margin.view.pivot</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_pivot"/>
        <field name="arch" type="xml">
            <pivot position="inside">
                <field name="margin_floor_percent" invisible="1"/>
            </pivot>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_margin_floor_sale_order_graph">
        <field name="name">sale.order.margin.view.graph</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_graph"/>
        <field name="arch" type="xml">
            <graph position="inside">
                <field name="margin_floor_percent" invisible="1"/>
            </graph>
        </field>
    </record>

    <record id="sale_subscription.model_sale_order_subscription_change_customer" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="sale_subscription.model_sale_order_subscription_pause_record" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="sale.model_sale_order_action_share" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="sale.action_mass_cancel_orders" model="ir.actions.act_window">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="sale.action_view_sale_advance_payment_inv" model="ir.actions.act_window">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
</odoo>
