# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import binascii
import json
import re

from odoo import _, fields, http
from odoo.addons.account.controllers.portal import PortalAccount
from odoo.addons.payment.controllers import portal as payment_portal
from odoo.addons.sale.controllers.portal import CustomerPortal
from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo.exceptions import AccessError, MissingError, UserError, ValidationError
from odoo.fields import Command
from odoo.http import content_disposition, request, route
from odoo.tools import SQL
from psycopg2.errors import LockNotAvailable


class ModulaSaleWebsiteSale(WebsiteSale):
    def _get_shop_payment_values(self, order, **kwargs):
        payment_values = super()._get_shop_payment_values(order, **kwargs)

        payment_form_values = {
            **CustomerRetailPortal._get_payment_values(
                self, order, website_id=request.website.id
            ),
            "display_submit_button": False,  # The submit button is re-added outside the form.
            "transaction_route": f"/shop/payment/transaction/{order.id}",
            "landing_route": "/shop/payment/validate",
            "sale_order_id": order.id,  # Allow Stripe to check if tokenization is required.
        }
        if "website_sale_order" in payment_values:
            if "providers_sudo" in payment_values:
                payment_values["providers_sudo"] = payment_values[
                    "providers_sudo"
                ].filtered(lambda pm: pm.code == "worldline")
            if "payment_methods_sudo" in payment_values:
                payment_values["payment_methods_sudo"] = payment_values[
                    "payment_methods_sudo"
                ].filtered(
                    lambda pm: pm.provider_ids in payment_values["providers_sudo"]
                )

            if "providers_sudo" in payment_form_values:
                payment_form_values["providers_sudo"] = payment_form_values[
                    "providers_sudo"
                ].filtered(lambda pm: pm.code == "worldline")
            if "payment_methods_sudo" in payment_values:
                payment_form_values["payment_methods_sudo"] = payment_form_values[
                    "payment_methods_sudo"
                ].filtered(
                    lambda pm: pm.provider_ids in payment_form_values["providers_sudo"]
                )
        # payment_values['payment_methods_finance_sudo'] = payment_values['payment_methods_sudo'].filtered(lambda pm: pm.code == "modula_payment_finance")
        # payment_values['payment_methods_eftpos_sudo'] = payment_values['payment_methods_sudo'].filtered(lambda pm: pm.code == "modula_payment_eftpos")
        # payment_values['unique_receipt_sequence'] = ''
        # partner_sudo = order.partner_id
        # company = order.company_id
        # currency = order.currency_id

        return payment_values | payment_form_values

    def _parse_form_data_checklist(self, post):
        values = []
        # values to add field one2many checklist_line_ids
        for key, value in post.items():
            if "_" in key:
                checklist_id, answer_id = key.split("_")
                values.append(
                    {
                        "question_id": int(checklist_id),
                        "answer_id": int(answer_id),
                        "value": value,
                    }
                )
            else:
                answer_id = (
                    request.env["survey.question.answer"]
                    .sudo()
                    .search(
                        [
                            ("is_checklist", "=", True),
                            ("value", "=", value),
                            ("question_id", "=", int(key)),
                        ],
                        limit=1,
                    )
                )
                values.append(
                    {
                        "question_id": int(key),
                        "answer_id": answer_id and answer_id.id or False,
                        "value": value,
                    }
                )
        return values

    def _get_checklist_line_ids(self, order):
        checklist_line_ids = order.checklist_line_ids.filtered(
            lambda x: x.question_id.is_checklist_ecommerce
        )
        checklist_lines = []
        for checklist in checklist_line_ids:
            trigger_answers = [
                {
                    "id": answer.id,
                    "question_id": answer.question_id.id,
                    "value": answer.value,
                }
                for answer in checklist.question_id.triggering_answer_checklist_ids
            ]
            invisible = "0"
            if trigger_answers:
                for trigger in trigger_answers:
                    question_line = checklist_line_ids.filtered(
                        lambda x: x.question_id.id == trigger["question_id"]
                        and x.value == trigger["value"]
                    )
                    if question_line:
                        invisible = "0"
                        break
                    else:
                        invisible = "1"

            checklist_lines.append(
                {
                    "id": checklist.question_id.id,
                    "title": checklist.question_id.title,
                    "name": checklist.question_id.name,
                    "required": checklist.question_id.constr_mandatory,
                    "type_checklist": checklist.question_id.question_type_checklist,
                    "value": checklist.value,
                    "suggested_answers": [
                        {
                            "answer_id": answer.id,
                            "question_id": answer.question_id.id,
                            "value": answer.value,
                            "color": answer.color,
                        }
                        for answer in checklist.question_id.suggested_answer_ids
                    ],
                    "trigger_answers": json.dumps(trigger_answers),
                    "invisible": invisible,
                }
            )
        return checklist_lines

    @route(["/shop/checklist"], type="http", auth="public", website=True, sitemap=False)
    def checklist(self, **post):
        # Check that this option is activated
        # extra_step = request.website.viewref('modula_sale')
        # if not extra_step.active:
        #     return request.redirect("/shop/checkout")

        # check that cart is valid
        order = request.website.sale_get_order()
        # redirection = self._check_cart(order)
        # open_editor = request.params.get('open_editor') == 'true'
        # # Do not redirect if it is to edit
        # # (the information is transmitted via the "open_editor" parameter in the url)
        # if not open_editor and redirection:
        #     return redirection

        order.load_template()
        checklist_lines = self._get_checklist_line_ids(order)
        # checklist_line_ids = order.checklist_line_ids.filtered(
        #     lambda x: x.question_id.is_checklist_ecommerce
        # )

        if not checklist_lines or (
            order.carrier_id and order.carrier_id.fixed_price == 0
        ):
            return request.redirect("/shop/checkout")

        values = {
            "website_sale_order": order,
            "checklist_lines": checklist_lines,
            "post": post,
            "escape": lambda x: x.replace("'", r"\'"),
            "partner": order.partner_id.id,
            "order": order,
        }
        values.update(
            {
                "date": fields.Date.today(),
                "suggested_products": [],
            }
        )
        if order:
            order.order_line.filtered(
                lambda sol: sol.product_id and not sol.product_id.active
            ).unlink()
            values["suggested_products"] = order._cart_accessories()
            values.update(self._get_express_shop_payment_values(order))

        values.update(self._cart_values(**post))
        return request.render("modula_sale.checklist", values)

    @route(
        ["/shop/checklist/submit"],
        type="http",
        methods=["POST"],
        auth="public",
        website=True,
        sitemap=False,
    )
    def checklist_submit(self, **post):
        order = request.website.sale_get_order()
        if redirection := self._check_cart(order):
            return json.dumps({"redirectUrl": redirection.location})

        checklist_values = self._parse_form_data_checklist(post)

        try:
            for val in checklist_values:
                line = order.checklist_line_ids.filtered(
                    lambda x: x.question_id.id == val["question_id"]
                )
                if line:
                    line.sudo().write(val)
                # else:
                #     order.checklist_line_ids.create(val)
        except Exception as e:
            print(e)
            return json.dumps({"error": str(e)})

        return json.dumps({"redirectUrl": "/shop/confirm_order"})

    def _complete_address_values(
        self, address_values, address_type, use_same, order_sudo
    ):
        """Override. Complete the address for EDI in the B2C case (CPF identification)."""
        super()._complete_address_values(
            address_values, address_type, use_same, order_sudo
        )
        retail_type = request.env.ref(
            "modula_contact_extended.res_partner_type_dom", raise_if_not_found=False
        )
        address_values.update(
            {
                "customer": True,
                "customer_type_id": retail_type.id,
            }
        )


class CustomerPortalRetail(CustomerPortal):
    def _show_report(
        self, model, report_type, report_ref, download=False, proforma_invoice=False
    ):
        if report_type not in ("html", "pdf", "text"):
            raise UserError(_("Invalid report type: %s", report_type))

        ReportAction = request.env["ir.actions.report"].sudo()

        if hasattr(model, "company_id"):
            if len(model.company_id) > 1:
                raise UserError(_("Multi company reports are not supported."))
            ReportAction = ReportAction.with_company(model.company_id)

        method_name = "_render_qweb_%s" % (report_type)
        report = getattr(ReportAction, method_name)(
            report_ref, list(model.ids), data={"report_type": report_type}
        )[0]
        headers = self._get_http_headers(
            model, report_type, report, download, proforma_invoice=proforma_invoice
        )
        return request.make_response(report, headers=list(headers.items()))

    def _get_http_headers(
        self, model, report_type, report, download, proforma_invoice=False
    ):
        headers = {
            "Content-Type": "application/pdf" if report_type == "pdf" else "text/html",
            "Content-Length": len(report),
        }
        if report_type == "pdf" and download:
            filename = "%s.pdf" % (
                re.sub("\W+", "-", model._get_report_base_filename())
            )
            if proforma_invoice:
                filename = "%s.pdf" % (
                    re.sub("\W+", "-", model._get_proforma_invoice_name())
                )
            headers["Content-Disposition"] = content_disposition(filename)
        return headers

    @http.route(["/my/orders/<int:order_id>"], type="http", auth="public", website=True)
    def portal_order_page(
        self,
        order_id,
        report_type=None,
        access_token=None,
        message=False,
        download=False,
        downpayment=None,
        **kw,
    ):
        try:
            order_sudo = self._document_check_access(
                "sale.order", order_id, access_token=access_token
            )
        except (AccessError, MissingError):
            return request.redirect("/my")

        if downpayment and "prepaymentPercent" in kw:
            order_sudo.prepayment_percent = float(kw.get("prepaymentPercent"))
        if downpayment and "prepaymentAmount" in kw:
            order_sudo.prepayment_amount = float(kw.get("prepaymentAmount"))

        if order_sudo.is_retail_payment:
            if report_type in ("html", "pdf", "text"):
                return self._show_report(
                    model=order_sudo,
                    report_type=report_type,
                    report_ref="modula_sale.saleorder_invoices_retails_sale",
                    download=download,
                    proforma_invoice=True,
                )
            request.update_context(is_retails=True)

            if request.env.user.share and access_token:
                # If a public/portal user accesses the order with the access token
                # Log a note on the chatter.
                today = fields.Date.today().isoformat()
                session_obj_date = request.session.get("view_quote_%s" % order_sudo.id)
                if session_obj_date != today:
                    # store the date as a string in the session to allow serialization
                    request.session["view_quote_%s" % order_sudo.id] = today
                    # The "Quotation viewed by customer" log note is an information
                    # dedicated to the salesman and shouldn't be translated in the customer/website lgg
                    context = {
                        "lang": order_sudo.user_id.partner_id.lang
                        or order_sudo.company_id.partner_id.lang
                    }
                    author = (
                        order_sudo.partner_id
                        if request.env.user._is_public()
                        else request.env.user.partner_id
                    )
                    msg = _("Quotation viewed by customer %s", author.name)
                    del context
                    order_sudo.message_post(
                        author_id=author.id,
                        body=msg,
                        message_type="notification",
                        subtype_xmlid="sale.mt_order_viewed",
                    )

            backend_url = (
                f"/web#model={order_sudo._name}"
                f"&id={order_sudo.id}"
                f"&action={order_sudo._get_portal_return_retail_action().id}"
                f"&view_type=form"
            )

            values = {
                "sale_order": order_sudo,
                "product_documents": order_sudo._get_product_documents(),
                "message": message,
                "report_type": "html",
                "backend_url": backend_url,
                "res_company": order_sudo.company_id,  # Used to display correct company logo
            }
            if order_sudo._has_to_be_paid():
                values.update(
                    self._get_payment_values(
                        order_sudo,
                        downpayment=downpayment == "true"
                        if downpayment is not None
                        else order_sudo.prepayment_percent < 1.0,
                    )
                )

            if order_sudo.state in ("draft", "sent", "cancel"):
                history_session_key = "my_quotations_history"
            else:
                history_session_key = "my_orders_history"

            values = self._get_page_view_values(
                order_sudo, access_token, values, history_session_key, False
            )

            return request.render(
                "modula_sale.sale_order_retail_portal_template", values
            )
        else:
            return super().portal_order_page(
                order_id=order_id,
                report_type=report_type,
                access_token=access_token,
                message=message,
                download=download,
                downpayment=downpayment,
                **kw,
            )

    def _get_payment_values(self, order_sudo, downpayment=False, **kwargs):
        """Return the payment-specific QWeb context values.

        :param sale.order order_sudo: The sales order being paid.
        :param bool downpayment: Whether the current payment is a downpayment.
        :param dict kwargs: Locally unused data passed to `_get_compatible_providers` and
                            `_get_available_tokens`.
        :return: The payment-specific values.
        :rtype: dict
        """
        logged_in = not request.env.user._is_public()
        partner_sudo = (
            request.env.user.partner_id if logged_in else order_sudo.partner_id
        )
        # partner_sudo = order_sudo.partner_id
        company = order_sudo.company_id
        if downpayment:
            amount = order_sudo._get_prepayment_required_amount()
        else:
            amount = order_sudo.amount_total - order_sudo.amount_paid
        currency = order_sudo.currency_id

        # Select all the payment methods and tokens that match the payment context.
        providers_sudo = (
            request.env["payment.provider"]
            .sudo()
            ._get_compatible_providers(
                company.id,
                partner_sudo.id,
                amount,
                currency_id=currency.id,
                sale_order_id=order_sudo.id,
                **kwargs,
            )
        )  # In sudo mode to read the fields of providers and partner (if logged out).
        payment_methods_sudo = (
            request.env["payment.method"]
            .sudo()
            ._get_compatible_payment_methods(
                providers_sudo.ids,
                partner_sudo.id,
                currency_id=currency.id,
                sale_order_id=order_sudo.id,
                **kwargs,
            )
        )  # In sudo mode to read the fields of providers.
        payment_methods_finance_sudo = False
        payment_methods_eftpos_sudo = False
        provider_finance = providers_sudo.filtered(lambda pm: pm.code == "finance")
        if provider_finance and payment_methods_sudo:
            payment_methods_finance_sudo = payment_methods_sudo.filtered(
                lambda pm: pm.code != "finance" and provider_finance in pm.provider_ids
            )
            payment_methods_eftpos_sudo = payment_methods_sudo.filtered(
                lambda pm: "eftpos" in pm.code
            )
            payment_methods_sudo = payment_methods_sudo.filtered(
                lambda pm: (
                    pm.code == "finance" or provider_finance not in pm.provider_ids
                )
                and pm.code not in ["eftpos_visa_mastercard", "eftpos_amex"]
            )
        customer_account = providers_sudo.filtered(
            lambda p: p.code == "customer_account"
        )
        if partner_sudo and customer_account and customer_account.split_transactions:
            payment_term = order_sudo.payment_term_id
            pt = request.env.ref("account.account_payment_term_immediate", False)
            if payment_term and payment_term != pt:
                payment_methods_finance_sudo = False
                payment_methods_eftpos_sudo = False
                providers_sudo = customer_account
                payment_methods_sudo = payment_methods_sudo.filtered(
                    lambda pm: pm.code == "customer_account"
                )
            else:
                providers_sudo = providers_sudo.filtered(
                    lambda p: p.code != "customer_account"
                )
                payment_methods_sudo = payment_methods_sudo.filtered(
                    lambda pm: pm.code != "customer_account"
                )

        tokens_sudo = (
            request.env["payment.token"]
            .sudo()
            ._get_available_tokens(providers_sudo.ids, partner_sudo.id, **kwargs)
        )  # In sudo mode to read the partner's tokens (if logged out) and provider fields.

        # Make sure that the partner's company matches the invoice's company.
        company_mismatch = not payment_portal.PaymentPortal._can_partner_pay_in_company(
            partner_sudo, company
        )

        portal_page_values = {
            "company_mismatch": company_mismatch,
            "expected_company": company,
        }
        payment_form_values = {
            "show_tokenize_input_mapping": payment_portal.PaymentPortal._compute_show_tokenize_input_mapping(
                providers_sudo, sale_order_id=order_sudo.id
            ),
        }
        # concatenate the sales order number with a unique receipt sequence
        sequence = (
            request.env["ir.sequence"].sudo().next_by_code("payment.sale.sequence")
            or "00001"
        )
        unique_receipt_sequence = f"{order_sudo.name}-{sequence}"

        payment_context = {
            "amount": amount,
            "currency": currency,
            "partner_id": partner_sudo.id,
            "providers_sudo": providers_sudo,
            "payment_methods_sudo": payment_methods_sudo,
            "payment_methods_finance_sudo": payment_methods_finance_sudo,
            "payment_methods_eftpos_sudo": payment_methods_eftpos_sudo,
            "tokens_sudo": tokens_sudo,
            "transaction_route": order_sudo.get_portal_url(suffix="/transaction"),
            "landing_route": order_sudo.get_portal_url(),
            "access_token": order_sudo._portal_ensure_token(),
            "unique_receipt_sequence": unique_receipt_sequence,
        }
        return {
            **portal_page_values,
            **payment_form_values,
            **payment_context,
            **self._get_extra_payment_form_values(**kwargs),
        }

    @http.route(
        ["/my/orders/<int:order_id>/accept"], type="json", auth="public", website=True
    )
    def portal_quote_accept(
        self, order_id, access_token=None, name=None, signature=None
    ):
        # get from query string if not on json param
        access_token = access_token or request.httprequest.args.get("access_token")
        try:
            order_sudo = self._document_check_access(
                "sale.order", order_id, access_token=access_token
            )
        except (AccessError, MissingError):
            return {"error": _("Invalid order.")}

        if not order_sudo._has_to_be_signed():
            return {
                "error": _("The order is not in a state requiring customer signature.")
            }
        if not signature:
            return {"error": _("Signature is missing.")}

        try:
            order_sudo.write(
                {
                    "signed_by": name,
                    "signed_on": fields.Datetime.now(),
                    "signature": signature,
                }
            )
            if order_sudo.is_rewrite:
                order_sudo.write(
                    {
                        "is_rewrite": False,
                    }
                )
            request.env.cr.commit()
        except (TypeError, binascii.Error) as e:
            return {"error": _("Invalid signature data.")}

        if not order_sudo._has_to_be_paid():
            order_sudo._validate_order()

        pdf = (
            request.env["ir.actions.report"]
            .sudo()
            ._render_qweb_pdf("sale.action_report_saleorder", [order_sudo.id])[0]
        )

        order_sudo.message_post(
            attachments=[("%s.pdf" % order_sudo.name, pdf)],
            author_id=(
                order_sudo.partner_id.id
                if request.env.user._is_public()
                else request.env.user.partner_id.id
            ),
            body=_("Order signed by %s", name),
            message_type="comment",
            subtype_xmlid="mail.mt_comment",
        )

        query_string = "&message=sign_ok"
        if order_sudo._has_to_be_paid():
            query_string += "&allow_payment=yes"
        return {
            "force_refresh": True,
            "redirect_url": order_sudo.get_portal_url(query_string=query_string),
        }


class PortalAccountRetailsSale(PortalAccount):
    def _show_report(
        self, model, report_type, report_ref, download=False, proforma_invoice=False
    ):
        if report_type not in ("html", "pdf", "text"):
            raise UserError(_("Invalid report type: %s", report_type))

        ReportAction = request.env["ir.actions.report"].sudo()

        if hasattr(model, "company_id"):
            if len(model.company_id) > 1:
                raise UserError(_("Multi company reports are not supported."))
            ReportAction = ReportAction.with_company(model.company_id)

        method_name = "_render_qweb_%s" % (report_type)
        report = getattr(ReportAction, method_name)(
            report_ref, list(model.ids), data={"report_type": report_type}
        )[0]
        headers = self._get_http_headers(
            model, report_type, report, download, proforma_invoice=proforma_invoice
        )
        return request.make_response(report, headers=list(headers.items()))

    def _get_http_headers(
        self, model, report_type, report, download, proforma_invoice=False
    ):
        headers = {
            "Content-Type": "application/pdf" if report_type == "pdf" else "text/html",
            "Content-Length": len(report),
        }
        if report_type == "pdf" and download:
            filename = "%s.pdf" % (
                re.sub("\W+", "-", model._get_report_base_filename())
            )
            if proforma_invoice:
                filename = "%s.pdf" % (
                    re.sub("\W+", "-", model._get_proforma_invoice_name())
                )
            headers["Content-Disposition"] = content_disposition(filename)
        return headers

    @http.route(
        ["/my/orders/<int:order_id>/retails"], type="http", auth="public", website=True
    )
    def portal_order_retails_page(
        self,
        order_id,
        report_type=None,
        access_token=None,
        message=False,
        download=False,
        downpayment=None,
        **kw,
    ):
        try:
            order_sudo = self._document_check_access(
                "sale.order", order_id, access_token=access_token
            )
        except (AccessError, MissingError):
            return request.redirect("/my")
        if downpayment and "prepaymentPercent" in kw:
            order_sudo.prepayment_percent = float(kw.get("prepaymentPercent"))
        if downpayment and "prepaymentAmount" in kw:
            order_sudo.prepayment_amount = float(kw.get("prepaymentAmount"))
        if report_type in ("html", "pdf", "text"):
            return self._show_report(
                model=order_sudo,
                report_type=report_type,
                report_ref="modula_sale.saleorder_invoices_retails_sale",
                download=download,
                proforma_invoice=True,
            )
        request.update_context(is_retails=True)

        if request.env.user.share and access_token:
            # If a public/portal user accesses the order with the access token
            # Log a note on the chatter.
            today = fields.Date.today().isoformat()
            session_obj_date = request.session.get("view_quote_%s" % order_sudo.id)
            if session_obj_date != today:
                # store the date as a string in the session to allow serialization
                request.session["view_quote_%s" % order_sudo.id] = today
                # The "Quotation viewed by customer" log note is an information
                # dedicated to the salesman and shouldn't be translated in the customer/website lgg
                context = {
                    "lang": order_sudo.user_id.partner_id.lang
                    or order_sudo.company_id.partner_id.lang
                }
                author = (
                    order_sudo.partner_id
                    if request.env.user._is_public()
                    else request.env.user.partner_id
                )
                msg = _("Quotation viewed by customer %s", author.name)
                del context
                order_sudo.message_post(
                    author_id=author.id,
                    body=msg,
                    message_type="notification",
                    subtype_xmlid="sale.mt_order_viewed",
                )

        backend_url = (
            f"/web#model={order_sudo._name}"
            f"&id={order_sudo.id}"
            f"&action={order_sudo._get_portal_return_retail_action().id}"
            f"&view_type=form"
        )

        values = {
            "sale_order": order_sudo,
            "product_documents": order_sudo._get_product_documents(),
            "message": message,
            "report_type": "html",
            "backend_url": backend_url,
            "res_company": order_sudo.company_id,  # Used to display correct company logo
        }
        if order_sudo._has_to_be_paid():
            values.update(
                self._get_payment_values(
                    order_sudo,
                    downpayment=downpayment == "true"
                    if downpayment is not None
                    else order_sudo.prepayment_percent < 1.0,
                )
            )

        if order_sudo.state in ("draft", "sent", "cancel"):
            history_session_key = "my_quotations_history"
        else:
            history_session_key = "my_orders_history"

        values = self._get_page_view_values(
            order_sudo, access_token, values, history_session_key, False
        )

        return request.render("modula_sale.sale_order_retail_portal_template", values)

    def _get_payment_values(self, order_sudo, downpayment=False, **kwargs):
        """Return the payment-specific QWeb context values.

        :param sale.order order_sudo: The sales order being paid.
        :param bool downpayment: Whether the current payment is a downpayment.
        :param dict kwargs: Locally unused data passed to `_get_compatible_providers` and
                            `_get_available_tokens`.
        :return: The payment-specific values.
        :rtype: dict
        """
        partner_sudo = order_sudo.partner_id
        company = order_sudo.company_id
        if downpayment:
            amount = order_sudo._get_prepayment_required_amount()
        else:
            amount = order_sudo.amount_total - order_sudo.amount_paid
        currency = order_sudo.currency_id

        # Select all the payment methods and tokens that match the payment context.
        providers_sudo = (
            request.env["payment.provider"]
            .sudo()
            ._get_compatible_providers(
                company.id,
                partner_sudo.id,
                amount,
                currency_id=currency.id,
                sale_order_id=order_sudo.id,
                **kwargs,
            )
        )  # In sudo mode to read the fields of providers and partner (if logged out).
        payment_methods_sudo = (
            request.env["payment.method"]
            .sudo()
            ._get_compatible_payment_methods(
                providers_sudo.ids,
                partner_sudo.id,
                currency_id=currency.id,
                sale_order_id=order_sudo.id,
                **kwargs,
            )
        )  # In sudo mode to read the fields of providers.
        payment_methods_finance_sudo = False
        payment_methods_eftpos_sudo = False
        provider_finance = providers_sudo.filtered(lambda pm: pm.code == "finance")
        if provider_finance and payment_methods_sudo:
            payment_methods_finance_sudo = payment_methods_sudo.filtered(
                lambda pm: pm.code != "finance" and provider_finance in pm.provider_ids
            )
            payment_methods_eftpos_sudo = payment_methods_sudo.filtered(
                lambda pm: "eftpos" in pm.code
            )
            payment_methods_sudo = payment_methods_sudo.filtered(
                lambda pm: (
                    pm.code == "finance" or provider_finance not in pm.provider_ids
                )
                and pm.code not in ["eftpos_visa_mastercard", "eftpos_amex"]
            )
        customer_account = providers_sudo.filtered(
            lambda p: p.code == "customer_account"
        )
        if partner_sudo and customer_account and customer_account.split_transactions:
            payment_term = order_sudo.payment_term_id
            pt = request.env.ref("account.account_payment_term_immediate", False)
            if payment_term and payment_term != pt:
                payment_methods_finance_sudo = False
                payment_methods_eftpos_sudo = False
                providers_sudo = customer_account
                payment_methods_sudo = payment_methods_sudo.filtered(
                    lambda pm: pm.code == "customer_account"
                )
            else:
                providers_sudo = providers_sudo.filtered(
                    lambda p: p.code != "customer_account"
                )
                payment_methods_sudo = payment_methods_sudo.filtered(
                    lambda pm: pm.code != "customer_account"
                )

        tokens_sudo = (
            request.env["payment.token"]
            .sudo()
            ._get_available_tokens(providers_sudo.ids, partner_sudo.id, **kwargs)
        )  # In sudo mode to read the partner's tokens (if logged out) and provider fields.

        # Make sure that the partner's company matches the invoice's company.
        company_mismatch = not payment_portal.PaymentPortal._can_partner_pay_in_company(
            partner_sudo, company
        )

        portal_page_values = {
            "company_mismatch": company_mismatch,
            "expected_company": company,
        }
        payment_form_values = {
            "show_tokenize_input_mapping": CustomerRetailPortal._compute_show_tokenize_input_mapping(
                providers_sudo, sale_order_id=order_sudo.id
            ),
        }
        # concatenate the sales order number with a unique receipt sequence
        sequence = (
            request.env["ir.sequence"].next_by_code("payment.sale.sequence") or "00001"
        )
        unique_receipt_sequence = f"{order_sudo.name}-{sequence}"

        payment_context = {
            "amount": amount,
            "currency": currency,
            "partner_id": partner_sudo.id,
            "providers_sudo": providers_sudo,
            "payment_methods_sudo": payment_methods_sudo,
            "payment_methods_finance_sudo": payment_methods_finance_sudo,
            "payment_methods_eftpos_sudo": payment_methods_eftpos_sudo,
            "tokens_sudo": tokens_sudo,
            "transaction_route": order_sudo.get_portal_url(suffix="/transaction"),
            "landing_route": order_sudo.get_portal_url(),
            "access_token": order_sudo._portal_ensure_token(),
            "unique_receipt_sequence": unique_receipt_sequence,
        }
        return {
            **portal_page_values,
            **payment_form_values,
            **payment_context,
            **self._get_extra_payment_form_values(**kwargs),
        }


class CustomerRetailPortal(payment_portal.PaymentPortal):
    def _get_payment_values(self, order_sudo, downpayment=False, **kwargs):

        values = super(CustomerRetailPortal, self)._get_payment_values(
            order_sudo, downpayment=downpayment, **kwargs
        )
        providers_sudo = values.get("providers_sudo")
        logged_in = not request.env.user._is_public()
        partner_sudo = (
            request.env.user.partner_id if logged_in else order_sudo.partner_id
        )
        company = order_sudo.company_id
        if downpayment:
            amount = order_sudo._get_prepayment_required_amount()
        else:
            amount = order_sudo.amount_total - order_sudo.amount_paid
        currency = order_sudo.currency_id
        availability_report = {}

        payment_methods_sudo = (
            request.env["payment.method"]
            .sudo()
            ._get_compatible_payment_methods(
                providers_sudo.ids,
                partner_sudo.id,
                currency_id=currency.id,
                sale_order_id=order_sudo.id,
                report=availability_report,
                **kwargs,
            )
        )

        if "payment_methods_sudo" in values:
            # payment_methods_sudo = values.get("payment_methods_sudo")
            providers_sudo = values.get("providers_sudo")
            payment_methods_finance_sudo = False
            payment_methods_eftpos_sudo = False
            provider_finance = providers_sudo.filtered(lambda pm: pm.code == "finance")
            if provider_finance and payment_methods_sudo:
                payment_methods_finance_sudo = payment_methods_sudo.filtered(
                    lambda pm: pm.code != "finance"
                    and provider_finance in pm.provider_ids
                )
                payment_methods_eftpos_sudo = payment_methods_sudo.filtered(
                    lambda pm: "eftpos" in pm.code
                )
                payment_methods_sudo = payment_methods_sudo.filtered(
                    lambda pm: (
                        pm.code == "finance" or provider_finance not in pm.provider_ids
                    )
                    and pm.code not in ["eftpos_visa_mastercard", "eftpos_amex"]
                )

            customer_account = providers_sudo.filtered(
                lambda p: p.code == "customer_account"
            )
            if order_sudo and customer_account and customer_account.split_transactions:
                payment_term = order_sudo.payment_term_id
                pt = request.env.ref("account.account_payment_term_immediate", False)
                if payment_term and payment_term != pt:
                    providers_sudo = customer_account
                    payment_methods_eftpos_sudo = False
                    payment_methods_finance_sudo = False
                    payment_methods_sudo = payment_methods_sudo.filtered(
                        lambda pm: pm.code == "customer_account"
                    )
                else:
                    providers_sudo = providers_sudo.filtered(
                        lambda p: p.code != "customer_account"
                    )
                    payment_methods_sudo = payment_methods_sudo.filtered(
                        lambda pm: pm.code != "customer_account"
                    )
            # concatenate the sales order number with a unique receipt sequence
            sequence = (
                request.env["ir.sequence"].sudo().next_by_code("payment.sale.sequence")
                or "00001"
            )
            unique_receipt_sequence = f"{order_sudo.name}-{sequence}"

            values.update(
                {
                    "payment_methods_finance_sudo": payment_methods_finance_sudo,
                    "payment_methods_eftpos_sudo": payment_methods_eftpos_sudo,
                    "payment_methods_sudo": payment_methods_sudo,
                    "unique_receipt_sequence": unique_receipt_sequence,
                }
            )
        return values

    @route(
        "/shop/payment/transaction/<int:order_id>",
        type="json",
        auth="public",
        website=True,
    )
    def shop_payment_transaction(self, order_id, access_token, **kwargs):
        """Create a draft transaction and return its processing values.

        :param int order_id: The sales order to pay, as a `sale.order` id
        :param str access_token: The access token used to authenticate the request
        :param dict kwargs: Locally unused data passed to `_create_transaction`
        :return: The mandatory values for the processing of the transaction
        :rtype: dict
        :raise: UserError if the order has already been paid or has an ongoing transaction
        :raise: ValidationError if the invoice id or the access token is invalid
        """
        # Check the order id and the access token
        # Then lock it during the transaction to prevent concurrent payments
        try:
            order_sudo = self._document_check_access(
                "sale.order", order_id, access_token
            )
            request.env.cr.execute(
                SQL(
                    "SELECT 1 FROM sale_order WHERE id = %s FOR NO KEY UPDATE NOWAIT",
                    order_id,
                )
            )
        except MissingError:
            raise
        except AccessError as e:
            raise ValidationError(_("The access token is invalid.")) from e
        except LockNotAvailable:
            raise UserError(_("Payment is already being processed."))

        if order_sudo.state == "cancel":
            raise ValidationError(_("The order has been cancelled."))

        order_sudo._check_cart_is_ready_to_be_paid()

        self._validate_transaction_kwargs(kwargs)
        kwargs.update(
            {
                "partner_id": order_sudo.partner_invoice_id.id,
                "currency_id": order_sudo.currency_id.id,
                "sale_order_id": order_id,  # Include the SO to allow Subscriptions to tokenize the tx
            }
        )
        if not kwargs.get("amount"):
            kwargs["amount"] = order_sudo.amount_total
        else:
            kwargs["amount"] = float(kwargs["amount"])

        compare_amounts = order_sudo.currency_id.compare_amounts
        if compare_amounts(kwargs["amount"], order_sudo.amount_total):
            raise ValidationError(
                _("The cart has been updated. Please refresh the page.")
            )
        if compare_amounts(order_sudo.amount_paid, order_sudo.amount_total) == 0:
            raise UserError(
                _("The cart has already been paid. Please refresh the page.")
            )
        provider_id = kwargs.get("provider_id")
        if provider_id and "payment_method_id" not in kwargs:
            provider_sudo = request.env["payment.provider"].sudo().browse(provider_id)
            if provider_sudo.code == "worldline":
                kwargs["payment_method_id"] = provider_sudo.payment_method_ids[0].id

        tx_sudo = self._create_transaction(
            custom_create_values={"sale_order_ids": [Command.set([order_id])]},
            **kwargs,
        )

        # Store the new transaction into the transaction list and if there's an old one, we remove
        # it until the day the ecommerce supports multiple orders at the same time.
        request.session["__website_sale_last_tx_id"] = tx_sudo.id

        self._validate_transaction_for_order(tx_sudo, order_sudo)

        return tx_sudo._get_processing_values()
