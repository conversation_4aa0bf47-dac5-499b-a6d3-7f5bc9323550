
.o_custom_form_view {
    .custom_statusbar {
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button.o_arrow_button_current:not(.d-none):disabled,
        .o_field_statusbar_duration > .o_statusbar_status > .o_arrow_button.o_arrow_button_current:not(.d-none):disabled,
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button:not(.d-none):active:not(.o_first),
        .o_field_statusbar_duration > .o_statusbar_status > .o_arrow_button:not(.d-none):active:not(.o_first) {
            background-color: $custom_statusbar-background-color;
            color: $rs_text_bar;
        }
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button:not(.d-none):not(.o_first):after,
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button:not(.d-none):not(.o_last):after {
            border-left: none !important;
        }
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button:not(.d-none):hover {
            background-color: unset;
        }
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button:not(.d-none) {
            position: unset;
            padding-left: 0.625rem;
            clip-path: unset;
            margin-left: 0;
        }
        .o_field_statusbar > .o_statusbar_status > .o_arrow_button:not(.d-none)::before {
            display: none !important;
        }
    }
    .o_form_nosheet {
        padding-top: 0 !important;
    }
    .o_field_percentage.oe_inline input {
        max-width: fit-content;
        width: 37px;
    }
    .floor_type {
        .btn-secondary {
            background-color: unset;
        }
        .btn-secondary:active, .btn-secondary.active {
            background-color: #e6f2f3;
        }
    }
    .o_title_form {
        background-color: $custom_statusbar-background-color;
        margin-left: -16px;
        margin-right: -16px;
        padding-left: 15px;
        input.o_input {
            font-weight: 500;
            text-align: left;
        }
        // div {
        //     width: 100%;
        // }
        .price_amount {
            display: contents;
            text-align: right;
            font-size: large;
            font-weight: 600;
            span {
                padding: 7px;
            }
        }
    }
    .custom_statusbar {
        .btn-secondary {
            color: #a0a4ab;
            background-color: inherit;
            border: unset;
        }
        .btn {
            font-weight: normal !important;
            // font-size: medium;
            padding: 0.5rem 1rem !important;
            border-radius: unset;
        }
        .o_field_widget {
            margin-bottom: 0;
        }
    }
    h2 {
        margin-top: 8px;
    }
    .o_field_custom {
        // padding-left: 15px;

        .form-charges {
            padding: 8px;
            background-color: $form-charges;
            margin-bottom: 35px;
            border: 1px solid $rs_border-table;
            // border-top: 2px solid var(--border-color, #d8dadd);
            // border-bottom: none;
        }
        .form-group-product, .group-product-summary {
            padding: 8px;
            background-color: $form-group-product;
            border: 1px solid $rs_border-table;
            // border-top: 2px solid var(--border-color, #d8dadd);
            // border-bottom: none;
        }
        .o_field_widget {
            width: 100% !important;
        }
        .o_field_image {
            width: unset !important;
        }
        .form-check-input:focus {
            box-shadow: unset;
        }
        .row {
            .o_field_widget {
                border: 1px solid #d8dbdf;
                padding: 5px;
                padding-left: 8px;
                margin-top: 5px;
                margin-bottom: 12px;
            }
            .o_readonly_modifier {
                height: 35px;
            }
            .o_input {
                border: unset;
                border-width: 0 0 0 0;
            }
            .o_field_widget.oe_inline > *:not(.o_field_input_buttons) {
                width: 100% !important;
            }
            // .o_field_input_buttons {
            //     width: auto !important;
            // }
        }
        input::placeholder {
            color: #a0a4ab;
        }
        .o_inner_group {
            grid-template-columns: fit-content(270px) 1fr !important;
        }
        .address-header {
            padding: 10px;
            border-bottom: 1px solid black;
            margin-bottom: 20px;
        }
        .text-invisible.o_readonly_modifier {
            color: transparent; /* make the text invisible */
            user-select: none;
        }
        .order_product_line {
            th[data-name="product_template_id"] {
                max-width: 330px !important;
            }
            th[data-name="product_uom"] {
                text-align: right !important;
                span {
                    text-align: right !important;
                }
            }
            td[data-name="product_uom"] {
                text-align: right !important;
                span {
                    text-align: right !important;
                }
            }

            th[data-name="name"] {
                width: 344px !important;
            }
            th[data-name="product_uom_qty"] {
                width: 156px !important;
            }
        }
        .charges_table {
            th[data-name="product_template_id"] {
                max-width: 320px !important;
            }
            th[data-name="price_unit"] {
                width: 200px !important;
                max-width: 200px !important;
            }
            th[data-name="price_subtotal"] {
                max-width: 200px !important;
                width: 200px !important;
            }
        }
        .summary_table {
            th[data-name="name"] {
                max-width: 250px !important;
                width: 250px !important;
            }
            th[data-name="product_uom_qty"] {
                width: 90px !important;
            }
            th[data-name="product_uom"] {
                text-align: right !important;
                span {
                    text-align: right !important;
                }
                width: 90px !important;
                max-width: 95px !important;
            }
            th[data-name="is_subcontract_service"] {
                width: 70px !important;
            }
        }
        td.o_data_cell.cursor-pointer.o_field_cell.o_list_many2one.o_readonly_modifier {
            text-align: right !important;
        }
        .order_line_cut {
            th[data-name="product_template_id"] {
                width: 160px !important;
                max-width: 300px !important;
            }
            th[data-name="image"] {
                width: 50px !important;
                max-width: 50px !important;
            }
            th:nth-child(3) {
                width: 30px !important;
                max-width: 30px !important;
            }
            th:nth-child(3) {
                width: 30px !important;
            }
            th[data-name="room_id"] {
                max-width: 160px !important;
            }
            th[data-name="buyin_colour"] {
                width: 70px !important;
            }
            th[data-name="piece"] {
                width: 56px !important;
                max-width: 56px !important;
            }
            th[data-name="product_uom_qty"] {
                width: 90px !important;
                max-width: 90px !important;
            }
            th[data-name="product_uom"] {
                text-align: right !important;
                span {
                    text-align: right !important;
                }
                width: 80px !important;
                max-width: 95px !important;
            }
            td[data-name="product_uom"] {
                text-align: right !important;
                span {
                    text-align: right !important;
                }
            }
            th[data-name="sqmeters"] {
                width: 90px !important;
                max-width: 95px !important;
            }
            th[data-name="price_unit"] {
                width: 90px !important;
                max-width: 95px !important;
            }
            th[data-name="price_subtotal"] {
                max-width: 100px !important;
            }
            th[data-name="default_underlay_code_id"] {
                width: 160px !important;
                max-width: 170px !important;
            }
            button[data-name="action_duplicate_row"] {
                width: 30px !important;
                max-width: 30px !important;
            }
            .o_list_button {
                width: 31px !important;
                max-width: 61px !important;
            }
        }
    }

    div[data-name="order_line"] {
        .o_section_and_note_list_view {
            th.o_list_button {
                width: 30px !important;
                max-width: 30px !important;
            }
        }
    }

    .checklist_form {

        .field_checklist {
            border: 1px solid $rs_field_checklist_border;
            background-color: $rs_field_checklist;
            margin-top: 5px;
            margin-bottom: 18px;


            .o_field_selection_badge {
                margin-bottom: 0;
                justify-content: right;
                display: flex;
                div {
                    margin-bottom: 0 !important;
                }
                .gap-1 {
                    gap: unset !important;
                }
                .custom_selection_badge {
                    margin-bottom: 0 !important;
                }
                .btn-secondary {
                    color: $rs_field_selection_badge_color;
                    background-color: $rs_field_selection_badge;
                    border-color: $rs_field_checklist;
                    border-radius: 0;
                }
            }
            .o_form_label {
                width: 100%;
                padding: 7px;
            }
        }
        .text_box {
            .o_field_selection_badge {
                background-color: $rs_field_selection_badge_text;
                width: 100%;
                div {
                    width: 100%;
                }
            }
        }
        .date_box, .datetime_box {
            .o_field_selection_badge {
                div {
                    background-color: $rs_field_selection_badge_text;
                }
            }
        }
        .referral_source {
            .o_form_label {
                width: 90%;
            }
        }

        .o_field_many2many_checkitems {
            width: 40% !important;
            margin-bottom: 0;
            label {
                padding: 6px 10px 6px 10px;
                border: 1px solid $rs_field_checklist_border;
                color: $rs_field_many2many_checkitems_text;
                background-color: $rs_field_selection_badge;

            }
            .form-check-input {
                border: unset;
            }
            .form-check-input[type="checkbox"]:checked {
                background-image: unset;
            }
            .form-check-input:checked {
                background-color: Unset;
            }
            .form-check-input:last-child:checked {
                background-color: unset;
            }
            div:first-child {
                justify-content: flex-end;
                display: flex;
                margin-bottom: 0;
            }
            .form-check {
                padding-left: 0;
            }
        }
    }
}
.checklist_form_custom {
    .background_text_box {
        background-color: $rs_field_selection_badge;
    }

    .field_checklist {
        border: 1px solid $rs_field_checklist_border;
        background-color: $rs_field_checklist;
        margin-top: 5px;
        margin-bottom: 18px;


        .o_field_selection_badge {
            margin-bottom: 0;
            justify-content: right;
            display: flex;
            div {
                margin-bottom: 0 !important;
            }
            .gap-1 {
                gap: unset !important;
            }
            .custom_selection_badge {
                margin-bottom: 0 !important;
            }
        }
        .o_form_label {
            width: 100%;
            padding: 7px;
        }
    }
    .o_field_widget {
        width: 100% !important;
    }
    .row {
        .o_field_widget {
            padding: 5px;
            padding-left: 8px;
            margin-top: 5px;
            margin-bottom: 12px;
        }
        .o_field_widget.oe_inline > *:not(.o_field_input_buttons) {
            width: 100% !important;
        }
    }
    input::placeholder {
        color: #a0a4ab;
    }
    .o_inner_group {
        grid-template-columns: fit-content(270px) 1fr !important;
    }
    .address-header {
        padding: 10px;
        border-bottom: 1px solid black;
        margin-bottom: 20px;
    }
}
.o_wrap_label {
    .change_color_red {
        color: red;
    }
}

// Photo form

#photo_form {
    .header_photo_form {
        justify-content: space-between;
    }
    .oe_kanban_global_click {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-content: space-between;
    }
}

.style_button_image {
    label {
        color: unset;
        background-color: unset;
        border-color: unset;
        border: unset;
    }
}

.kanban_photo {
    .kanban-container {
        display: flex;
        flex-direction: column;
        background: #f9fafbbf !important;
    }
    .footer_space {
        height: 3vh;
    }
    .footer_btn_record {
        display: flex;
        justify-content: start; /* Positions items at opposite ends */
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100%;
        padding: 5px;
        box-sizing: border-box;
        // border-top: 1px solid var(--border-color, #d8dadd);
        .fa {
            font-size: large;
        }
    }
    .footer_btn_record button {
        display: none; /* Ẩn các nút theo mặc định */
        transition: opacity 0.3s;
    }
    .kanban-container:hover button {
        display: inline-block; /* Hiển thị các nút khi hover vào khối */
    }
}

#detail_sale_form {
    .description {
        .o_field_text {
            border: 1px solid #d4d4d4;
            color: #5a5a5a;
            div {
                height: auto !important;
                textarea {
                    height: auto !important;
                    padding: 5px;
                    color: $rs_text;
                }
            }
        }

    }
    .container_detail {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .list-group-item {
        display: block;
        padding: 0.75rem 1.25rem;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    svg:not(:root).svg-inline--fa {
        overflow: visible;
    }
    .svg-inline--fa.fa-w-16 {
        width: 1em;
    }
    .svg-inline--fa {
        display: inline-block;
        font-size: inherit;
        height: 1em;
        overflow: visible;
        vertical-align: -0.125em;
    }
    .o_field_many2many_additems {
        width: 100% !important;
        margin-bottom: 0;
        label {
            padding: 6px 10px 7px 10px;
            border: 1px solid #f3efef;
            color: $rs_text;
            background-color: $rs_field_many2many_additems;

        }
        .form-check-input {
            border: unset;
        }
        .form-check-input[type="checkbox"]:checked {
            background-image: unset;
        }
        .form-check-input:checked {
            background-color: Unset;
        }
        .form-check-input:last-child:checked {
            background-color: unset;
        }
        div:first-child {
            margin-bottom: 0;
        }
        .form-check {
            padding-left: 0;
            width: 100%;
        }
        .form-check-label {
            width: 100%;
        }
    }
    .list_items_question {
        .form-check-label {
            border-radius: 4px;
            border: 1px solid #33333340;
            padding: 10px 14px;

            fa-icon {
                margin-right: 8px;
            }
        }
    }
    .checkbox_field{
        .o_field_widget  {
            border: unset;
            display: contents;
            .o-checkbox {
                width: auto !important;
            }
        }
    }
    .ui-autocomplete {
        min-width: 28%;
    }
}
#note_form {
    .o_field_text {
        border: 1px solid #d4d4d4;
        color: #5a5a5a;
        margin-bottom: 0;
    }
    button {
        width: 5%;
    }
}

#product_form {
    background-color: $custom_statusbar-background-color;
    padding: 10px;
    margin-left: -16px;
    margin-right: -16px;
    margin-top: -8px;
    margin-bottom: -16px;
    .o_field_many2many_tags {
        width: 100% !important;
        .o_field_tags {
            width: 100%;
        }
    }
    .checkbox_field{
        .o_field_widget  {
            border: unset;
            display: contents;
            .o-checkbox {
                width: auto !important;
            }
        }
    }
    .button_plus {
        display: flex;
        justify-content: end;
        .oe_avatar {
            width: 39px !important;
            padding: 0;
            border-radius: 4px;
            border: 1px solid #d1d5d8;
            img {
                border-radius: 4px;
                width: 38px !important;
                height: 35px;
                border: unset;
            }
        }
        button {
            width: 37px;
            margin-top: 5px;
            margin-bottom: 12px;
            padding-bottom: 7px;
        }
        .btn-secondary {
            color: #fff !important;
            background-color: #6c757d !important;
            border-color: #6c757d !important;
        }
        .oe_stat_button {
            background-color: #d1d5d8;
        }
    }

    .oe_forecasted_button {
        // background-color: #d1d5d8;
        border-radius: 4px;
        border: 1px solid #d1d5d8;
        width: 37px;
        margin-top: 5px;
        margin-bottom: 12px;
        .o_widget_qty_at_date_widget_sale {
            height: 100%;
            padding: 0;
        }
        a {
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
    }
    .btn-outline-secondary{
        width: 37px;
        margin-bottom: 8px;
        padding-bottom: 7px;
    }
    .o_price_total_retail {
        border: unset;
    }
    .card-body {
        padding-left: 16;
        padding-top: 8px;
        padding-bottom: 0;
    }
    .font-weight-bold {
        font-weight: bold;
    }
    .form-charges, .group-product-summary {
        // border-top: 1px solid var(--border-color, #d8dadd);
        margin-top: 10px;
    }
    .total_product {
        display: contents;
        text-align: right;
    }
}
.text-right {
    text-align: right;
}
.text-center {
    text-align: center;
}
.o_detail_sale_price {
    .o_field_widget {
        width: 100% !important;
        border: 1px solid #d8dbdf;
        padding: 5px;
        padding-left: 8px;
        margin-top: 5px;
        margin-bottom: 12px;
    }
    .o_input {
        border: unset;
        border-width: 0 0 0 0;
    }
    .o_field_widget.oe_inline > *:not(.o_field_input_buttons) {
        width: 100% !important;
    }
}

.tax-totals-retails {
    .oe_subtotal_footer {
        margin-left: 0 !important;
    }
}

.hide-dropdown-custom {
    .o_m2o_dropdown_option_create {
        display: none !important;
    }
    .o_m2o_dropdown_option_search_more {
        display: none !important;
    }
}

.o_row {
    .o_field_res_partner_many2one {
        padding-left: 35px;
    }
}

@media (max-width: 1024px) {
    .o_row .o_field_res_partner_many2one {
        padding-left: 5px;
    }
}
.o_list_table {
    .o_list_table {
        thead {
            z-index: 1300;
            top: 0;
        }
    }
}

.color-info-badge {
    color: #f5f6f8;
    border: 1px solid #898282;
    padding: 0px;
    border-radius: 16px;
    max-width: fit-content !important;
    background-color: #17a2b8;
    font-weight: 500;
    text-align: center;
    span {
        padding: 0 12px;
    }
    select {
        padding: 0 12px !important;
        color: #f5f6f8;
        font-weight: 500;
        text-align: center;
        background: unset;
        &:hover {
            padding-bottom: 1px !important;
            border: none;
        }
        &:focus {
            border: none;
            padding-bottom: 1px !important;
        }
    }
    option {
        color: #111827;
    }

    .o_required_modifier {
        --o-input-border-color: none;
    }
}
.color-success-badge {
    color: #f5f6f8;
    border: 1px solid #898282;
    padding: 0px;
    border-radius: 16px;
    max-width: fit-content !important;
    background-color: #28a745;
    font-weight: 500;
    text-align: center;
    span {
        padding: 0 12px;
    }
    select {
        padding: 0 12px !important;
        color: #f5f6f8;
        font-weight: 500;
        text-align: center;
        background: unset;
        &:hover {
            padding-bottom: 1px !important;
            border: none;
        }
        &:focus {
            border: none;
            padding-bottom: 1px !important;
        }
    }
    option {
        color: #111827;
    }

    .o_required_modifier {
        --o-input-border-color: none;
    }
}

.color-warning-badge {
    color: #f5f6f8;
    border: 1px solid #898282;
    padding: 0px;
    border-radius: 16px;
    max-width: fit-content !important;
    background-color: RGBA(107, 62, 102, var(--bg-opacity, 1));
    font-weight: 500;
    text-align: center;
    span {
        padding: 0 12px;
    }
    select {
        padding: 0 12px !important;
        color: #f5f6f8;
        font-weight: 500;
        text-align: center;
        background: unset;
        &:hover {
            padding-bottom: 1px !important;
            border: none;
        }
        &:focus {
            border: none;
            padding-bottom: 1px !important;
        }
    }
    option {
        color: #111827;
    }
    .o_required_modifier {
        --o-input-border-color: none;
    }
}
.retail_location_wrap {
    display: flex;
    justify-content: flex-end;
    .retail_location {
        display: flex;
        max-width: 80%;
        border: 1px solid #dddfe1;
        margin-top: 5px;
        margin-bottom: 14px;
        .o_field_widget {
            border: none !important;
            margin: 0 !important;;
        }
        .o_field_float {
            text-align: left;
            width: 25% !important;
            padding: 5px 0px !important;
        }
        .o_field_selection {
            width: 75% !important;
        }
        .text_retail_qty {
            padding: 5px 0px;
        }
    }
}

#dialog_style_custom {
    max-width: 70% !important;
}
.quant_stock_wizard_style {
    .o_field_x2many_list_row_add, .o_list_record_remove {
        display: none !important;
    }
}
.o_list_view.o_sale_order {
    th[data-name="margin_string"] {
        text-align: right !important;
    }
    td[name="margin_string"] {
        text-align: right !important;
    }
}
.show-unknow {
    .checkbox-wrapper-51 {
        p {
            padding-right: 10px;
            color: #000000;
            margin-bottom: 0px;
            margin-top: 3px;
        }
        .toggle {
            position: relative;
            display: block;
            width: 42px;
            height: 24px;
            cursor: pointer;
            -webkit-tap-highlight-color: transparent;
            transform: translate3d(0, 0, 0);
        }

        .toggle:before {
            content: "";
            position: relative;
            top: 1px;
            left: 1px;
            width: 40px;
            height: 22px;
            display: block;
            background: #c8ccd4;
            border-radius: 12px;
            transition: background 0.2s ease;
        }
        .toggle span {
            position: absolute;
            top: 0;
            left: 0;
            width: 24px;
            height: 24px;
            display: block;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(154,153,153,0.75);
            transition: all 0.2s ease;
        }

        .toggle span svg {
            margin: 7px;
            fill: none;
        }
        .toggle span svg path {
            stroke: #c8ccd4;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 24;
            stroke-dashoffset: 0;
            transition: all 0.5s linear;
        }
        input[type="checkbox"] {
            visibility: hidden;
            display: none;
        }

        input[type="checkbox"]:checked + .toggle:before {
            background: #017e84;
        }

        input[type="checkbox"]:checked + .toggle span {
            transform: translateX(18px);
        }

        input[type="checkbox"]:checked + .toggle span path {
            stroke: #000000;
            stroke-dasharray: 25;
            stroke-dashoffset: 25;
        }
    }
}
.text-left {
    text-align: left;
}

.info_sale_popup {
    .o_form_label_readonly {
        opacity: 1 !important;
        font-weight: 600 !important;
    }
    .container {
        text-align: center;
        width: 300px;
    }

    .info-circle {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 2px solid #00AEEF; /* Light blue border */
        color: #00AEEF; /* Light blue text */
        font-size: 24px; /* Font size for "i" */
        font-family: Arial, sans-serif;
    }
    .text-nowrap {
        text-align: left;
        padding-left: 5px;
        display: inline-block;
    }
}

.o_field_many2one_extra {
    font-size: large !important;
}

.bg-green {
    .o_date_item_percentage {
        background-color: #c1facf;
        border-radius: 40px;
        padding: 18px 18px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 0;
    }
}
.bg-orange {
    .o_date_item_percentage {
        background-color: #ffeeba;
        border-radius: 40px;
        padding: 18px 18px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 0;
    }
}
.bg-red {
    .o_date_item_percentage {
        background-color: #ffc1c6;
        border-radius: 40px;
        padding: 18px 18px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 0;
    }
}

.o_datetime_picker .o_date_item_cell {
    color: #000000;
}

.o_datetime_picker .o_out_of_range {
    color: var(--gray-400);
}

.order_product_line_builder {
    .o_list_renderer.o_renderer.table-responsive {
        margin-left: 2px;
        margin-right: 2px;
    }
    .table_cut {
        padding-right: 5px;
        padding-left: 5px;
        border: 1px solid $bs_table_cut_border;
        background-color: $bs_table_cut;
        width: 100%;
        .o_list_table {
            margin: 0 auto;
            margin-top: 32px;
        }
        th[data-name="product_template_id"] {
            max-width: 250px !important;
        }
        td[data-name="product_uom"] {
            text-align: right !important;
            span {
                text-align: right !important;
            }
        }
        th[data-name="product_uom"] {
            text-align: right !important;
            span {
                text-align: right !important;
            }
        }
    }
    .table_summary {
        margin-top: 35px;
        padding-right: 5px;
        padding-left: 5px;
        border: 1px solid $bs_table_cut_border;
        background-color: $bs_table_cut;
        width: 100%;
        .o_list_table {
            margin: 0 auto;
        }
        .card-body {
            padding: 16px 10px;
            padding-bottom: 8px;
            background-color: $bs_table_cut !important;
            h4 {
                margin: 6px 0;
            }
        }
        th[data-name="product_template_id"] {
            max-width: 330px !important;
        }
    }
    .table_total_builder {
        padding-right: 10px;
        .o_group {
            width: 100%;
            margin: 0 auto;
        }
    }
}
