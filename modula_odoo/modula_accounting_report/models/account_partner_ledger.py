from odoo import api, models, osv


class PartnerLedgerCustomHandler(models.AbstractModel):
    _inherit = "account.partner.ledger.report.handler"

    # def _get_aml_values(self, options, partner_ids, offset=0, limit=None):
    #     aml_values = super()._get_aml_values(options, partner_ids, offset, limit)
    #     for partner_id, amls in aml_values.items():
    #         amls[:] = [
    #             line
    #             for line in amls
    #             if line.get("name") == None
    #             or "Job Completed Line" not in line.get("name")
    #         ]
    #     return aml_values

    def _get_options_account_type_domain(self, res):
        return [("name", "not ilike", "Job Completed Line")] + osv.expression.OR(
            [
                res,
                [
                    "&",
                    ("account_id.code", "=", "M-000-8750-0000-980"),
                    ("move_id.origin_payment_id", "!=", False),
                    ("move_id.name", "not ilike", "SCH/"),
                ],
            ]
        )


class AccountReport(models.Model):
    _inherit = "account.report"

    def _get_options_account_type_domain(self, options):
        res = super()._get_options_account_type_domain(options)

        if self.custom_handler_model_id:
            handler = self.env[self.custom_handler_model_name]
            function_name = "_get_options_account_type_domain"
            if hasattr(handler, function_name):
                return handler._get_options_account_type_domain(res)
        return res
