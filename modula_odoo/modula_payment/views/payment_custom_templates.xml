<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="custom_transaction_status" inherit_id="payment.state_header">
        <xpath expr="//div[@name='o_payment_status_alert']/a" position="attributes">
            <attribute name="t-if">False</attribute>
        </xpath>
        <xpath expr="//div[@id='o_payment_status_icon']" position="attributes">
            <attribute name="t-if">not is_processing</attribute>
        </xpath>
    </template>

    <!-- <template id="custom_transaction_status" inherit_id="payment.state_header">
        <xpath expr="//div[@id='o_payment_status_icon']" position="before">
            <t t-if="tx.state == 'pending'">
                <t t-set="alert_style" t-value="'warning'"/>
                <t t-set="status_message" t-value=""/>
            </t>
        </xpath>
    </template> -->

    <!-- <template id="method_form_retail" inherit_id="payment.method_form"> -->
        <!-- === Inline form === -->
        <!-- <xpath expr="//p[@name='o_payment_secured_by']" position="attributes">
            <attribute name="t-if">provider_sudo.code == 'securepay' or provider_sudo.code == 'securepay_amex'</attribute>
        </xpath>
    </template> -->

    <template id="method_form_cash" inherit_id="payment.method_form" name="Payment Method Form">
        <xpath expr="//div[@name='o_payment_inline_form']" position="after">
            <t t-if="provider_sudo._get_code() == 'cash'">
                <div name="cash_cash_payment" class="cash-cash d-none">
                    <div class="cash-cash-container">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modal-basic-title">Payment amount</h5>
                        </div>
                        <div class="modal-body cash-input-wrap">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="amount">
                                            <span class="hidden-xs">Payment amount</span>
                                        </label>
                                        <input class="form-control ng-pristine ng-invalid ng-touched" name="cash_amount" id="cash_amount" placeholder="Amount" type="number"  ng-reflect-name="amount" t-att-value="'%.2f' % amount"/>

                                        <div>
                                            <span id="cash_amount_required_error" class="text-danger small d-none">Amount is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="reference">
                                            <span class="hidden-xs">Payment reference (Receipt No.)</span>
                                        </label>
                                        <input class="form-control ng-untouched ng-pristine ng-invalid" name="cash_reference" id="cash_reference" placeholder="Reference" type="text" ng-reflect-name="reference" t-att-value="unique_receipt_sequence"/>
                                        <div>
                                            <span id="cash_reference_required_error" class="text-danger small d-none">Reference is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
            <t t-elif="provider_sudo._get_code() == 'bank_transfer'">
                <div name="bank_transfer_cash_payment" class="cash-cash d-none">
                    <div class="cash-cash-container">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modal-basic-title">Payment amount</h5>
                        </div>
                        <div class="modal-body bank_transfer-input-wrap">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="amount">
                                            <span class="hidden-xs">Payment amount</span>
                                        </label>
                                        <input class="form-control ng-pristine ng-invalid ng-touched" name="bank_transfer_amount" id="bank_transfer_amount" placeholder="Amount" type="number" readonly="readonly" ng-reflect-name="amount" t-att-value="'%.2f' % amount"/>

                                        <div>
                                            <span id="bank_transfer_amount_required_error" class="text-danger small d-none">Amount is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="reference">
                                            <span class="hidden-xs">Payment reference (Receipt No.)</span>
                                        </label>
                                        <input class="form-control ng-untouched ng-pristine ng-invalid" name="bank_transfer_reference" id="bank_transfer_reference" placeholder="Reference" type="text" ng-reflect-name="reference" t-att-value="unique_receipt_sequence"/>
                                        <div>
                                            <span id="bank_transfer_reference_required_error" class="text-danger small d-none">Reference is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
            <t t-elif="pm_sudo.code == 'eftpos' and payment_methods_eftpos_sudo">
                <div name="eftpos_cash_payment" class="eftpos-cash d-none">
                    <div class="eftpos-cash-container">
                        <div class="modal-header" id="header_eftpos">
                            <h5 class="modal-title" id="modal-basic-title">Enter the Payment amount</h5>
                        </div>
                        <div class="modal-body eftpos-input-wrap">
                            <t t-set="brand_ids" t-value="payment_methods_eftpos_sudo.brand_ids"/>
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="reference">
                                            <span class="hidden-xs">Payment reference (Receipt No.)</span>
                                        </label>
                                        <input class="form-control ng-untouched ng-pristine ng-invalid" name="eftpos_reference" id="eftpos_reference" placeholder="Reference" type="text" ng-reflect-name="reference"/>
                                        <div>
                                            <span id="reference_required_error" class="text-danger small d-none">Reference is required</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="provider">
                                            <span class="hidden-xs">Payment Method</span>
                                        </label>
                                        <select id="eftpos_payment_option" class="form-control o_website_form_input" required="" t-if="payment_methods_eftpos_sudo">
                                            <t t-foreach="payment_methods_eftpos_sudo" t-as="pm_eftpos_sudo">
                                                <t t-set="is_selected"
                                                    t-value="pm_eftpos_sudo.id == selected_method_id"
                                                    />
                                                <t t-if="not pm_eftpos_sudo.brand_ids">
                                                    <option t-att-value="pm_eftpos_sudo.id" t-att-data-payment-method-eftpos="pm_eftpos_sudo.code">
                                                        <t t-esc="pm_eftpos_sudo.name"/>
                                                    </option>
                                                </t>
                                            </t>
                                            <option t-if="brand_ids" t-att-value="brand_ids[0].id" t-att-data-payment-method-eftpos="">
                                                Credit Card
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="mb-3 credit-card-wrap d-none">
                                <div class="row mt-3">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="reference">
                                                <span class="hidden-xs">Credit card number</span>
                                            </label>
                                            <div class="d-flex">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">
                                                        <fa-icon class="ng-fa-icon" ng-reflect-icon="credit-card"><svg role="img" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="credit-card" class="svg-inline-fa fa-credit-card fa-w-18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M0 432c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V256H0v176zm192-68c0-6.6 5.4-12 12-12h136c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H204c-6.6 0-12-5.4-12-12v-40zm-128 0c0-6.6 5.4-12 12-12h72c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM576 80v48H0V80c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48z"></path></svg></fa-icon>
                                                    </span>
                                                </div>
                                                <input class="form-control" name="eftpos_card_number" id="eftpos_card_number" placeholder="Card number" type="text" t-att-required="true"/>
                                            </div>
                                            <div class="d-flex image-card">
                                                <t t-foreach="brand_ids.filtered(lambda p: p.code != 'eftposvisadebit')" t-as="pm_to_display_sudo">
                                                    <t t-set="logo_pm_sudo" t-value="pm_to_display_sudo"/>
                                                    <span t-field="logo_pm_sudo.image_payment_form"
                                                        t-options="{'widget': 'image', 'alt-field': 'name'}"
                                                        class="position-relative d-block rounded overflow-hidden z-index-1 shadow-sm"
                                                        t-att-title="logo_pm_sudo.name"
                                                        name="eftpos_card_type"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        data-bs-delay="0"
                                                        t-att-data-logo_pm_id="pm_to_display_sudo.id"
                                                        t-att-data-logo_pm_code="pm_to_display_sudo.code"
                                                    />
                                                </t>
                                            </div>
                                            <div>
                                                <span id="card_number_required_error" class="text-danger small d-none">Credit Card Number is required</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="reference">
                                                <span class="hidden-xs">Expiration</span>
                                            </label>
                                            <div class="d-flex">
                                                <div>
                                                    <input class="form-control" name="eftpos_expiration_month" id="eftpos_expiration_month" placeholder="MM" type="text"/>
                                                    <span id="expiration_month_required_error" class="text-danger small d-none">Expiry Month is required</span>
                                                </div>
                                                <div>
                                                    <input class="form-control" name="eftpos_expiration_year" id="eftpos_expiration_year" placeholder="YYYY" type="text"/>
                                                    <span id="expiration_year_required_error" class="text-danger small d-none">Expiry Year is required</span>
                                                </div>
                                            </div>
                                            <div>
                                                <span id="expiry_error" class="text-danger small d-none">Invalid expiry date</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="reference">
                                                <span class="hidden-xs">Name on card</span>
                                            </label>
                                            <input class="form-control" name="eftpos_name_card" id="eftpos_name_card" placeholder="Name on card" type="text" t-att-value="sale_order.partner_id.display_name"/>
                                            <div>
                                                <span id="name_card_required_error" class="text-danger small d-none">Name on card is required</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="reference">
                                                <span class="hidden-xs">CVV/CVC</span>
                                            </label>
                                            <input class="form-control" name="eftpos_ccv" id="eftpos_ccv" placeholder="CCV" type="number"/>
                                            <div>
                                                <span id="ccv_required_error" class="text-danger small d-none">CCV is required</span>
                                                <span id="ccv_invalid_error" class="text-danger small d-none">Invalid CVV</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> -->

                            <div id="surcharge_percentage" class="o_website_form_input mb-3 select-surcharge d-none" required="">
                                <p style="color: #414141">Surcharge Percentage <input name="enableSurchargeEftpos" style="transform: scale(1.2); margin-left:5px" type="checkbox" class="enable_surcharge_eftpos"/></p>
                                <div class="mb-3 d-flex select-surcharge-eftpos" id="surcharge_percentage">
                                    <t t-foreach="brand_ids.filtered(lambda pm: pm.active)" t-as="brand_id">
                                        <t t-if="brand_id.name.lower() == 'visa'">
                                            <div class="btn btn-surcharge-eftpos btn-surcharge-visa" t-att-data-pm-id="brand_id.id" t-att-data-pm-name="brand_id.code"><span>Visa <t t-esc="'{:.2f}'.format(brand_id.surcharge_percentage * 100)"/> %</span></div>
                                        </t>
                                        <t t-elif="brand_id.name.lower().find('master') != -1">
                                            <div class="btn btn-surcharge-eftpos btn-surcharge-master" t-att-data-pm-id="brand_id.id" t-att-data-pm-name="brand_id.code"><span>Master <t t-esc="'{:.2f}'.format(brand_id.surcharge_percentage * 100)"/> %</span></div>
                                        </t>
                                        <t t-elif="brand_id.name.lower().find('american') != -1">
                                            <div class="btn btn-surcharge-eftpos btn-surcharge-amex" t-att-data-pm-id="brand_id.id" t-att-data-pm-name="brand_id.code"><span>Amex <t t-esc="'{:.2f}'.format(brand_id.surcharge_percentage * 100)"/> %</span></div>
                                        </t>
                                        <t t-else="">
                                            <div class="btn btn-surcharge-eftpos btn-surcharge-other" t-att-data-pm-id="brand_id.id" t-att-data-pm-name="brand_id.code"><span><t t-esc="brand_id.name"/> <t t-esc="'{:.2f}'.format(brand_id.surcharge_percentage * 100)"/> %</span></div>
                                        </t>
                                    </t>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6 d-none" id="eftpos_surcharge_wrap">
                                    <div class="form-group">
                                        <label for="surcharge_amount">
                                            <span class="hidden-xs">Surcharge amount</span>
                                        </label>
                                        <input class="form-control" id="eftpos_surcharge_amount" type="text" name="eftpos_surcharge_amount" readonly="readonly" value="0.00"/>
                                    </div>
                                </div>
                                <div class="col-12" id="eftpos_amount_wrap">
                                    <div class="form-group">
                                        <label for="amount">
                                            <span class="hidden-xs">Total amount</span>
                                        </label>
                                        <input class="form-control" id="eftpos_amount" type="number" name="eftpos_amount" t-att-value="'%.2f' % amount"/>
                                        <div>
                                            <span id="amount_required_error" class="text-danger small d-none">Reference is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
            <t t-elif="provider_sudo._get_code() == 'finance' and payment_methods_finance_sudo">
                <div name="finance_cash_payment" class="finance-cash d-none">
                    <div class="finance-cash-container">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modal-basic-title">Enter the finance approval number and payment amount</h5>
                        </div>
                        <div class="modal-body finance-input-wrap">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="amount">
                                            <span class="hidden-xs">Payment Amount</span>
                                        </label>
                                        <input class="form-control" id="finance_amount" placeholder="Amount" type="number" name="finance_amount" t-att-value="'%.2f' % amount"/>
                                        <div>
                                            <span id="finance_amount_required_error" class="text-danger small d-none">Amount is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="provider">
                                            <span class="hidden-xs">Finance Provider</span>
                                        </label>
                                        <select id="finance_payment_option" class="form-control o_website_form_input" required="">
                                            <t t-foreach="payment_methods_finance_sudo" t-as="pm_sudo">
                                                <t t-set="is_selected"
                                                    t-value="pm_sudo.id == selected_method_id"
                                                    />
                                                <option t-att-value="pm_sudo.id">
                                                    <t t-esc="pm_sudo.name"/>
                                                </option>
                                            </t>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="reference">
                                            <span class="hidden-xs">Approval Number</span>
                                        </label>
                                        <input class="form-control" id="finance_reference" placeholder="Finance Approval Number" type="text" name="finance_reference"  t-att-value="unique_receipt_sequence"/>
                                        <div>
                                            <span id="finance_reference_required_error" class="text-danger small d-none">Approval Number is required</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

</odoo>
