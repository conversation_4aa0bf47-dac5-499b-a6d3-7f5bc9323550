<?xml version ="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_account_payment_form_inherit_branch" model="ir.ui.view">
        <field name="name">view.account.payment.invoice.form.inherit.branch</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="modula_branch.view_account_payment_form_inherit_branch" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='branch_id']" position="attributes">
                <attribute name="invisible">not is_sale_manager</attribute>
                <attribute name="readonly">not can_edit</attribute>
            </xpath>
            <xpath expr="//field[@name='branch_id']" position="after">
                <field name="branch_id" readonly="not can_edit" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}" invisible="is_sale_manager"/>
            </xpath>
        </field>
    </record>

    <record id="view_account_payment_tree_inherit" model="ir.ui.view">
		<field name="name">view.account.payment.tree.inherit</field>
		<field name="model">account.payment</field>
		<field name="priority">50</field>
		<field name="inherit_id" ref="account_batch_payment.view_account_payment_tree_inherit_account_batch_payment"/>
		<field name="arch" type="xml">
			<field name="journal_id" position="attributes">
				<attribute name="optional">show</attribute>
			</field>
			<field name="payment_method_line_id" position="attributes">
				<attribute name="optional">show</attribute>
			</field>
			<field name="batch_payment_id" position="attributes">
				<attribute name="optional">show</attribute>
			</field>
			<field name="name" position="after">
				<field name="partner_id" position="move"/>
			</field>
 		</field>
	</record>

	<record id="payment_provider_form" model="ir.ui.view">
        <field name="name">Customer Account Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <!-- Load after account_payment for the invisible attr. of the payment_followup group. -->
        <field name="priority">40</field>
        <field name="arch" type="xml">
            <field name="state" position="after">
                <field name="show_ecommerce"/>
            </field>
			<page name="credentials" position="attributes">
                <attribute name="invisible" separator="or" add="code == 'customer_account'"/>
            </page>
            <field name="payment_method_ids" position="attributes">
                <attribute name="invisible" separator="or" add="code == 'customer_account'"/>
            </field>
            <a name="action_view_payment_methods" position="attributes">
                <attribute name="invisible" separator="or" add="code == 'customer_account'"/>
            </a>
            <field name="capture_manually" position="after">
                <field name="qr_code" invisible="code != 'customer_account'"/>
            </field>
            <group name="payment_followup" position="attributes">
                <attribute name="invisible">code == 'customer_account'</attribute>
            </group>
            <field name="pre_msg" position="attributes">
                <attribute name="invisible" separator="or" add="code == 'customer_account'"/>
            </field>
            <field name="pending_msg" position="after">
                <div class="o_row" colspan="2" invisible="custom_mode != 'wire_transfer'">
                    <button string="Reload Pending Message" type="object" name="action_recompute_pending_msg" class="oe_link ms-0 ps-0" icon="fa-refresh"/>
              </div>
            </field>
            <field name="done_msg" position="attributes">
                <attribute name="invisible" separator="or" add="code == 'customer_account'"/>
            </field>
            <field name="cancel_msg" position="attributes">
                <attribute name="invisible" separator="or" add="code == 'customer_account'"/>
            </field>
			<a name="action_view_payment_methods" position="after">
				<group invisible="code != 'customer_account'">
					<field name="split_transactions"/>
					<field name="journal_id" required="not split_transactions and state != 'disabled'" placeholder="Leave empty to use the receivable account of customer" />
				</group>
            </a>
        </field>
    </record>

    <record id="view_account_payment_search_inherit" model='ir.ui.view'>
		<field name="name">view.account.payment.search.inherit</field>
		<field name="model">account.payment</field>
		<field name="inherit_id" ref="account.view_account_payment_search"/>
		<field name="arch" type="xml">
			<filter name="inbound_filter" position="before">
				<filter
					string="Cash Only" name="cash_only"
					domain="[
						('partner_type', '=', 'customer'),
						('is_downpayment', '=', True),
						('payment_method_line_id.name', '=', 'Cash')]"
					/>
				<filter
					string="Cash Not Batched" name="cash_not_batched"
					domain="[
						('partner_type', '=', 'customer'),
						('is_downpayment', '=', True),
						('payment_method_line_id.name', '=', 'Cash'),
						('batch_payment_id', '=', False)]"
					/>
				<separator/>
			</filter>
 		</field>
	</record>
    <!-- <record id="view_account_payment_tree_inherit" model="ir.ui.view">
		<field name="name">view.account.payment.tree.inherit</field>
		<field name="model">account.payment</field>
		<field name="inherit_id" ref="modula_downpayment.view_account_payment_tree_inherit"/>
		<field name="arch" type="xml">
			<field name="remaining_amount" position="attributes">
				<attribute name="optional">show</attribute>
			</field>
			<field name="name" position="after">
				<field name="partner_id" position="move"/>
			</field>
 		</field>
	</record> -->

	<record id="view_account_payment_form" model="ir.ui.view">
        <field name="name">view.payment.form</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="modula_downpayment.view_account_payment_form_inherit"/>
        <field name="arch" type="xml">
            <field name="payment_transaction_id" position="after">
                <field name="receipt_number" readonly="payment_transaction_id != False"/>
                <field name="can_edit" invisible="1"/>
            </field>
            <header position="inside">
                <button name="action_reverse_and_reconcile" type="object" class="btn btn-secondary" string="Reverse and Reconcile" invisible="state != 'in_process'" groups="sales_team.group_sale_manager"/>
            </header>
            <button name="action_draft" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </button>
            <button name="action_post" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </button>
            <button name="action_cancel" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </button>
            <field name="branch_id" position="attributes">
                <attribute name="readonly">not can_edit</attribute>
            </field>
            <field name="sale_order_id" position="attributes">
                <attribute name="readonly">not can_edit</attribute>
            </field>
            <field name="memo" position="attributes">
                <attribute name="readonly">not can_edit</attribute>
            </field>
            <field name="line_allocation_ids" position="attributes">
                <attribute name="readonly">not can_edit</attribute>
            </field>
        </field>
    </record>

	<record id="payment_transaction_form" model="ir.ui.view">
        <field name="name">payment.transaction.form</field>
        <field name="model">payment.transaction</field>
        <field name="inherit_id" ref="payment.payment_transaction_form"/>
        <field name="arch" type="xml">
			<xpath expr="//field[@name='payment_method_id']" position="after">
                <field name="surcharge_percentage" invisible="provider_code not in ('eftpos', 'eftpos_amex')"/>
                <field name="surcharge_amount" invisible="provider_code not in ('eftpos', 'eftpos_amex')"/>
            </xpath>
            <field name="provider_reference" position="after">
                <field name="provider_code" invisible="1"/>
                <field name="payment_ref" invisible="provider_code not in ['cash', 'eftpos', 'bank_transfer', 'finance']"/>
            </field>
        </field>
    </record>

	<record id="payment_method_form_inherit" model="ir.ui.view">
        <field name="name">payment.method.form.inherit</field>
        <field name="model">payment.method</field>
        <field name="inherit_id" ref="payment.payment_method_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='active']" position="after">
                <field name="code" invisible="1"/>
                <label for="surcharge_percentage" invisible="is_primary and code != 'amex'"/>
                <div class="o_row" invisible="is_primary and code != 'amex'" style="width: 20px !important;">
                    <field name="surcharge_percentage" nolabel="1" widget="percentage"/>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
