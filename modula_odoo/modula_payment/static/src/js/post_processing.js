/** @odoo-module **/

import paymentPostProcessing from '@payment/js/post_processing';
import PaymentForm from '@payment/js/payment_form';
import { loadJS } from "@web/core/assets";
import { _t } from "@web/core/l10n/translation";

PaymentForm.include({
    events: Object.assign({}, PaymentForm.prototype.events, {
        'click .enable_surcharge_eftpos': '_enableSurchargeOptionEftpos',
        'click .btn-surcharge-eftpos': '_selectSurchargeOptionEftpos',
        'change #eftpos_payment_option': '_selectPaymentMethodOption',
        // 'focusout .eftpos-input-wrap input': 'checkRequirePayment',
        // 'change #eftpos_card_number': '_get_credit_card_type',
        // 'change #eftpos_ccv': '_check_eftpos_ccv',
        // 'change #eftpos_expiration_month': '_checkExpiryDate',
        // 'change #eftpos_expiration_year': '_checkExpiryDate',
    }),

    /**
     * @override
     */
    async start() {
        this.surcharge_amount = 0.0;
        this.payment_method_code = '';
        this.is_valid_cvv = false;
        this.is_valid_date = false;
        this.finance_pm_id = false;
        this.card_number = '';
        this._applyInputMask();
        await this._super(...arguments);
        this.disable_payment_button();
    },

    // #=== EVENT HANDLERS ===#

    disable_payment_button () {

        const checkedRadio = this.el.querySelector('input[name="o_payment_radio"]:checked');
        if (checkedRadio && this._getProviderCode(checkedRadio) === 'worldline') {
            this._enableButton();
        } else {
            this._disableButton();
            this.checkRequirePayment();
        }
    },

    // BANK TRANSFER

    bankTransferCheckRequirePayment () {
        $(document).ready(function() {
            $('div.bank_transfer-input-wrap input').focusout(function() {
                var paymentButton = $('[name="o_payment_submit_button"]');
                if ($(this).val() == '') {
                    if ($(this).attr('id') === 'bank_transfer_amount') {
                        $('span#bank_transfer_amount_required_error')[0].classList.remove('d-none');

                        paymentButton.attr('disabled', true);
                    }

                    if ($(this).attr('id') === 'bank_transfer_reference') {
                        $('span#bank_transfer_reference_required_error')[0].classList.remove('d-none');
                        paymentButton.attr('disabled', true);
                    }
                } else {
                    if ($(this).attr('id') === 'bank_transfer_amount') {
                        $('span#bank_transfer_amount_required_error')[0].classList.add('d-none');

                        if ($($('input#bank_transfer_reference')[0]).val()) {
                            paymentButton.attr('disabled', false);
                        }
                    }

                    if ($(this).attr('id') === 'bank_transfer_reference') {
                        $('span#bank_transfer_reference_required_error')[0].classList.add('d-none');

                        if ($($('input#bank_transfer_amount')[0]).val()) {
                            paymentButton.attr('disabled', false);
                        }
                    }
                }
            }).trigger("focusout");
        });
    },

    bankTransferPaymentAmount(code) {
        const bankTransferContainer = this.el.querySelector('[name="bank_transfer_cash_payment"]');
        if (code === 'bank_transfer') {
            bankTransferContainer?.classList.remove('d-none'); // Show the cash payment amount field.
        } else {
            bankTransferContainer?.classList.add('d-none'); // Hide the cash payment amount field.
        }
    },

    // CASH

    cashCheckRequirePayment () {
        $(document).ready(function() {
            $('div.cash-input-wrap input').focusout(function() {
                var paymentButton = $('[name="o_payment_submit_button"]');
                if ($(this).val() == '') {
                    if ($(this).attr('id') === 'cash_amount') {
                        $('span#cash_amount_required_error')[0].classList.remove('d-none');

                        paymentButton.attr('disabled', true);
                    }

                    if ($(this).attr('id') === 'cash_reference') {
                        $('span#cash_reference_required_error')[0].classList.remove('d-none');
                        paymentButton.attr('disabled', true);
                    }
                } else {
                    if ($(this).attr('id') === 'cash_amount') {
                        $('span#cash_amount_required_error')[0].classList.add('d-none');

                        if ($($('input#cash_reference')[0]).val()) {
                            paymentButton.attr('disabled', false);
                        }
                    }

                    if ($(this).attr('id') === 'cash_reference') {
                        $('span#cash_reference_required_error')[0].classList.add('d-none');

                        if ($($('input#cash_amount')[0]).val()) {
                            paymentButton.attr('disabled', false);
                        }
                    }
                }
            }).trigger("focusout");
        });
    },

    cashPaymentAmount(code) {
        const cashContainer = this.el.querySelector('[name="cash_cash_payment"]');
        if (code === 'cash') {
            cashContainer?.classList.remove('d-none'); // Show the cash payment amount field.
        } else {
            cashContainer?.classList.add('d-none'); // Hide the cash payment amount field.
        }
    },

    // EFTPOS


    _applyInputMask() {
        if (typeof Inputmask === 'undefined') {
            console.error("Inputmask is not loaded");
        }
        Inputmask('9999 9999 9999 9999', { placeholder: ' ' }).mask('#eftpos_card_number');
        Inputmask('99').mask('#eftpos_expiration_month');
        Inputmask('9999').mask('#eftpos_expiration_year');
    },

    checkRequirePayment () {
        $(document).ready(function() {
            // const is_valid_cvv = this.is_valid_cvv;
            // const is_valid_date = this.is_valid_date;
            const payment_method_code = this.payment_method_code;
            var activepaymentButton = false;
            var paymentButton = $('[name="o_payment_submit_button"]');

            // // Check if reference is empty for EFTPOS
            if (!$('#eftpos_reference').val()) {
                $('span#reference_required_error').removeClass('d-none');
                paymentButton.attr('disabled', true);
                activepaymentButton = false;
            } else {
                $('span#reference_required_error').addClass('d-none');
                paymentButton.attr('disabled', false);
                activepaymentButton = true;
            }
            if (payment_method_code !== 'eftpos' && activepaymentButton) {
                if ($('select#eftpos_payment_option option:selected').data('payment-method-eftpos') !== 'eftpos') {
                    if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
                        if ($('div.select-surcharge-eftpos div').hasClass('active')) {
                            paymentButton.attr('disabled', false);
                            activepaymentButton = true;
                        } else {
                            paymentButton.attr('disabled', true);
                            activepaymentButton = false;
                        }
                    } else {
                        // Enable or disable button payment
                        paymentButton.attr('disabled', false);
                        activepaymentButton = true;
                    }
                }
            }

            // if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
            //     // Enable or disable button payment
            //     $('div.select-surcharge-eftpos div').each(function() {
            //         if ($(this).hasClass('active') && is_valid_cvv && is_valid_date) {
            //             $('span#ccv_invalid_error')[0].classList.add('d-none');
            //             $('span#expiry_error')[0].classList.add('d-none');
            //             activepaymentButton = true;
            //         }
            //     })
            // } else {
            //     // Enable or disable button payment
            //     if (payment_method_code == 'eftpos') {
            //         activepaymentButton = true;
            //     } else {
            //         if (is_valid_cvv && is_valid_date) {
            //             $('span#ccv_invalid_error')[0].classList.add('d-none');
            //             $('span#expiry_error')[0].classList.add('d-none');
            //             activepaymentButton = true;
            //         }
            //     }
            // }
            $('div.eftpos-input-wrap input').focusout(function() {
                if ($(this).val() == '') {
                    if ($(this).attr('id') === 'eftpos_reference') {
                        $('span#reference_required_error')[0].classList.remove('d-none');
                        paymentButton.attr('disabled', true);
                    }
                    if ($(this).attr('id') === 'eftpos_amount') {
                        $('span#amount_required_error')[0].classList.remove('d-none');
                        paymentButton.attr('disabled', true);
                    }
                    // if ($(this).attr('id') === 'eftpos_name_card') {
                    //     $('span#name_card_required_error')[0].classList.remove('d-none');
                    //     paymentButton.attr('disabled', true);
                    // }
                    // if ($(this).attr('id') === 'eftpos_ccv') {
                    //     $('span#ccv_required_error')[0].classList.remove('d-none');
                    //     $('span#ccv_invalid_error')[0].classList.add('d-none');
                    //     paymentButton.attr('disabled', true);
                    // }
                    // if ($(this).attr('id') === 'eftpos_expiration_month') {
                    //     $('span#expiration_month_required_error')[0].classList.remove('d-none');
                    //     $('span#expiry_error')[0].classList.add('d-none');
                    //     paymentButton.attr('disabled', true);
                    // }
                    // if ($(this).attr('id') === 'eftpos_expiration_year') {
                    //     $('span#expiration_year_required_error')[0].classList.remove('d-none');
                    //     $('span#expiry_error')[0].classList.add('d-none');
                    //     paymentButton.attr('disabled', true);
                    // }
                    // if ($(this).attr('id') === 'eftpos_card_number') {
                    //     $('span#card_number_required_error')[0].classList.remove('d-none');
                    //     paymentButton.attr('disabled', true);
                    // }
                } else {
                    // Hide error message when input has value
                    if ($(this).attr('id') === 'eftpos_reference') {
                        $('span#reference_required_error')[0].classList.add('d-none');
                        paymentButton.attr('disabled', false);
                        if ($('select#eftpos_payment_option option:selected').data('payment-method-eftpos') !== 'eftpos') {
                            if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
                                if ($('div.select-surcharge-eftpos div').hasClass('active')) {
                                    paymentButton.attr('disabled', false);
                                } else {
                                    paymentButton.attr('disabled', true);
                                }
                            } else {
                                // Enable or disable button payment
                                paymentButton.attr('disabled', false);
                            }
                        }
                        // activepaymentButton = true;
                    }
                    if ($(this).attr('id') === 'eftpos_amount') {
                        $('span#amount_required_error')[0].classList.add('d-none');
                        paymentButton.attr('disabled', false);
                        // activepaymentButton = true;
                    }
                    // if ($(this).attr('id') === 'eftpos_name_card') {
                    //     $('span#name_card_required_error')[0].classList.add('d-none');
                    //     paymentButton.attr('disabled', false);
                    // }
                    // if ($(this).attr('id') === 'eftpos_ccv') {
                    //     $('span#ccv_required_error')[0].classList.add('d-none');
                    // }
                    // if ($(this).attr('id') === 'eftpos_card_number') {
                    //     $('span#card_number_required_error')[0].classList.add('d-none');
                    // }
                    // if ($(this).attr('id') === 'eftpos_expiration_month') {
                    //     $('span#expiration_month_required_error')[0].classList.add('d-none');
                    // }
                    // if ($(this).attr('id') === 'eftpos_expiration_year') {
                    //     $('span#expiration_year_required_error')[0].classList.add('d-none');
                    // }

                    // Check if all required fields are filled based on payment method
                    // if ($('select#eftpos_payment_option option:selected').data('payment-method-eftpos') === 'eftpos') {
                    //     // For EFTPOS payment, only check reference and amount
                    //     if ($('#eftpos_reference').val() && $('#eftpos_amount').val()) {
                    //         activepaymentButton = true;
                    //         paymentButton.attr('disabled', false);
                    //     }
                    // } else {
                        // For credit card payment, check all required fields
                        // if ($('#eftpos_reference').val() &&
                        //     $('#eftpos_amount').val()
                            // $('#eftpos_name_card').val() &&
                            // $('#eftpos_ccv').val() &&
                            // $('#eftpos_card_number').val() &&
                            // $('#eftpos_expiration_month').val() &&
                            // $('#eftpos_expiration_year').val()
                        // ) {
                        //     activepaymentButton = true;
                        //     paymentButton.attr('disabled', false);
                        // }
                    // }
                    // if ($('select#eftpos_payment_option option:selected').data('payment-method-eftpos') !== 'eftpos') {
                    //     if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
                    //         if ($('div.select-surcharge-eftpos div').hasClass('active')) {
                    //             activepaymentButton = true;
                    //         } else {
                    //             activepaymentButton = false;
                    //         }
                    //     } else {
                    //         // Enable or disable button payment
                    //         activepaymentButton = true;
                    //     }
                    // }
                }
            }).trigger("focusout");

            if (activepaymentButton) {
                paymentButton.attr('disabled', false);
            } else {
                paymentButton.attr('disabled', true);
            }
        });
    },

    // #=== EVENT HANDLERS ===#
    /**
     * Check validity of expiration month and year
     * @param {Event} ev
     */
    _checkExpiryDate(ev) {
        const month = parseInt($('#eftpos_expiration_month').val(), 10);
        const year = parseInt($('#eftpos_expiration_year').val(), 10);
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();

        let isValid = true;

        if (month < 1 || month > 12) {
            isValid = false;
        }

        if (year < currentYear || (year === currentYear && month < currentMonth)) {
            isValid = false;
        }

        if (isValid) {
            $('#expiry_error').addClass('d-none');
        } else {
            $('#expiry_error').removeClass('d-none');
        }
        this.is_valid_date = isValid;

        this.checkRequirePayment();
    },

    /**
     * Check the CVV code based on the card type.
     * @param {Event} ev - The event object triggered when the CVV input field changes.
     */
    _check_eftpos_ccv(ev) {
        const cardNumber = this.card_number;
        const cvv = ev.target.value;
        if (cardNumber && cvv) {
            if (this.isCVV(cardNumber, cvv)) {
                $('span#ccv_invalid_error')[0].classList.add('d-none');
                this.is_valid_cvv = true
            } else {
                $('span#ccv_invalid_error')[0].classList.remove('d-none');
                this.is_valid_cvv = false
            }
        } else {
            this.is_valid_cvv = false
        }
        this.checkRequirePayment();
    },

    /**
     * Check if CVV is valid based on the card number.
     * @param {string} cardNumber - The card number.
     * @param {string|number} cvv - The CVV entered by the user.
     * @return {boolean} - Returns true if the CVV is valid, otherwise false.
     */
    isCVV(cardNumber, cvv) {
        if (cardNumber === null || cvv === null) {
            return false;
        }

        const value = cvv.toString();

        // Kiểm tra tính hợp lệ của cardNumber và cvv
        if (!this.isStr(cardNumber) || !this.isStr(value)) {
            return false;
        }

        // Kiểm tra loại thẻ và độ dài CVV
        if (this.isAmericanExpressCardNumber(cardNumber)) {
            // American Express: CVV phải có 4 ký tự
            if (value.length === 4) {
                return true;
            }
        } else {
            // Các thẻ khác: CVV phải có 3 ký tự
            if (value.length === 3) {
                return true;
            }
        }

        return false;
    },

    /**
     * Check if the card is an American Express card.
     * @param {string} cardNumber - The card number.
     * @return {boolean} - Returns true if the card is American Express, otherwise false.
     */
    isAmericanExpressCardNumber(cardNumber) {
        if (typeof cardNumber !== 'string' || cardNumber.length !== 15) {
            return false;
        }

        // Kiểm tra prefix 34 hoặc 37 cho American Express
        const prefix = cardNumber.slice(0, 2);
        return prefix === '34' || prefix === '37';
    },

    /**
     * Helper function to check if a value is a string.
     * @param {any} value - The value to check.
     * @return {boolean} - Returns true if the value is a string, otherwise false.
     */
    isStr(value) {
        return typeof value === 'string' || value instanceof String;
    },

    /**
     * Check if the card number is a valid MasterCard (16 digits, prefix between 50 and 55).
     * @param {string} cardNumber
     * @return {boolean} true if it's a valid MasterCard, false otherwise.
     */
    isMasterCard(cardNumber) {
        if (typeof cardNumber !== 'string' || cardNumber.length !== 16) {
            return false;
        }

        const prefix = Math.floor(+cardNumber.slice(0, 2));
        if (prefix < 50 || prefix > 55) {
            return false;
        }

        return this.isCreditCardNumber(cardNumber);
    },

    /**
     * Check if the card number is a valid Visa card (13 or 16 digits, starts with 4).
     * @param {string} cardNumber
     * @return {boolean} true if it's a valid Visa card, false otherwise.
     */
    isVisa(cardNumber) {
        if (typeof cardNumber !== 'string' || (cardNumber.length !== 13 && cardNumber.length !== 16)) {
            return false;
        }

        if (cardNumber.slice(0, 1) !== '4') {
            return false;
        }

        return this.isCreditCardNumber(cardNumber);
    },

    /**
     * Check if the card number is a valid American Express card (15 digits, prefix 34 or 37).
     * @param {string} cardNumber
     * @return {boolean} true if it's a valid American Express card, false otherwise.
     */
    isAmericanExpressCardNumber(cardNumber) {
        if (typeof cardNumber !== 'string' || cardNumber.length !== 15) {
            return false;
        }

        const prefix = Math.floor(+cardNumber.slice(0, 2));
        if (prefix !== 34 && prefix !== 37) {
            return false;
        }

        return this.isCreditCardNumber(cardNumber);
    },

    /**
     * Check if the card number is valid according to the Luhn algorithm.
     * @param {string} cardNumber
     * @return {boolean} true if it's a valid credit card number, false otherwise.
     */
    isCreditCardNumber(cardNumber) {
        if (typeof cardNumber === 'string' && cardNumber.trim() !== '') {
            const ary = cardNumber.split('');
            let cnt = 1;

            // Apply Luhn algorithm
            for (let i = ary.length - 1; i >= 0; i--, cnt++) {
                if (cnt % 2 === 0) {
                    ary[i] = +ary[i] * 2;
                    if (ary[i] > 9) {
                        ary[i] -= 9;  // If the number after multiplication is greater than 9, subtract 9
                    }
                } else {
                    ary[i] = +ary[i];
                }
            }

            // Calculate the sum
            const sum = ary.reduce((acc, num) => acc + num, 0);

            // Check if the sum modulo 10 is 0 (Luhn algorithm)
            return sum % 10 === 0;
        }
        return false;
    },

    /**
     * Determine the type of the credit card when the value in the card number field changes.
     * @param {Event} ev
     * @return {void}
     */
    _get_credit_card_type(ev) {
        $('div.btn-surcharge-visa')[0].classList.remove('d-none');
        $('div.btn-surcharge-amex')[0].classList.remove('d-none');
        $('div.btn-surcharge-master')[0].classList.remove('d-none');
        $('div.btn-surcharge-other')[0].classList.remove('d-none');
        const cardNumber = ev.target.value.trim().replace(/\s+/g, '').replace(/[^0-9]/g, '');
        const isMasterCard = this.isMasterCard(cardNumber);
        const isVisa = this.isVisa(cardNumber);
        const isAmEx = this.isAmericanExpressCardNumber(cardNumber);
        var amount = parseFloat(this.paymentContext.amount);
        this.card_number = cardNumber;

        let payment_method_code = this.payment_method_code;
        if (cardNumber) {
            $('input[name="enableSurchargeEftpos"]').attr('checked', 'true');
        }
        if (isMasterCard) {
            payment_method_code = 'eftposmastercard';
            $('div.btn-surcharge-visa')[0].classList.add('d-none');
            $('div.btn-surcharge-amex')[0].classList.add('d-none');
            $('div.btn-surcharge-other')[0].classList.add('d-none');

            // You can add additional handling for MasterCard here
        } else if (isVisa) {
            payment_method_code = 'eftposvisa';
            $('div.btn-surcharge-master')[0].classList.add('d-none');
            $('div.btn-surcharge-amex')[0].classList.add('d-none');
            // Handle Visa card here
        } else if (isAmEx) {
            payment_method_code = 'eftposamex';
            $('div.btn-surcharge-master')[0].classList.add('d-none');
            $('div.btn-surcharge-visa')[0].classList.add('d-none');
            $('div.btn-surcharge-other')[0].classList.add('d-none');
            // Handle American Express card here
        } else {
            payment_method_code = 'eftpos_visa_mastercard';
            $('input#eftpos_surcharge_amount').val('0.00');
            $('input#eftpos_amount').val(amount.toFixed(2));
            $('input[name="enableSurchargeEftpos"]').removeAttr('checked');
        }
        this.payment_method_code = payment_method_code;
        $('div.image-card span').each(function() {
            if ($(this)[0].dataset['logo_pm_code'] === payment_method_code) {
                $(this).addClass('border_img');
                $(this)[0].click();
            }else {
                $(this).removeClass('border_img');
            }
        });
        $('div.select-surcharge-eftpos div').each(function() {
            if ($(this)[0].dataset['pmName'] === payment_method_code) {
                $(this)[0].click()
            }else {
                $(this).removeClass('active');
            }
        });
        this._check_eftpos_ccv({target: $('input#eftpos_ccv')[0]});
        this.checkRequirePayment();
    },

    /**
     * @private
     * @param {Event} ev
     * @return {void}
     */
    _selectSurchargeOptionEftpos(ev) {
        ev.preventDefault();
        var innerText = ev.target.innerText;
        var amount = parseFloat(this.paymentContext.amount);
        var percentage = parseFloat(innerText.match(/(\d+\.\d+)%?/)[1]);
        var surchargeAmount = 0.0;
        // Remove the "active" class from all buttons
        $('div.btn-surcharge-eftpos').removeClass('active');
        if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
            // Add the "active" class to the clicked button
            $(ev.currentTarget).addClass('active');
            this.finance_pm_id = ev.currentTarget.getAttribute('data-pm-id');
            // Calculate surcharge amount
            surchargeAmount = (amount * (percentage / 100)).toFixed(2);
            this.surcharge_amount = surchargeAmount;
            var totalAmount = (amount + parseFloat(surchargeAmount)).toFixed(2);

            // update value into input field
            $('input#eftpos_surcharge_amount').val(surchargeAmount);
            $('input#eftpos_amount').val(totalAmount);

            // Enable or disable button payment
            var paymentButton = $('[name="o_payment_submit_button"]');
            paymentButton.attr('disabled', false);
        } else {
            $('input#eftpos_surcharge_amount').val('0.00');
            $('input#eftpos_amount').val(amount.toFixed(2));
        }
        this.checkRequirePayment();
    },

    /**
     * @private
     * @param {Event} ev
     * @return {void}
     */
    _selectPaymentMethodOption(ev) {
        const selectedIndex = ev.target.selectedIndex;
        const selectedOption = ev.target.options[selectedIndex];

        this.payment_method_code = selectedOption.dataset['paymentMethodEftpos']
        var amount = parseFloat(this.paymentContext.amount);
        if (this.payment_method_code === 'eftpos') {
            $('div#header_eftpos')[0].classList.remove('d-none');
            $('div#surcharge_percentage')[0].classList.add('d-none');
            $('div#eftpos_surcharge_wrap')[0].classList.add('d-none');
            // $('div.credit-card-wrap')[0].classList.add('d-none');
            $('div#eftpos_amount_wrap')[0].classList.remove('col-6');
            $('input#eftpos_amount').removeAttr('readonly');
            $('input[name="enableSurchargeEftpos"]').removeAttr('checked');
        } else {
            $('input[name="enableSurchargeEftpos"]').attr('checked', 'true');
            $('div#header_eftpos')[0].classList.add('d-none');
            $('div#surcharge_percentage')[0].classList.remove('d-none');
            $('div#eftpos_surcharge_wrap')[0].classList.remove('d-none');
            // $('div.credit-card-wrap')[0].classList.remove('d-none');
            $('div#eftpos_amount_wrap')[0].classList.remove('col-12');
            $('div#eftpos_amount_wrap')[0].classList.add('col-6');
            // $('input#eftpos_amount')[0].classList.remove('col-6');
            $('input#eftpos_amount').attr('readonly', 'readonly');
            $('input#eftpos_surcharge_amount').val('0.00');
        }
        $('input#eftpos_amount').val(amount.toFixed(2));
        const payment_method_code = this.payment_method_code;
        $('div.select-surcharge-eftpos div').each(function() {
            if ($(this)[0].dataset['pmName'] === payment_method_code) {
                // select_function = $(this)[0]
                $(this)[0].click()
            }else {
                $(this).removeClass('active');
            }
        });

        this.checkRequirePayment();
    },

    /**
     * @private
     * @param {Event} ev
     * @return {void}
     */
    _enableSurchargeOptionEftpos(ev) {
        var amount = parseFloat(this.paymentContext.amount);
        var paymentButton = $('[name="o_payment_submit_button"]');
        if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
            // Enable or disable button payment
            paymentButton.attr('disabled', true);
        } else {
            // Enable or disable button payment
            paymentButton.attr('disabled', false);
            // Reset surcharge amount
            // $('input#eftpos_surcharge_amount').val('0.00');
            // $('input#eftpos_amount').val(amount.toFixed(2));
        }
        // Disable the surcharge buttons
        // const payment_method_code = this.payment_method_code;
        // $('div.select-surcharge-eftpos div').each(function() {
        //     if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
        //         if ($(this)[0].dataset['pmName'] === payment_method_code) {
        //             // select_function = $(this)[0]
        //             $(this)[0].click()
        //         }
        //     } else {
        //         $(this).removeClass('active');
        //     }
        // });
        // Disable the surcharge buttons
        $('div.select-surcharge-eftpos div').each(function() {
            $(this).removeClass('active');
        });
        // Reset surcharge amount
        $('input#eftpos_surcharge_amount').val('0.00');
        $('input#eftpos_amount').val(amount.toFixed(2));

    },

    eftposPaymentAmount(code) {
        const eftposContainer = this.el.querySelector('[name="eftpos_cash_payment"]');
        if (code === 'eftpos') {
            eftposContainer?.classList.remove('d-none'); // Show the eftpos payment amount field.
        } else {
            eftposContainer?.classList.add('d-none'); // Hide the eftpos payment amount field.
        }
    },

    // Finance

    checkRequirePaymentFinance () {
        $(document).ready(function() {
            $('div.finance-input-wrap input').focusout(function() {
                var paymentButton = $('[name="o_payment_submit_button"]');
                if ($(this).val() == '') {
                    if ($(this).attr('id') === 'finance_amount') {
                        $('span#finance_amount_required_error')[0].classList.remove('d-none');

                        paymentButton.attr('disabled', true);
                    }

                    if ($(this).attr('id') === 'finance_reference') {
                        $('span#finance_reference_required_error')[0].classList.remove('d-none');
                        paymentButton.attr('disabled', true);
                    }
                } else {
                    if ($(this).attr('id') === 'finance_amount') {
                        $('span#finance_amount_required_error')[0].classList.add('d-none');

                        if ($($('input#finance_reference')[0]).val()) {
                            paymentButton.attr('disabled', false);
                        }
                    }

                    if ($(this).attr('id') === 'finance_reference') {
                        $('span#finance_reference_required_error')[0].classList.add('d-none');

                        if ($($('input#finance_amount')[0]).val()) {
                            paymentButton.attr('disabled', false);
                        }
                    }
                }
            }).trigger("focusout");
        });
    },

    financePaymentAmount(code) {
        const financeContainer = this.el.querySelector('[name="finance_cash_payment"]');
        if (code === 'finance') {
            financeContainer?.classList.remove('d-none'); // Show the finance payment amount field.
        } else {
            financeContainer?.classList.add('d-none'); // Hide the finance payment amount field.
        }
    },

    // #=== PAYMENT FLOW ===#

    /**
     * Perform some validations for donations before processing the payment flow.
     *
     * @override method from @payment/js/payment_form
     * @private
     * @param {Event} ev
     * @return {void}
     */
    async _selectPaymentOption(ev) {
        await this._super(...arguments);
        var code = ev.target.getAttribute('data-provider-code');
        this.payment_method_code = ev.target.getAttribute('data-payment-method-code');

        this.cashPaymentAmount(code);
        this.bankTransferPaymentAmount(code);
        this.eftposPaymentAmount(code);
        this.financePaymentAmount(code);
        if (code === 'cash') {
            this.cashCheckRequirePayment();
        } else if (code === 'eftpos') {
            this.checkRequirePayment();
        } else if (code === 'finance') {
            this.checkRequirePaymentFinance();
        } else if (code === 'bank_transfer') {
            this.bankTransferCheckRequirePayment();
        }
    },

    /**
     * Prepare the params for the RPC to the transaction route.
     *
     * @override method from @payment/js/payment_form
     * @private
     * @return {object} The extended transaction route params.
     */
    _prepareTransactionRouteParams() {
        let transactionRouteParams = this._super(...arguments);
        transactionRouteParams.payment_method_id = this.paymentContext.paymentMethodId;
        const $form = $('#o_payment_form');

        if (this.paymentContext.providerCode === 'cash') {
            const cash_amount = $form.find('#cash_amount').val();
            const cash_reference = $form.find('#cash_reference').val();
            transactionRouteParams.amount = cash_amount;
            transactionRouteParams.payment_ref = cash_reference;
        } else if (this.paymentContext.providerCode === 'bank_transfer') {
            const bank_transfer_amount = $form.find('#bank_transfer_amount').val();
            const bank_transfer_reference = $form.find('#bank_transfer_reference').val();
            transactionRouteParams.amount = bank_transfer_amount;
            transactionRouteParams.payment_ref = bank_transfer_reference;
        } else if (this.paymentContext.providerCode === 'eftpos') {
            const reference = $form.find('#eftpos_reference').val();
            let eftpos_pm_id = $form.find('#eftpos_payment_option').val();
            if ((this.payment_method_code !== 'eftpos') && this.finance_pm_id) {
                eftpos_pm_id = this.finance_pm_id
            }
            transactionRouteParams.payment_ref = reference;
            if ($('input[name="enableSurchargeEftpos"]').is(':checked')) {
                transactionRouteParams.surcharge_amount = this.surcharge_amount;
                transactionRouteParams.amount = parseFloat(transactionRouteParams.amount) + parseFloat(this.surcharge_amount);
                transactionRouteParams.is_surcharge = true;
            } else {
                transactionRouteParams.is_surcharge = false;
            }
            transactionRouteParams.payment_method_id = eftpos_pm_id;
        } else if (this.paymentContext.providerCode === 'finance') {
            const finance_amount = $form.find('#finance_amount').val();
            const finance_reference = $form.find('#finance_reference').val();
            const finance_pm_id = $form.find('#finance_payment_option').val();
            transactionRouteParams.amount = finance_amount;
            transactionRouteParams.payment_ref = finance_reference;
            transactionRouteParams.payment_method_id = finance_pm_id;
        }

        return transactionRouteParams;
    },

});

paymentPostProcessing.include({
    /**
     * Don't wait for the transaction to be confirmed before redirecting customers to the
     * landing route because custom transactions remain in the state 'pending' forever.
     *
     * @override method from `@payment/js/post_processing`
     * @param {string} providerCode - The code of the provider handling the transaction.
     */
    _getFinalStates(providerCode) {
        const finalStates = this._super(...arguments);
        if (providerCode === 'customer_account') {
            finalStates.push('pending');
        }
        return finalStates;
    }

});
