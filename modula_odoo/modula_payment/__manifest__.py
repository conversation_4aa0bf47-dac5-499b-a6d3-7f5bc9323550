# -*- coding: utf-8 -*-
{
    "name": "Modula - Payment",
    "version": "********.0",
    "category": "Payment",
    "description": """
    """,
    "author": "ITMS Group",
    "website": "http://www.itmsgroup.com.au",
    "depends": [
        "payment_custom",
        "modula_downpayment",
        "modula_sale",
        "account_accountant",
        "account_batch_payment",
        "payment_authorize",
        "modula_branch",
    ],
    "license": "LGPL-3",
    "sequence": 0,
    "data": [
        # ============================================================
        # SECURITY SETTING - GROUP - PROFILE
        # ============================================================
        # 'security/',
        # ============================================================
        # DATA
        # ============================================================
        # 'data/',
        "data/payment_template.xml",
        "data/payment_data.xml",
        "data/payment_term_update_data.xml",
        "data/ir_cron.xml",
        # ============================================================
        # VIEWS
        # ============================================================
        # 'views/',
        "views/payment_custom_templates.xml",
        "views/bank_rec_widget_views.xml",
        "views/res_partner_views.xml",
        "views/account_move_views.xml",
        "views/account_payment_views.xml",
        "views/res_config_settings_views.xml",
        "views/account_payment_term_views.xml",
        # ============================================================
        # MENU
        # ============================================================
        # 'menu/',
    ],
    "assets": {
        "web.assets_frontend": [
            "modula_payment/static/src/js/post_processing.js",
            "modula_payment/static/src/scss/card.scss",
        ],
    },
    "post_init_hook": "post_init_hook",
    "uninstall_hook": "uninstall_hook",
    "test": [],
    "demo": [],
    "installable": True,
    "application": True,
}
