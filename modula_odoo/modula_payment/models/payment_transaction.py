# Part of Odoo. See LICENSE file for full copyright and licensing details.

import datetime
import logging

from odoo import Command, _, api, fields, models
from odoo.addons.modula_payment.controllers.main import CustomerAccountController
from odoo.exceptions import UserError, ValidationError
from werkzeug import urls

_logger = logging.getLogger(__name__)


class PaymentTransaction(models.Model):
    _inherit = "payment.transaction"

    payment_ref = fields.Char(string="Payment reference")
    surcharge_amount = fields.Monetary(
        string="Surcharge Amount", currency_field="currency_id"
    )
    surcharge_percentage = fields.Float(
        string="Surcharge Percentage", related="payment_method_id.surcharge_percentage"
    )
    is_surcharge = fields.Boolean(string="Is Surcharge")
    payment_surcharge_id = fields.Many2one(
        string="Surcharge Payment", comodel_name="account.payment", readonly=True
    )

    def _get_specific_rendering_values(self, processing_values):
        """Override of payment to return custom-specific rendering values.

        Note: self.ensure_one() from `_get_processing_values`

        :param dict processing_values: The generic and specific processing values of the transaction
        :return: The dict of provider-specific processing values
        :rtype: dict
        """
        res = super()._get_specific_rendering_values(processing_values)
        IrParamSudo = self.env["ir.config_parameter"].sudo()
        base_url = IrParamSudo.get_param("web.base.url")
        redirect_amount = str(int(float(self.amount) * 100)) if self.amount else "00"
        timestamp = fields.Datetime.now().strftime("%Y%m%d%H%M%S")
        values = {
            "redirect_amount": redirect_amount,
            "timestamp": timestamp,
            "return_url_text": "Complete Payment",
            "return_url_target": "parent",
            "title": "%s Payment Page" % (self.company_id.name),
            "partner_email": self.partner_email,
            "partner_country_name": self.partner_country_id
            and self.partner_country_id.name
            or "",
            "reference": self.reference,
            "currency": self.currency_id.name,
        }

        if self.provider_code == "customer_account":
            return {
                "api_url": CustomerAccountController._process_url,
                "reference": self.reference,
            }
        elif self.provider_code == "cash":
            cancel_url = urls.url_join(base_url, "/payment/cash/cancel")
            values.update(
                {
                    "return_url": urls.url_join(base_url, "/payment/cash/validate"),
                    "cancel_url": cancel_url,
                    "api_url": urls.url_join(base_url, "/payment/cash/progress"),
                }
            )

            return values
        elif self.provider_code == "bank_transfer":
            cancel_url = urls.url_join(base_url, "/payment/bank_transfer/cancel")
            values.update(
                {
                    "return_url": urls.url_join(
                        base_url, "/payment/bank_transfer/validate"
                    ),
                    "cancel_url": cancel_url,
                    "api_url": urls.url_join(
                        base_url, "/payment/bank_transfer/progress"
                    ),
                }
            )

            return values
        elif self.provider_code == "eftpos":
            cancel_url = urls.url_join(base_url, "/payment/eftpos/cancel")
            values.update(
                {
                    "return_url": urls.url_join(base_url, "/payment/eftpos/validate"),
                    "cancel_url": cancel_url,
                    "api_url": urls.url_join(base_url, "/payment/eftpos/progress"),
                }
            )
            return values
        elif self.provider_code == "finance":
            cancel_url = urls.url_join(base_url, "/payment/finance/cancel")
            values.update(
                {
                    "return_url": urls.url_join(base_url, "/payment/finance/validate"),
                    "cancel_url": cancel_url,
                    "api_url": urls.url_join(base_url, "/payment/finance/progress"),
                }
            )
            return values
        return res

    def _get_communication(self):
        """Return the communication the user should use for their transaction.

        This communication might change according to the settings and the accounting localization.

        Note: self.ensure_one()

        :return: The selected communication.
        :rtype: str
        """
        self.ensure_one()
        communication = ""
        if hasattr(self, "invoice_ids") and self.invoice_ids:
            communication = self.invoice_ids[0].payment_reference
        elif hasattr(self, "sale_order_ids") and self.sale_order_ids:
            communication = self.sale_order_ids[0].reference
        return communication or self.reference

    def _get_tx_from_notification_data(self, provider_code, notification_data):
        """Override of payment to find the transaction based on custom data.

        :param str provider_code: The code of the provider that handled the transaction
        :param dict notification_data: The notification feedback data
        :return: The transaction if found
        :rtype: recordset of `payment.transaction`
        :raise: ValidationError if the data match no transaction
        """
        tx = super()._get_tx_from_notification_data(provider_code, notification_data)
        if provider_code != "customer_account" or len(tx) == 1:
            return tx

        reference = notification_data.get("reference")
        tx = self.search(
            [("reference", "=", reference), ("provider_code", "=", "customer_account")]
        )
        if not tx:
            raise ValidationError(
                "Customer Account: "
                + _("No transaction found matching reference %s.", reference)
            )
        return tx

    def _process_notification_data(self, notification_data):
        """Override of payment to process the transaction based on custom data.

        Note: self.ensure_one()

        :param dict notification_data: The custom data
        :return: None
        """
        super()._process_notification_data(notification_data)
        if self.provider_code == "customer_account":
            _logger.info(
                "validated Customer Account payment for transaction with reference %s: set as pending",
                self.reference,
            )
            self._set_pending()
            if self.operation == "refund":
                self.env.ref("payment.cron_post_process_payment_tx")._trigger()

        elif self.provider_code == "custom":
            sale_order = self.sale_order_ids.filtered(
                lambda so: so.state in ("draft", "sent")
            )
            # if sale_order.flooring_type_code == 'rug':
            #     sale_order.with_context(send_email=True).action_confirm()
            # else:
            #     sale_order.with_context(send_email=True).action_process_order()
            if sale_order:
                try:
                    sale_order._create_invoices()
                    if sale_order.invoice_ids:
                        sale_order.invoice_ids[0].action_post()
                except (TypeError, ValueError, OverflowError, UserError) as e:
                    _logger.error("Error creating invoice: %s", e)
        elif self.provider_code in ["eftpos", "finance", "cash", "bank_transfer"]:
            # Update the provider reference.
            self.provider_reference = f"{self.provider_code}-{self.reference}"

            # Create the token.
            if self.tokenize:
                # The reasons why we immediately tokenize the transaction regardless of the state rather
                # than waiting for the payment method to be validated ('authorized' or 'done') like the
                # other payment providers do are:
                # - To save the simulated state and payment details on the token while we have them.
                # - To allow customers to create tokens whose transactions will always end up in the
                #   said simulated state.
                self._demo_tokenize_from_notification_data(notification_data)

            # Update the payment state.
            self._set_done()
            # Immediately post-process the transaction if it is a refund, as the post-processing
            # will not be triggered by a customer browsing the transaction from the portal.
            if self.operation == "refund":
                self.env.ref("payment.cron_post_process_payment_tx")._trigger()

    def _log_received_message(self):
        """Override of `payment` to remove custom providers from the recordset.

        :return: None
        """
        other_provider_txs = self.filtered(
            lambda t: t.provider_code != "customer_account"
        )
        super(PaymentTransaction, other_provider_txs)._log_received_message()

    def _get_sent_message(self):
        """Override of payment to return a different message.

        :return: The 'transaction sent' message
        :rtype: str
        """
        message = super()._get_sent_message()
        if self.provider_code == "customer_account":
            message = _(
                "The customer has selected %(provider_name)s to make the payment.",
                provider_name=self.provider_id.name,
            )
        return message

    def _post_process(self):
        """Override of payment to automatically confirm quotations and generate invoices."""
        res = super()._post_process()
        # self.sale_order_ids.create_archieved_input_sales_move()
        if self.sale_order_ids and len(self.sale_order_ids) == 1:
            sale_order_id = self.sale_order_ids[0]
            if self.payment_id and sale_order_id.branch_id:
                self.payment_id.branch_id = sale_order_id.branch_id.id or False
                self.payment_id.sale_order_id = sale_order_id.id or False
            if not sale_order_id._confirmation_error_message():
                sale_order_id.with_context(send_email=True).action_confirm()
            try:
                invoice_posted = sale_order_id.invoice_ids.filtered(
                    lambda inv: inv.state == "posted"
                )
                invoice_draft = sale_order_id.invoice_ids.filtered(
                    lambda inv: inv.state == "draft"
                )
                # if not sale_order_id.is_home_approvals and not invoice_posted:
                #     # sale_order_id._force_lines_to_invoice_policy_order()
                #     invoice_posted = sale_order_id.with_context(
                #         raise_if_nothing_to_invoice=False
                #     )._create_invoices()
                #     if invoice_posted:
                #         invoice_posted.action_post()
                # elif invoice_posted and sale_order_id.invoice_status == "invoiced":
                #     # update the transaction_id of the invoice
                #     invoice_posted.transaction_ids = [(4, self.id)]
                # if invoice_draft and len(sale_order_id.invoice_ids) == 1:
                #     invoice_draft.transaction_ids = [(4, self.id)]

            except (TypeError, ValueError, OverflowError, UserError) as e:
                _logger.error("Error creating invoice: %s", e)

        return res

    # def _handle_notification_data(self, provider_code, notification_data):
    #     """Match the transaction with the notification data, update its state and return it.

    #     :param str provider_code: The code of the provider handling the transaction.
    #     :param dict notification_data: The notification data sent by the provider.
    #     :return: The transaction.
    #     :rtype: recordset of `payment.transaction`
    #     """
    #     res = super()._handle_notification_data(provider_code, notification_data)
    #     for tx in self:
    #         if tx.sale_order_ids and len(tx.sale_order_ids) == 1:
    #             sale_order_id = tx.sale_order_ids[0]
    #         invoice_posted = sale_order_id.invoice_ids.filtered(
    #             lambda inv: inv.state == "posted"
    #         )
    #         if (
    #             invoice_posted
    #             and sale_order_id.invoice_status == "invoiced"
    #             and tx not in invoice_posted.transaction_ids
    #         ):
    #             if (
    #                 tx.state in ("pending", "authorized")
    #                 or tx.state == "done"
    #                 and not (tx.payment_id and tx.payment_id.is_reconciled)
    #             ):
    #                 invoice_posted.transaction_ids = [(4, tx.id)]
    #     return res

    def _create_payment_surcharge(self, **extra_create_values):
        """Create an `account.payment` record for the current transaction.

        If the transaction is linked to some invoices, their reconciliation is done automatically.

        Note: self.ensure_one()

        :param dict extra_create_values: Optional extra create values
        :return: The created payment
        :rtype: recordset of `account.payment`
        """
        self.ensure_one()

        reference = (
            f"{self.reference} - "
            f'{self.partner_id.display_name or ""} - '
            f'{self.provider_reference or ""}'
        )

        payment_method_line = (
            self.provider_id.journal_id.inbound_payment_method_line_ids.filtered(
                lambda l: l.payment_provider_id == self.provider_id
            )
        )
        if (
            self.provider_id.code == "eftpos"
            and self.payment_method_id.primary_payment_method_id
        ):
            eftpos_pm = self.payment_method_id.primary_payment_method_id
            payment_method_line_eftpos = (
                self.provider_id.journal_id.inbound_payment_method_line_ids.filtered(
                    lambda l: l.code == eftpos_pm.code
                )
            )
            if payment_method_line_eftpos:
                payment_method_line = payment_method_line_eftpos

        payment_values = {
            "amount": abs(
                self.amount
            ),  # A tx may have a negative amount, but a payment must >= 0
            "payment_type": "inbound" if self.amount > 0 else "outbound",
            "currency_id": self.currency_id.id,
            "partner_id": self.partner_id.commercial_partner_id.id,
            "partner_type": "customer",
            "journal_id": self.provider_id.journal_id.id,
            "company_id": self.provider_id.company_id.id,
            "payment_method_line_id": payment_method_line.id,
            "payment_token_id": self.token_id.id,
            "payment_transaction_id": self.id,
            "ref": reference,
            **extra_create_values,
        }
        payment = self.env["account.payment"].create(payment_values)
        payment.action_post()
        # payment.move_id.button_draft()
        # payment.action_validate()

        # Track the payment to make a one2one.
        self.payment_id = payment

        if payment.state == "paid":
            # Reconcile the payment with the source transaction's invoices in case of a partial capture.
            if self.operation == self.source_transaction_id.operation:
                invoices = self.source_transaction_id.invoice_ids
            else:
                invoices = self.invoice_ids
            if invoices:
                invoices.filtered(lambda inv: inv.state == "draft").action_post()

                (payment.move_id.line_ids + invoices.line_ids).filtered(
                    lambda line: line.account_id == payment.destination_account_id
                    and not line.reconciled
                ).reconcile()

        return payment

    def _create_payment(self, **extra_create_values):
        if self.sale_order_ids:
            sale_order_id = self.sale_order_ids[0]
            extra_create_values["sale_order_id"] = sale_order_id.id
            extra_create_values["name"] = self.env["ir.sequence"].next_by_code(
                "down.payment.no"
            )

            if sale_order_id.branch_id:
                extra_create_values["branch_id"] = sale_order_id.branch_id.id
            if self.is_surcharge and self.surcharge_amount > 0:
                res = self._create_payment_surcharge(**extra_create_values)
            else:
                res = super()._create_payment(**extra_create_values)
                # res.move_id.button_draft()
                # res.action_validate()
                res.action_post()
                sale_order_id.prepayment_amount = round(
                    (sale_order_id.remaining_amount - res.amount), 2
                )
            res.is_downpayment = True
            # create payment for surcharge
            if (
                self.provider_id
                and self.is_surcharge
                and self.surcharge_amount > 0
                and not self.payment_surcharge_id
            ):
                reference = (
                    f"{self.reference} - "
                    f'{self.partner_id.display_name or ""} - '
                    f'{self.provider_reference or ""}'
                )

                payment_method_line = self.provider_id.journal_id.inbound_payment_method_line_ids.filtered(
                    lambda l: l.payment_provider_id == self.provider_id
                )
                if (
                    self.provider_id.code == "eftpos"
                    and self.payment_method_id.primary_payment_method_id
                ):
                    eftpos_pm = self.payment_method_id.primary_payment_method_id
                    payment_method_line = self.provider_id.journal_id.inbound_payment_method_line_ids.filtered(
                        lambda l: l.code == eftpos_pm.code
                    )
                sequence = (
                    self.env["ir.sequence"].next_by_code("down.payment.sequence")
                    or "SCH-00001"
                )
                outstanding_account_id = self.env["account.account"].search(
                    [
                        *self.env["account.account"]._check_company_domain(
                            self.company_id
                        ),
                        ("code", "=", "W-006-7503-0000-980"),
                        ("name", "ilike", "UNDER/OVER BANKING"),
                    ],
                    limit=1,
                )
                destination_account_id = self.env["account.account"].search(
                    [
                        *self.env["account.account"]._check_company_domain(
                            self.company_id
                        ),
                        ("code", "=", "W-006-8750-0000-980"),
                        ("name", "ilike", "REJECTED RECEIPTS - JOBS"),
                    ],
                    limit=1,
                )

                if "name" in extra_create_values:
                    del extra_create_values["name"]

                payment_values = {
                    "name": sequence,
                    "amount": abs(
                        self.surcharge_amount
                    ),  # A tx may have a negative amount, but a payment must >= 0
                    "payment_type": "inbound"
                    if self.surcharge_amount > 0
                    else "outbound",
                    "currency_id": self.currency_id.id,
                    "partner_id": self.partner_id.commercial_partner_id.id,
                    "partner_type": "customer",
                    "journal_id": self.provider_id.journal_id.id,
                    "company_id": self.provider_id.company_id.id,
                    "payment_method_line_id": payment_method_line.id,
                    "payment_token_id": self.token_id.id,
                    "payment_transaction_id": self.id,
                    "branch_id": res.branch_id.id,
                    "ref": reference,
                    **extra_create_values,
                }
                if outstanding_account_id and destination_account_id:
                    payment_values.update(
                        {
                            "line_ids": [
                                Command.create(
                                    {
                                        "debit": self.surcharge_amount,
                                        "credit": 0.0,
                                        "partner_id": self.partner_id.id,
                                        "account_id": outstanding_account_id.id,
                                        "receipt_number": "",
                                    }
                                ),
                                Command.create(
                                    {
                                        "debit": 0.0,
                                        "credit": self.surcharge_amount,
                                        "partner_id": self.partner_id.id,
                                        "account_id": destination_account_id.id,
                                        "receipt_number": "",
                                    }
                                ),
                            ],
                        }
                    )
                if "sale_order_id" in payment_values:
                    del payment_values["sale_order_id"]
                self.payment_surcharge_id = (
                    self.env["account.payment"].sudo().create(payment_values)
                )
                self.payment_surcharge_id.action_post()
                # self.payment_surcharge_id.move_id.button_draft()
                # self.payment_surcharge_id.action_validate()

                if self.payment_surcharge_id.state == "paid":
                    # update the sale order with the payment information
                    # 25500, 40000, 14500, 16700, 69600, 44600, 28,128,750
                    sale_order_id.prepayment_amount = round(
                        (sale_order_id.remaining_amount - res.amount), 2
                    )

                    job_complete_journal = (
                        self.sale_order_ids.invoice_ids.completed_sale_invoice_ids
                    )
                    if not job_complete_journal:
                        return res
                    so_id = job_complete_journal.source_sale_order_id
                    downpayment_ids = so_id.downpayment_ids
                    # if (
                    #     so_id.sale_type
                    #     and so_id.sale_type == "retail"
                    #     and not downpayment_ids
                    # ):
                    #     raise UserError(
                    #         _("Please create a downpayment for the sale order.")
                    #     )
                    downpayment_amount = sum(downpayment_ids.mapped("amount"))
                    tax_total = (
                        so_id.tax_totals["total_amount_currency"]
                        - so_id.tax_totals["base_amount_currency"]
                    )
                    account_credit_gst_suspense_ex_id = (
                        so_id.fiscal_position_id.account_credit_gst_suspense_ex_id
                    )
                    account_debit_customer_dep_on_prog_id = (
                        so_id.fiscal_position_id.account_debit_customer_dep_on_prog_id
                    )
                    account_credit_wrk_prog_ach_id = (
                        so_id.fiscal_position_id.account_credit_wrk_prog_ach_id
                    )
                    line_vals = [
                        (
                            0,
                            0,
                            {
                                "name": "Line 9",
                                "credit": tax_total,
                                "debit": 0.0,
                                "account_id": account_credit_gst_suspense_ex_id.id,
                            },
                        ),
                        (
                            0,
                            0,
                            {
                                "name": "Line 10",
                                "credit": so_id.tax_totals["base_amount_currency"],
                                "debit": 0,
                                "account_id": account_credit_wrk_prog_ach_id.id,
                            },
                        ),
                        (
                            0,
                            0,
                            {
                                "name": "Line 11",
                                "credit": 0.0,
                                "debit": downpayment_amount,
                                "account_id": account_debit_customer_dep_on_prog_id.id,
                            },
                        ),
                    ]
                    job_complete_journal.write({"invoice_line_ids": line_vals})
        else:
            res = super()._create_payment(**extra_create_values)
        return res

    # def _check_amount_and_confirm_order(self):
    #     res = super()._check_amount_and_confirm_order()
    #     # confirmed_orders = self.env['sale.order']
    #     for tx in self:
    #         # We only support the flow where exactly one quotation is linked to a transaction.
    #         if len(tx.sale_order_ids) == 1:
    #             quotation = tx.sale_order_ids.filtered(
    #                 lambda so: so.state in ("draft", "sent")
    #             )
    #             if quotation and quotation.amount_paid:
    #                 if (
    #                     quotation.is_retail_sales
    #                     and quotation.flooring_type_code != "rug"
    #                 ):
    #                     quotation.state = "sent"
    #                 else:
    #                     quotation.state = (
    #                         "achieved" if quotation.is_retail_sales else "in_progress"
    #                     )
    #                 # if quotation.flooring_type_code == 'rug':
    #                 #     quotation.with_context(send_email=True).action_confirm()
    #                 # else:
    #                 #     quotation.with_context(send_email=True).action_process_order()
    #     return res

    # def _invoice_sale_orders(self):
    #     for tx in self.filtered(lambda tx: tx.sale_order_ids):
    #         tx = tx.with_company(tx.company_id)

    #         confirmed_orders = tx.sale_order_ids.filtered(lambda so: so.state == "sale")
    #         if confirmed_orders:
    #             # Filter orders between those fully paid and those partially paid.
    #             fully_paid_orders = confirmed_orders.filtered(lambda so: so._is_paid())

    #             # Create a down payment invoice for partially paid orders
    #             downpayment_invoices = (
    #                 confirmed_orders - fully_paid_orders
    #             )._generate_downpayment_invoices()

    #             # For fully paid orders create a final invoice.
    #             fully_paid_orders._force_lines_to_invoice_policy_order()
    #             final_invoices = fully_paid_orders.with_context(
    #                 raise_if_nothing_to_invoice=False
    #             )._create_invoices(final=True)
    #             invoices = False
    #             if final_invoices != None:
    #                 invoices = downpayment_invoices + final_invoices

    #             # Setup access token in advance to avoid serialization failure between
    #             # edi postprocessing of invoice and displaying the sale order on the portal
    #             if invoices:
    #                 for invoice in invoices:
    #                     invoice._portal_ensure_token()
    #                 tx.invoice_ids = [Command.set(invoices.ids)]


class AccountPayment(models.Model):
    _inherit = "account.payment"

    receipt_number = fields.Char(
        string="Receipt Number",
        related="payment_transaction_id.payment_ref",
        store=True,
        readonly=False,
    )


class AccountMove(models.Model):
    _inherit = "account.move"

    receipt_number = fields.Char(
        string="Receipt Number",
        compute="_compute_receipt_number",
        store=True,
        readonly=False,
    )

    @api.depends("origin_payment_id.receipt_number")
    def _compute_receipt_number(self):
        for rec in self:
            receipt_number = ""
            if rec.origin_payment_id:
                receipt_number = rec.origin_payment_id.receipt_number
            else:
                receipt_number = rec.receipt_number
            rec.receipt_number = receipt_number


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    receipt_number = fields.Char(
        string="Receipt Number", compute="_compute_receipt_number", store=True
    )

    @api.depends("move_id.receipt_number")
    def _compute_receipt_number(self):
        for rec in self:
            receipt_number = ""
            if rec.move_id.receipt_number:
                receipt_number = rec.move_id.receipt_number
            rec.receipt_number = receipt_number
