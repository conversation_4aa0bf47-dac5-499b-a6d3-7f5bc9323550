import VariantMixin from '@website_sale/js/sale_variant_mixin';
import { _t } from "@web/core/l10n/translation";
import publicWidget from "@web/legacy/js/public/public_widget";

publicWidget.registry.WebsiteSale.include({
    start: function () {
        this._super.apply(this, arguments);
        this._websiteSaleMainButtonHandler = (event) => {
            const target = event.target.closest('a[name="website_sale_main_button"]');
            if (target) {
                if (window.location.pathname.startsWith('/shop/checklist')) {
                    event.preventDefault();
                    const saveBtn = document.getElementById('save_checklist');
                    if (saveBtn) {
                        saveBtn.click();
                    } else {
                        console.warn('No button with id="save_checklist" found.');
                    }
                }
            }
        };
        document.body.addEventListener('click', this._websiteSaleMainButtonHandler);
    },
    // stop: function () {
    //     this._super.apply(this, arguments);
    //     if (this._websiteSaleMainButtonHandler) {
    //         document.body.removeEventListener('click', this._websiteSaleMainButtonHandler);
    //     }
    // },
    _disableInput: function ($parent, attributeValueId, excludedBy, attributeNames, productName) {
        console.log('Custom _disableInput running...');
        // Your override logic here...
        var $input = $parent.find('option[value=' + attributeValueId + '], input[value=' + attributeValueId + ']');
        $input.addClass('css_not_available');
        $input.closest('label').addClass('css_not_available');
        $input.closest('.o_variant_pills').addClass('css_not_available');
        // Uncheck all disabled inputs within the same attribute group (ul)
        $input.closest('ul').find('input.css_not_available').prop('checked', false);
        $input.closest('ul').find('li').css('display', '');
        // add style display none for li has label css_not_available
        $input.closest('ul').find('li:has(label.css_not_available)').css('display', 'none');
        // Tìm input đầu tiên KHÔNG bị disable hoặc bị ẩn (tuỳ theo UI bạn thiết kế)
        // nếu đã có input checked thì không checked đầu tiên nữa
        if ($input.closest('ul').find('input:not(.css_not_available):checked').length === 0) {
            var $firstValid = $input.closest('ul').find('input:not(.css_not_available):not([disabled]):visible').first();
            $firstValid.prop('checked', true).trigger('change');
        }

        if (excludedBy && attributeNames) {
            var $target = $input.is('option') ? $input : $input.closest('label').add($input);
            var excludedByData = [];
            if ($target.data('excluded-by')) {
                excludedByData = JSON.parse($target.data('excluded-by'));
            }

            var excludedByName = attributeNames[excludedBy];
            if (productName) {
                excludedByName = productName + ' (' + excludedByName + ')';
            }
            excludedByData.push(excludedByName);

            $target.attr('title', _t('Not available with %s', excludedByData.join(', ')));
            $target.data('excluded-by', JSON.stringify(excludedByData));
        }
    },
});

VariantMixin._toggleDisable = function ($parent, isCombinationPossible) {
    console.log('isCombinationPossible', isCombinationPossible);
    // $parent.toggleClass('css_not_available', !isCombinationPossible);
};

export default VariantMixin;
