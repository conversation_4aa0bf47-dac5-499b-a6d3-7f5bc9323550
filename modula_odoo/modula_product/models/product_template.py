# -*- coding: utf-8 -*-
import logging

from odoo import api, fields, models
from odoo.tools import float_is_zero

_logger = logging.getLogger(__name__)


Leather = [
    "SASSARI CHALK",
    "SASSARI LIGHT GREY",
    "SASSARI GREY",
    "SASSARI GINGER SNAP",
    "SASSARI PERIWINKLE",
    "ATOLLO BLACK",
    "ATOLLO DOVE GREY",
    "ATOLLO DRIFTWOOD",
    "ATOLLO TAUPE",
    "RIO PERIWINKLE BLUE",
    "<PERSON><PERSON><PERSON><PERSON>O TAUPE",
    "C<PERSON>SSIC<PERSON> PORTOBELLO",
    "<PERSON><PERSON>ON<PERSON> TERRACOTTA",
    "VERON<PERSON> IVORY",
    "VERON<PERSON> TOMATO",
    "EST MARIGOLD",
    "EST PEACOCK BLUE",
    "OLD WORLD WALNUT",
    "RANGERS ELEPHANT",
    "RANGERS DOVE GREY",
    "RANGERS STOUT",
    "RANGERS CHARCOAL",
    "RANGERS CARAMEL",
    "RANGERS MOSE",
    "RANGERS SAGE",
    "DORN CARAMEL",
    "DORN REDDISH BROWN",
    "DORN GRANITE",
    "TEXAS(NEST) STONE",
    "TEXAS(NEST) TOFFEE",
    "TEXAS(NEST) MUSHROOM",
    "TEXAS(NEST) THUNDER",
    "BELLAGIO BLACK",
    "TORELLO GUNMETAL",
    "TORELLO STONE",
    "LIA FOREST GREEN",
]

Fabric = [
    "VANCOUVER WOLF",
    "TALENT SILVER",
    "TALENT SALT & PEPPER",
    "RASTA GREY",
    "RASTA SMOKE",
    "RASTA APPLE GREEN",
    "POPSTITCH PEBBLE",
    "POPSTITCH SHELL",
    "7525-156 FOREST",
    "7525-132 GOLD",
    "7525-56 INDIGO",
    "BRODERICK CHARCOAL",
    "BRODERICK HEMP",
    "BRODERICK SEAGLASS",
    "HELIO OYSTER",
    "HELIO SAND",
    "HELIO PEWTER",
    "HELIO SMOKE",
    "HELIO CITRON",
    "OSLO-IVORY WHITE",
    "DIVA STONE",
    "DIVA MUSHROOM",
    "DIVA MOSS",
    "MILAN-110",
    "MILAN-105",
    "MILAN-353",
    "A3817-11A BISCUIT",
    "MAISON HALE",
    "MON CHERI PEBBLE",
    "CHACHA LINEN",
    "CHACHA PEWTER",
    "CHACHA FOREST GREEN",
    "CHACHA SILVER",
    "CHACHA BLUE",
    "ANSELL BASALT",
    "ANSELL AQUA",
]


class ProductTemplate(models.Model):
    _inherit = "product.template"

    variant_attribute_names = fields.Char(
        string="Variant Attribute Names",
    )
    range_name = fields.Char(
        string="Range Name",
    )

    def _get_attribute_inclusions(self, parent_combination=None, combination_ids=None):
        """Get attribute inclusions based on the given combination.

        :param parent_combination: the parent combination to use when searching for
            attribute inclusions, if any
        :type parent_combination: recordset `product.template.attribute.value`
        :param combination_ids: list of product.template.attribute.value ids to filter the
            inclusions
        :type combination_ids: list[int]

        :return: dict with inclusions, archived combinations and parent inclusions
        :rtype: dict
        """
        self.ensure_one()
        # for it can be read product.template.attribute.inclusion urgent for website_sale
        self = self.sudo()
        inclusions = {}
        parent_inclusions = {}
        archived_combinations = []

        # Get all attribute values with manage_combination_matching='enabled'
        enabled_attr_values = self.valid_product_template_attribute_line_ids.product_template_value_ids.filtered(
            lambda x: x.manage_combination_matching == "enabled"
        )

        # For each enabled attribute value
        for product_attr_value in enabled_attr_values:

            # Get the include_for values
            included_values = (
                self.env["product.template.attribute.inclusion"]
                .browse(product_attr_value.include_for.ids)
                .mapped("value_ids")
                .ids
            )

            # If no include_for values exist, mark this attribute value as always visible
            if not included_values:
                inclusions[product_attr_value.id] = []
            else:
                inclusions[product_attr_value.id] = included_values

        # Handle parent combinations
        if parent_combination:
            for product_attr_value in parent_combination:
                if product_attr_value.manage_combination_matching == "enabled":
                    parent_inclusions[
                        product_attr_value.id
                    ] = product_attr_value.include_for.filtered(
                        lambda v: v.attribute_line_id
                        in self.valid_product_template_attribute_line_ids
                    ).ids

        res = {
            "inclusions": inclusions,
            "parent_inclusions": parent_inclusions,
            "archived_combinations": archived_combinations,
        }
        if self.env.context.get("from_website_sale"):
            final_res = self._from_inclusion_to_exclusions(res, parent_combination)
        else:
            final_res = res
        return final_res

    def _from_inclusion_to_exclusions(
        self, inclusions, parent_combination=None, parent_name=None
    ):
        """Convert inclusions to exclusions.

        For each attribute value, exclude all other values that are not included in its inclusions.
        Also, exclude attribute values with manage_combination_matching == 'enabled' that are not in inclusions.
        Add archived_combinations and mapped_attribute_names to the result.
        """
        self.ensure_one()
        final_res = dict(inclusions)
        inclusions_dict = inclusions.get("inclusions", {})
        all_value_ids = set(inclusions_dict.keys())

        # Exclusions: for each value, exclude only enabled values not included in its inclusions
        exclusions = {}

        # Find all enabled attribute values (manage_combination_matching == 'enabled')
        attribute_line_ids = self.valid_product_template_attribute_line_ids
        archived_combinations = []

        all_included_ids = self.env[
            "product.template.attribute.inclusion"
        ].search_fetch([("product_tmpl_id", "=", self.id)], ["value_ids"])

        all_includes_values = all_included_ids.value_ids
        need_to_include_value = (
            attribute_line_ids.product_template_value_ids - all_includes_values
        )
        for attribute_line_id in attribute_line_ids:
            concurrent_exclusions = {}
            enabled_attr_values = attribute_line_id.product_template_value_ids
            enabled_value_ids = set(enabled_attr_values.ids)
            for enabled_value_id in enabled_value_ids:
                enabled_value = self.env["product.template.attribute.value"].browse(
                    enabled_value_id
                )
                if (
                    enabled_value.manage_combination_matching != "enabled"
                    or not inclusions_dict.get(enabled_value_id)
                ):
                    exclusions[enabled_value_id] = []
                else:
                    # concurrent_exclusions.update(
                    #     {
                    #         enabled_value_id: list(
                    #             set(inclusions_dict.get(enabled_value_id, []))
                    #         )
                    #     }
                    # )
                    # for attribute_need_to_edit_exclude in enabled_attr_values:
                    #     if attribute_need_to_edit_exclude != enabled_value:
                    #         exclusions[attribute_need_to_edit_exclude.id] = list(
                    #             set(inclusions_dict.get(enabled_value_id, []))
                    #         )
                    attribute_id = (
                        self.env["product.template.attribute.value"]
                        .browse(inclusions_dict.get(enabled_value_id)[0])
                        .attribute_id
                    )
                    exclude_values = all_includes_values.filtered(
                        lambda x: x.attribute_id == attribute_id
                    )
                    new_value = set(exclude_values.ids) - set(
                        inclusions_dict.get(enabled_value_id, [])
                    )
                    new_value = new_value - set(
                        need_to_include_value.filtered(
                            lambda x: x.attribute_line_id == attribute_line_id
                        ).ids
                    )
                    new_value = new_value.union(
                        set(
                            need_to_include_value.filtered(
                                lambda x: x.attribute_line_id != attribute_line_id
                            ).ids
                        )
                    )
                    exclusions[enabled_value_id] = list(new_value)
            if concurrent_exclusions:
                for key, value in concurrent_exclusions.items():
                    for enabled_value_id in enabled_value_ids:
                        if enabled_value_id != key:
                            if (
                                key in inclusions_dict
                                and enabled_value_id in inclusions_dict
                            ):
                                values_to_append = [
                                    v
                                    for v in inclusions_dict.get(key, [])
                                    if v
                                    not in inclusions_dict.get(enabled_value_id, [])
                                ]
                                if values_to_append and enabled_value_id in exclusions:
                                    exclusions[enabled_value_id].extend(
                                        values_to_append
                                    )

        archived_products = self.with_context(
            active_test=False
        ).product_variant_ids.filtered(lambda l: not l.active)
        active_combinations = set(
            tuple(product.product_template_attribute_value_ids.ids)
            for product in self.product_variant_ids
        )
        archived_combinations = list(
            set(
                tuple(product.product_template_attribute_value_ids.ids)
                for product in archived_products
                if product.product_template_attribute_value_ids
            )
            - active_combinations
        )

        # Mapped attribute names
        all_attr_values = (
            self.valid_product_template_attribute_line_ids.product_template_value_ids
        )
        mapped_attribute_names = {
            attr_value.id: attr_value.display_name for attr_value in all_attr_values
        }
        parent_combination = (
            parent_combination or self.env["product.template.attribute.value"]
        )
        parent_exclusions = self._get_parent_attribute_exclusions(parent_combination)
        parent_combination_ids = parent_combination.ids if parent_combination else []

        final_res["exclusions"] = exclusions
        final_res["archived_combinations"] = archived_combinations
        final_res["parent_exclusions"] = parent_exclusions
        if not self.env.context.get("from_sale_order_configurator"):
            final_res["mapped_attribute_names"] = mapped_attribute_names
            final_res["parent_combination"] = parent_combination_ids
            final_res["parent_product_name"] = parent_name
        return final_res

    def _inclusion_base_possible(self, combination):
        self.ensure_one()

        # First check if any attribute value has manage_combination_matching == 'enabled'
        enabled_ptavs = combination.filtered(
            lambda ptav: ptav.manage_combination_matching == "enabled"
        )

        if not enabled_ptavs:
            return True
        all_included_ids = self.env[
            "product.template.attribute.inclusion"
        ].search_fetch([("product_tmpl_id", "=", self.id)], ["value_ids"])
        for ptav in enabled_ptavs:
            if ptav not in all_included_ids.value_ids:
                continue
            incluse_ids = all_included_ids.filtered(lambda x: ptav in x.value_ids)
            for incluse_id in incluse_ids:
                if incluse_id.product_template_attribute_value_id in enabled_ptavs:
                    continue
                return False
        return True

    def _get_additionnal_combination_info(
        self, product_or_template, quantity, date, website
    ):
        """Computes additional combination info, based on given parameters

        :param product_or_template: `product.product` or `product.template` record
            as variant values must take precedence over template values (when we have a variant)
        :param float quantity:
        :param date date: today's date, avoids useless calls to today/context_today and harmonize
            behavior
        :param website: `website` record holding the current website of the request (if any),
            or the contextual website (tests, ...)
        :returns: additional product/template information
        :rtype: dict
        """
        pricelist = website.pricelist_id
        currency = website.currency_id

        # Pricelist price doesn't have to be converted
        pricelist_price, pricelist_rule_id = pricelist._get_product_price_rule(
            product=product_or_template,
            quantity=quantity,
            target_currency=currency,
        )

        price_before_discount = pricelist_price
        pricelist_item = self.env["product.pricelist.item"].browse(pricelist_rule_id)
        if pricelist_item._show_discount_on_shop():
            price_before_discount = pricelist_item._compute_price_before_discount(
                product=product_or_template,
                quantity=quantity or 1.0,
                date=date,
                uom=product_or_template.uom_id,
                currency=currency,
            )

        has_discounted_price = price_before_discount > pricelist_price
        combination_info = {
            "list_price": max(pricelist_price, price_before_discount),
            "price": pricelist_price,
            "has_discounted_price": has_discounted_price,
        }

        comparison_price = None
        if (
            not has_discounted_price
            and product_or_template.compare_list_price
            and self.env.user.has_group("website_sale.group_product_price_comparison")
        ):
            comparison_price = product_or_template.currency_id._convert(
                from_amount=product_or_template.compare_list_price,
                to_currency=currency,
                company=self.env.company,
                date=date,
                round=False,
            )
        combination_info["compare_list_price"] = comparison_price

        combination_info["price_extra"] = product_or_template.currency_id._convert(
            from_amount=product_or_template._get_attributes_extra_price(),
            to_currency=currency,
            company=self.env.company,
            date=date,
            round=False,
        )

        # Apply taxes
        fiscal_position = website.fiscal_position_id.sudo()

        product_taxes = product_or_template.sudo().taxes_id._filter_taxes_by_company(
            self.env.company
        )
        taxes = self.env["account.tax"]
        tax_included = False
        if pricelist_rule_id:
            tax_included = (
                self.env["product.pricelist.item"].sudo().browse(pricelist_rule_id).base
                == "tax_included"
            )
        if product_taxes:
            taxes = fiscal_position.map_tax(product_taxes)
            # We do not apply taxes on the compare_list_price value because it's meant to be
            # a strict value displayed as is.
            for price_key in ("price", "list_price", "price_extra"):
                if tax_included and price_key in ("price", "list_price"):
                    continue
                combination_info[price_key] = self._apply_taxes_to_price(
                    combination_info[price_key],
                    currency,
                    product_taxes,
                    taxes,
                    product_or_template,
                    website=website,
                )

        combination_info.update(
            {
                "prevent_zero_price_sale": website.prevent_zero_price_sale
                and float_is_zero(
                    combination_info["price"],
                    precision_rounding=currency.rounding,
                ),
                "base_unit_name": product_or_template.base_unit_name,
                "base_unit_price": product_or_template._get_base_unit_price(
                    combination_info["price"]
                ),
                # additional info to simplify overrides
                "currency": currency,  # displayed currency
                "date": date,
                "product_taxes": product_taxes,  # taxes before fpos mapping
                "taxes": taxes,  # taxes after fpos mapping
            }
        )

        if combination_info["prevent_zero_price_sale"]:
            combination_info["compare_list_price"] = 0

        return combination_info

    def _get_combination_info(
        self,
        combination=False,
        product_id=False,
        add_qty=1.0,
        parent_combination=False,
        only_template=False,
    ):
        # for it can be read product.template.attribute.inclusion urgent for website_sale
        self = self.sudo()
        res = super()._get_combination_info(
            combination, product_id, add_qty, parent_combination, only_template
        )

        if combination:
            result = self._inclusion_base_possible(combination)
            if not result:
                is_combination_possible = False
                res["is_combination_possible"] = is_combination_possible
        return res

    def process_import_file(self, vals_list):
        attribute_names_list = []
        for vals in vals_list:
            if "variant_attribute_names" in vals and vals.get(
                "variant_attribute_names"
            ):
                attribute_names = [
                    name.strip()
                    for name in vals["variant_attribute_names"].split(",")
                    if name.strip()
                ]
                attribute_names_list.append(attribute_names)
                # Optionally remove the field from vals
                # del vals["variant_attribute_names"]
            else:
                attribute_names_list.append([])
        return vals_list, attribute_names_list

    @api.model_create_multi
    def create(self, vals_list):
        """
        Odoo batch product creation with attribute/variant assignment.

        - Uses the ORM for all model operations (no direct SQL).
        - For each product, ensures all referenced product.attribute.value records exist (creates if missing).
        - For each attribute, creates or updates product.template.attribute.line with all value_ids set to product.attribute.value ids.
        - Odoo automatically creates product.template.attribute.value records as needed.
        - After all attribute lines/values are set, calls product._create_variant_ids() to ensure all product variants are generated.
        - This approach is robust, future-proof, and leverages Odoo's business logic for variant management.
        """
        attribute_names_list = None
        if self.env.context.get("import_file", False):
            vals_list, attribute_names_list = self.process_import_file(vals_list)

        products = super().create(vals_list)
        if attribute_names_list:
            for product, attribute_names in zip(products, attribute_names_list):
                if not attribute_names:
                    continue

                # 1. Ensure all product.attribute.value exist (create if missing)
                attr_value_recs = self.env["product.attribute.value"].search(
                    [("name", "in", attribute_names)]
                )
                found_names = set(attr_value_recs.mapped("name"))
                missing_names = set(attribute_names) - found_names
                for missing_name in missing_names:
                    attribute = self.env["product.attribute"].search([], limit=1)
                    if attribute:
                        new_value = self.env["product.attribute.value"].create(
                            {
                                "name": missing_name,
                                "attribute_id": attribute.id,
                            }
                        )
                        attr_value_recs += new_value
                if not attr_value_recs:
                    continue

                # 2. For each attribute, create or update the line with all values
                for attribute in attr_value_recs.mapped("attribute_id"):
                    value_ids = attr_value_recs.filtered(
                        lambda v: v.attribute_id == attribute
                    ).ids
                    attribute_line = self.env["product.template.attribute.line"].search(
                        [
                            ("product_tmpl_id", "=", product.id),
                            ("attribute_id", "=", attribute.id),
                        ],
                        limit=1,
                    )
                    if not attribute_line:
                        attribute_line = self.env[
                            "product.template.attribute.line"
                        ].create(
                            {
                                "product_tmpl_id": product.id,
                                "attribute_id": attribute.id,
                                "value_ids": [(6, 0, value_ids)],
                            }
                        )
                    else:
                        attribute_line.value_ids = [(6, 0, value_ids)]
                # 3. Ensure variants are generated
                product._create_variant_ids()
        # --- Add imported products to range's skus_product_ids ---
        if self.env.context.get("import_file", False):
            if len(vals_list) > 0:
                if "range_name" in vals_list[0]:
                    for product in products:
                        if product.range_name:
                            range_product = self.env["product.template"].search(
                                [
                                    ("name", "=", product.range_name),
                                    (
                                        "id",
                                        "!=",
                                        product.id,
                                    ),  # Exclude self if same range_name
                                ],
                                limit=1,
                            )
                            if range_product:
                                range_product.write(
                                    {"skus_product_ids": [(4, product.id)]}
                                )
        return products

    def write(self, vals):
        """
        Optimize attribute value assignment and variant generation during import.
        """
        if self.env.context.get("import_file", False) and vals.get(
            "variant_attribute_names"
        ):
            vals_list = [vals]
            vals_list, attribute_names_list = self.process_import_file(vals_list)
            vals = vals_list[0]  # Get back the processed vals
            res = super().write(vals)

            if attribute_names_list and attribute_names_list[0]:
                attribute_names = attribute_names_list[0]
                # 1. Search all attribute values at once
                attr_value_recs = self.env["product.attribute.value"].search(
                    [("name", "in", attribute_names)]
                )
                if attr_value_recs:
                    # 2. Group attribute values by attribute_id
                    attr_values_by_attr = {}
                    for val in attr_value_recs:
                        attr_values_by_attr.setdefault(val.attribute_id.id, []).append(
                            val.id
                        )

                    ProductTemplateAttributeLine = self.env[
                        "product.template.attribute.line"
                    ]
                    for product in self:
                        # Search all lines for this product in one go
                        existing_lines = ProductTemplateAttributeLine.search(
                            [
                                ("product_tmpl_id", "=", product.id),
                                (
                                    "attribute_id",
                                    "in",
                                    list(attr_values_by_attr.keys()),
                                ),
                            ]
                        )
                        existing_lines_map = {
                            line.attribute_id.id: line for line in existing_lines
                        }

                        for attr_id, value_ids in attr_values_by_attr.items():
                            if attr_id in existing_lines_map:
                                existing_lines_map[attr_id].value_ids = [
                                    (6, 0, value_ids)
                                ]
                            else:
                                ProductTemplateAttributeLine.create(
                                    {
                                        "product_tmpl_id": product.id,
                                        "attribute_id": attr_id,
                                        "value_ids": [(6, 0, value_ids)],
                                    }
                                )
                        # Generate variants only once per product
                        product._create_variant_ids()
            return res
        return super().write(vals)

    def clean_variant_attribute(self):
        """
        For each product, ensure each variant has exactly two attributes:
        1. 'Cover' (with value 'Leather' or 'Fabric')
        2. The second attribute value must be in the Leather or Fabric list, depending on the Cover type.
        If not, unlink (delete) the variant.
        """
        # Convert lists to sets for O(1) lookup instead of O(n)
        leather_set = set(Leather)
        fabric_set = set(Fabric)
        cover_values = {"Leather", "Fabric"}

        # Batch collect all variants to unlink across all products
        variants_to_unlink = self.env["product.product"]

        for product in self:
            # Fetch all variants and prefetch related fields
            variants = product.product_variant_ids
            # Force prefetch of attribute values to reduce database queries
            variants.mapped("product_template_attribute_value_ids.attribute_id.name")
            variants.mapped("product_template_attribute_value_ids.name")

            for variant in variants:
                attr_values = variant.product_template_attribute_value_ids

                # Skip variants that don't have exactly 2 attributes
                if len(attr_values) != 2:
                    variants_to_unlink |= variant
                    continue

                # Use a more efficient approach to find cover and other values
                cover_value = None
                other_value = None

                for attr_value in attr_values:
                    if (
                        attr_value.attribute_id.name == "Cover"
                        and attr_value.name in cover_values
                    ):
                        cover_value = attr_value
                    else:
                        other_value = attr_value

                # Check if we have valid cover and other values
                if not cover_value or not other_value:
                    variants_to_unlink |= variant
                    continue

                # Check if the combination is valid using set lookups
                is_valid = False
                if cover_value.name == "Leather":
                    is_valid = other_value.name in leather_set
                elif cover_value.name == "Fabric":
                    is_valid = other_value.name in fabric_set

                if not is_valid:
                    variants_to_unlink |= variant

        # Batch unlink all invalid variants at once, handling constraint violations
        if variants_to_unlink:
            try:
                variants_to_unlink.unlink()
            except Exception as e:
                # If batch unlink fails due to foreign key constraints, try individual unlinks
                _logger.warning(
                    f"Batch unlink failed: {e}. Attempting individual unlinks..."
                )
                failed_variants = []
                for variant in variants_to_unlink:
                    try:
                        variant.unlink()
                    except Exception as individual_error:
                        failed_variants.append(variant.id)
                        _logger.warning(
                            f"Cannot delete variant {variant.id} ({variant.display_name}): {individual_error}"
                        )

                if failed_variants:
                    _logger.info(
                        f"Successfully cleaned variants except for {len(failed_variants)} variants that are referenced by other records: {failed_variants}"
                    )

    @api.depends("product_variant_ids")
    def _compute_product_variant_id(self):
        for p in self:
            # Try to find a variant based on inclusions if they exist
            if p.product_variant_ids:
                inclusions = self.env[
                    "product.template.attribute.inclusion"
                ].search_fetch(
                    [
                        ("product_tmpl_id", "=", p.id),
                    ],
                    ["value_ids", "product_template_attribute_value_id"],
                )

                # Only proceed with inclusion-based selection if inclusions exist
                if inclusions:
                    inclusions.value_ids.fetch(["id"])
                    inclusions.product_template_attribute_value_id.fetch(["id"])

                    if inclusions.product_template_attribute_value_id.ids:
                        first_value_id = (
                            inclusions.product_template_attribute_value_id.ids[0]
                        )
                        inclusions_with_first_value = inclusions.filtered(
                            lambda i: i.product_template_attribute_value_id.id
                            == first_value_id
                        )

                        if inclusions_with_first_value.value_ids.ids:
                            second_value_id = inclusions_with_first_value.value_ids.ids[
                                0
                            ]

                            # Find variant with both attribute values
                            variant = p.product_variant_ids.filtered(
                                lambda v: first_value_id
                                in v.product_template_variant_value_ids.ids
                                and second_value_id
                                in v.product_template_variant_value_ids.ids
                            )[:1]

                            if variant:
                                p.product_variant_id = variant.id
                                continue

            # Default fallback
            p.product_variant_id = p.product_variant_ids[:1].id
