# -*- coding: utf-8 -*-
{
    "name": "Modula - Purchase",
    "version": "********.0",
    "category": "Purchase",
    "description": """
    """,
    "author": "ITMS Group",
    "website": "",
    "depends": [
        "base",
        "purchase",
        "product",
        "sale",
        "stock_account",
        "sale_purchase_stock",
        "modula_sale",
        "modula_accounting",
        "modula_fiscal_position",
        "modula_contact_extended",
    ],
    "sequence": 0,
    "data": [
        "security/ir.model.access.csv",
        # "views/product_packaging.xml",
        # "views/purchase_order_line_view.xml",
        "views/receipt_view.xml",
        "views/container_views.xml",
        # "report/purchase_order_report.xml",
        # ============================================================
        # MENU
        # ============================================================
        # 'menu/',
        "menu/menu.xml",
        "views/purchase_order_view.xml",
        "views/product_views.xml",
        "views/stock_picking_view.xml",
        "views/account_move_view.xml",
    ],
    "test": [],
    "demo": [],
    "installable": True,
    "application": True,
    "license": "LGPL-3",
}
