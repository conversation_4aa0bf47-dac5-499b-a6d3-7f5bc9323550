<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_purchase_order_form_inherit" model="ir.ui.view">
        <field name="name">view.purchase.order.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form" />
        <field name="arch" type="xml">
            <div class="oe_button_box" position="inside">
				<button class="oe_stat_button" icon="fa-cubes" type="object" name="action_open_container" invisible="container_count == 0">
					<field widget="statinfo" name="container_count" string="Container"/>
				</button>
            </div>
            <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='products']/field[@name='order_line']/list[1]/field[@name='product_id']" position="attributes">
                <attribute name="options">{"no_create":true}</attribute>
            </xpath>
            <xpath expr="//field[@name='tax_calculation_rounding_method']" position="after">
                <field name="is_mto"/>
            </xpath>
        </field>
    </record>

    <record id="purchase_order_tree" model="ir.ui.view">
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_tree" />
        <field name="arch" type="xml">
            <field name="amount_total" position="attributes">
                <attribute name="column_invisible">context.get('not_show_amount', False)</attribute>
            </field>
        </field>
    </record>

</odoo>
