# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from collections import defaultdict
from datetime import datetime

from dateutil.relativedelta import relativedelta
from odoo import SUPERUSER_ID, _, api, fields, models
from odoo.addons.stock.models.stock_rule import ProcurementException
from odoo.exceptions import UserError
from odoo.tools import float_compare, groupby


class StockRule(models.Model):
    _inherit = "stock.rule"

    # def _get_procurements_to_merge(self, procurements):
    #     # exclude service procurements from merge
    #     procurements_to_merge = [
    #         procurement
    #         for procurement in procurements
    #         if (
    #             not procurement.values.get("is_subcontract_service")
    #             and procurement.product_id.categ_id.display_name
    #             != "Common Charge / Other"
    #         )
    #     ]
    #     # services is the remaining procurements
    #     services = [
    #         [procurement]
    #         for procurement in procurements
    #         if procurement not in procurements_to_merge
    #     ]
    #     res = super(StockRule, self)._get_procurements_to_merge(procurements_to_merge)
    #     return res + services

    # def _make_po_get_domain(self, company_id, values, partner):
    #     domain = super(StockRule, self)._make_po_get_domain(company_id, values, partner)
    #     if values.get("is_subcontract_service"):
    #         # we want to create a new PO everytime for subcontract service
    #         # domain += ((1, '=', 0),)
    #         return (("id", "=", 0), ("id", "=", 1))
    #     if values.get("is_buyin") and values.get("sale_line_id"):
    #         domain += (
    #             (
    #                 "origin",
    #                 "ilike",
    #                 self.env["sale.order.line"]
    #                 .browse(values.get("sale_line_id"))
    #                 .order_id.name,
    #             ),
    #         )
    #     return domain

    # @api.model
    # def _run_buy(self, procurements):
    #     moves_values_by_company = defaultdict(list)
    #     res = super(StockRule, self)._run_buy(procurements)
    #     for procurement, rule in procurements:
    #         move_values = {}
    #         internal_transfer_rule = self.env["stock.rule"].search(
    #             [
    #                 ("name", "ilike", "internal transfer"),
    #                 ("company_id", "=", procurement.company_id.id),
    #             ],
    #             limit=1,
    #         )
    #         if (
    #             procurement.values.get("is_subcontract_service")
    #             or procurement.product_id.categ_id.display_name
    #             == "Common Charge / Other"
    #         ):
    #             # now we need make sure that the rule is an internal transfer
    #             if not internal_transfer_rule:
    #                 raise UserError(
    #                     _(
    #                         "No internal transfer rule found! Please contact your system administrator."
    #                     )
    #                 )
    #             sale_line_id = procurement.values.get("sale_line_id")
    #             sale_line = False
    #             if sale_line_id:
    #                 sale_line = self.env["sale.order.line"].browse(sale_line_id)
    #                 if not sale_line or sale_line.delivery_method != "transfer":
    #                     continue
    #             move_values = internal_transfer_rule._get_stock_move_values(
    #                 *procurement
    #             )
    #             if sale_line:
    #                 move_values["location_dest_id"] = (
    #                     sale_line.order_id.location_id
    #                     and sale_line.order_id.location_id.id
    #                     or move_values["location_dest_id"]
    #                 )
    #                 move_values["location_id"] = (
    #                     sale_line.location_id
    #                     and sale_line.location_id.id
    #                     or move_values["location_id"]
    #                 )
    #         if move_values:
    #             moves_values_by_company[procurement.company_id.id].append(move_values)

    #     for company_id, moves_values in moves_values_by_company.items():
    #         # create the move as SUPERUSER because the current user may not have the rights to do it (mto product launched by a sale for example)
    #         moves = (
    #             self.env["stock.move"]
    #             .with_user(SUPERUSER_ID)
    #             .sudo()
    #             .with_company(company_id)
    #             .create(moves_values)
    #         )
    #         # Since action_confirm launch following procurement_group we should activate it.
    #         moves._action_confirm()
    #     return res

    def _prepare_purchase_order(self, company_id, origins, values):
        vals = super()._prepare_purchase_order(company_id, origins, values)
        vals["currency_id"] = self.env.ref("base.AUD").id
        return vals

    def _make_po_get_domain(self, company_id, values, partner):
        domain = super()._make_po_get_domain(company_id, values, partner)
        # remove currency domain
        domain = (x for x in domain if x[0] != "currency_id")
        return domain
