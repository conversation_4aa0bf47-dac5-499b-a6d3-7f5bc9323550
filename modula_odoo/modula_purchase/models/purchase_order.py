# -*- coding: utf-8 -*-

from odoo import _, api, fields, models
from odoo.exceptions import UserError


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    container_id = fields.Many2one(
        "container.container", string="Container", copy=False
    )
    container_count = fields.Integer(
        string="Container Count", compute="_compute_container_count"
    )
    is_mto = fields.Boolean(string="Is MTO", compute="_compute_is_mto")

    def _compute_is_mto(self):
        for rec in self:
            rec.is_mto = (
                rec._get_sale_orders()
                and "cancel" not in rec._get_sale_orders().mapped("state")
            )

    def _compute_container_count(self):
        for rec in self:
            rec.container_count = len(rec.container_id)

    def action_open_container(self):
        self.ensure_one()
        # add default move_ids to the context
        context = dict(self._context)
        # context.update({'default_move_ids': [(6,0,[self.id])]})
        # need to hide the moves if the user is not from HOLDING
        context.update(
            {
                "hide_move": True,
                "default_po_id": self.id,
            }
        )
        if self.container_id:
            return {
                "name": "Container",
                "type": "ir.actions.act_window",
                "view_mode": "form",
                "res_model": "container.container",
                "res_id": self.container_id.id,
                "context": context,
            }
        return False

    def button_confirm(self):
        for rec in self:
            if any(rec.order_line.filtered(lambda l: l.product_qty == 0)):
                raise UserError(
                    _(
                        "You cannot confirm a purchase order with a line with 0 quantity."
                    )
                )
            else:
                super(PurchaseOrder, rec).button_confirm()
        return True

    @api.depends("order_line.sale_order_id")
    def _compute_sale_order_count(self):
        super()._compute_sale_order_count()
        for order in self:
            order_ids = self._get_sale_orders()
            so_name = order.origin.split("/")[0] if order.origin else False
            if not so_name:
                continue
            if not order_ids:
                order_ids = self.env["sale.order"].search([("name", "=", so_name)])
            else:
                if so_name:
                    additional_so = self.env["sale.order"].search(
                        [("name", "=", so_name)]
                    )
                    order_ids |= additional_so
            order.sale_order_count = len(order_ids)

    def action_view_sale_orders(self):
        self.ensure_one()
        sale_order_ids = self._get_sale_orders()
        so_name = self.origin.split("/")[0] if self.origin else False

        if so_name:
            if not sale_order_ids:
                sale_order_ids = self.env["sale.order"].search([("name", "=", so_name)])
            else:
                additional_po = self.env["sale.order"].search([("name", "=", so_name)])
                sale_order_ids |= additional_po
        sale_order_ids = sale_order_ids.ids
        if not sale_order_ids:
            so_name = self.origin.split("/")[0] if self.origin else False
            if so_name:
                so = self.env["sale.order"].search([("name", "=", so_name)])
                sale_order_ids = so.ids
        action = {
            "res_model": "purchase.order",
            "type": "ir.actions.act_window",
        }
        action = {
            "res_model": "sale.order",
            "type": "ir.actions.act_window",
        }
        if len(sale_order_ids) == 1:
            action.update(
                {
                    "view_mode": "form",
                    "res_id": sale_order_ids[0],
                }
            )
        else:
            action.update(
                {
                    "name": _("Sources Sale Orders %s", self.name),
                    "domain": [("id", "in", sale_order_ids)],
                    "view_mode": "list,form",
                }
            )
        return action

    @api.onchange("partner_id", "company_id")
    def onchange_partner_id(self):
        # force the currency to AUD
        self = self.with_company(self.company_id)
        aud_currency = self.env.ref("base.AUD")
        self.currency_id = aud_currency.id
        return {}

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        return res
