# -*- coding: utf-8 -*-
from odoo import SUPERUSER_ID, _, api, fields, models
from odoo.tools import SQL, float_compare


class StockPicking(models.Model):
    _inherit = "stock.picking"

    def _action_done(self):
        res = super()._action_done()
        so_ids = self.filtered(lambda x: x.picking_type_code == "outgoing").mapped(
            "sale_id"
        )
        if so_ids:
            so_ids._check_if_need_post_delivery_mail()
        return res

    def action_open_delivery_confirm_mail(self):
        lang = self.env.context.get("lang")

        ctx = {
            "default_model": "stock.picking",
            "default_res_ids": self.ids,
            "default_composition_mode": "comment",
            "default_template_id": self.env.ref(
                "modula_sale_stock.mail_template_delivery_schedule"
            ).id,
            "email_notification_allow_footer": True,
        }

        action = {
            "type": "ir.actions.act_window",
            "view_mode": "form",
            "res_model": "mail.compose.message",
            "views": [(False, "form")],
            "view_id": False,
            "target": "new",
            "context": ctx,
        }
        return action
