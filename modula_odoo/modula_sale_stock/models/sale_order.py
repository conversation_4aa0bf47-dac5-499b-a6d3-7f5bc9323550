# -*- coding: utf-8 -*-
from odoo import SUPERUSER_ID, _, api, fields, models
from odoo.tools import SQL, float_compare


class SaleOrder(models.Model):
    _inherit = "sale.order"

    is_show_stock_button = fields.Boolean(
        string="Is Show Stock Button",
        compute="_compute_is_show_stock_button",
    )

    @api.depends("state")
    def _compute_is_show_stock_button(self):
        for rec in self:
            rec.is_show_stock_button = False
            if rec.state in ("draft", "sent") and rec.order_line.filtered(
                lambda x: x.stock_location_quant_manager_id
            ):
                rec.is_show_stock_button = True

    @api.depends("warehouse_id.lot_stock_id", "branch_id", "order_line.location_id")
    def _compute_location_id(self):
        super()._compute_location_id()

        def Recursion(location_ids):
            for location_id in location_ids:
                if (
                    location_id.branch_id == order.branch_id
                    and not location_id.is_display_stock
                ):
                    return location_id
            if not location_ids.child_ids:
                return False
            return Recursion(location_ids.child_ids)

        for order in self:
            location_id = order.warehouse_id.lot_stock_id
            location_ids = self.env["stock.location"].search(
                [("branch_id", "=", order.branch_id.id)]
            )
            if order.branch_id:
                location_id = Recursion(location_ids)
            order.location_id = location_id
            # for line in order.order_line:
            #     if not line.location_id and order.location_id:
            #         line.location_id = order.location_id.id

    def _get_today(self):
        return fields.Date.context_today(self)

    def _check_if_need_post_delivery_mail(self):
        for so in self:
            if all(
                picking.state == "done"
                for picking in so.picking_ids.filtered(
                    lambda x: x.picking_type_code == "outgoing"
                )
            ):
                so._send_post_delivery_mail()

    def _send_post_delivery_mail(self):
        """Send a mail to the customer

        Note: self.ensure_one()

        :param mail.template mail_template: the template used to generate the mail
        :return: None
        """
        self.ensure_one()
        mail_template = self.env.ref(
            "modula_sale_stock.mail_template_post_delivery", raise_if_not_found=False
        )
        if not mail_template:
            return
        if self.env.su:
            # sending mail in sudo was meant for it being sent from superuser
            self = self.with_user(SUPERUSER_ID)

        self.with_context(force_send=True).message_post_with_source(
            mail_template,
            subtype_xmlid="mail.mt_comment",
        )

    def action_open_balance_due_mail(self):

        ctx = {
            "default_model": "sale.order",
            "default_res_ids": self.ids,
            "default_composition_mode": "comment",
            "default_template_id": self.env.ref(
                "modula_sale_stock.mail_template_balance_due"
            ).id,
            "email_notification_allow_footer": True,
        }

        action = {
            "type": "ir.actions.act_window",
            "view_mode": "form",
            "res_model": "mail.compose.message",
            "views": [(False, "form")],
            "view_id": False,
            "target": "new",
            "context": ctx,
        }
        return action


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    stock_location_quant_manager_id = fields.Many2one(
        "stock.location.quant.manager",
        string="Stock Location Quant Manager",
        compute="_compute_stock_location_quant_manager_id",
        store=True,
        ondelete="cascade",
    )
    delivery_method = fields.Selection(
        compute="_compute_delivery_method",
        store=True,
        readonly=False,
        # default="delivery",
    )
    route_id = fields.Many2one(compute="_compute_route_id", store=True)
    is_mto_route_product = fields.Boolean(
        compute="_compute_is_mto_route_product", store=True
    )
    location_id = fields.Many2one(
        related="stock_location_quant_manager_id.location_id", store=True
    )
    destination_location_id = fields.Many2one(
        related="stock_location_quant_manager_id.destination_location_id", store=True
    )
    delivery_d_run = fields.Selection(
        [
            ("1", "1"),
            ("2", "2"),
            ("3", "3"),
            ("4", "4"),
        ],
        string="D'Run",
    )

    @api.depends("product_id", "product_id.route_ids", "order_id.website_id")
    def _compute_is_mto_route_product(self):
        for rec in self:
            mto_available_route = self.env.ref(
                "modula_sale_stock.route_warehouse0_mto_available",
                raise_if_not_found=False,
            )
            mto_website_route = self.env.ref(
                "modula_sale_stock.route_warehouse0_mto_website",
                raise_if_not_found=False,
            )
            is_mto_route_product = (
                True
                if mto_available_route
                and mto_available_route in rec.product_id.route_ids
                else False
            )
            if rec.order_id.website_id:
                is_mto_route_product = (
                    True
                    if mto_website_route
                    and mto_website_route in rec.product_id.route_ids
                    else False
                )
            rec.is_mto_route_product = is_mto_route_product

    @api.depends(
        "is_mto_route_product",
        "delivery_method",
        "destination_location_id",
        "stock_location_quant_manager_id.location_id",
    )
    def _compute_route_id(self):
        for rec in self:
            rec.route_id = False
            if rec.is_mto_route_product:
                rec.route_id = self.env.ref("stock.route_warehouse0_mto")
            if (
                rec.location_id
                and not rec.order_id.website_id
                and rec.destination_location_id
            ):
                location_dest_id = rec.destination_location_id
                rec.route_id = False
                if rec.location_id and rec.location_id.is_display_stock:
                    rec.route_id = self.env.ref(
                        "modula_sale_stock.route_warehouse0_transfer",
                        raise_if_not_found=False,
                    )
                elif rec.location_id:
                    if (
                        "store" in rec.location_id.display_name.lower()
                        and "store" in location_dest_id.display_name.lower()
                    ):
                        rec.route_id = False
                    elif (
                        "warehouse" in rec.location_id.display_name.lower()
                        and "warehouse" in location_dest_id.display_name.lower()
                    ):
                        rec.route_id = False
                    else:
                        rec.route_id = self.env.ref(
                            "modula_sale_stock.route_warehouse0_transfer",
                            raise_if_not_found=False,
                        )
            if rec.route_id == self.env.ref("stock.route_warehouse0_mto"):
                rec.location_id = rec.order_id.warehouse_id.lot_stock_id
            else:
                rec.location_id = rec.stock_location_quant_manager_id.location_id

                # elif location_dest_id and rec.location_id != location_dest_id:
                #     rec.route_id = self.env.ref(
                #         "modula_sale_stock.route_warehouse0_transfer",
                #         raise_if_not_found=False,
                #     )

    def _prepare_stock_location_quant_manager(self):
        self.ensure_one()
        if (
            self.product_id.stock_quant_ids
            and self.product_id.sale_ok
            and self.product_id.type != "service"
        ):
            vals = [
                {
                    "location_id": line.location_id.id,
                    "qty_onhand": line.available_quantity,
                    "quantity": self.product_uom_qty,
                    "is_display_stock": line.location_id.is_display_stock,
                }
                for line in self.product_id.stock_quant_ids.filtered(
                    lambda x: x.location_id.usage == "internal"
                )
            ]
            lines = self.env["stock.location.quant.line"].create(vals)
            if not self.stock_location_quant_manager_id:
                vals_stock_location_quant_manager = {
                    "line_ids": [
                        (6, 0, lines.filtered(lambda x: not x.is_display_stock).ids)
                    ],
                    "line_ids_display_stock": [
                        (6, 0, lines.filtered(lambda x: x.is_display_stock).ids)
                    ],
                    "sale_line_id": self.id,
                    "quantity": self.product_uom_qty,
                    "product_id": self.product_id.id,
                    "delivery_method": "delivery",
                }
                self.stock_location_quant_manager_id = self.env[
                    "stock.location.quant.manager"
                ].create(vals_stock_location_quant_manager)
            else:
                vals_stock_location_quant_manager = {
                    "line_ids": [
                        (6, 0, lines.filtered(lambda x: not x.is_display_stock).ids)
                    ],
                    "line_ids_display_stock": [
                        (6, 0, lines.filtered(lambda x: x.is_display_stock).ids)
                    ],
                    "product_id": self.product_id.id,
                    "quantity": self.product_uom_qty,
                }
                self.stock_location_quant_manager_id.write(
                    vals_stock_location_quant_manager
                )
        else:
            self.stock_location_quant_manager_id = False

    @api.depends("product_id", "product_id.stock_quant_ids")
    def _compute_stock_location_quant_manager_id(self):
        for rec in self:
            rec._prepare_stock_location_quant_manager()

    @api.onchange("product_uom_qty")
    def _onchange_product_uom_qty(self):
        for rec in self:
            if rec.stock_location_quant_manager_id:
                rec.stock_location_quant_manager_id.quantity = rec.product_uom_qty
                if rec.stock_location_quant_manager_id.line_id:
                    rec.stock_location_quant_manager_id.line_id.quantity = (
                        rec.product_uom_qty
                    )

    @api.depends("order_id.carrier_id", "product_id")
    def _compute_delivery_method(self):
        for rec in self:
            rec.delivery_method = "delivery"
            if not rec.product_id.sale_ok or rec.product_id.type == "service":
                rec.delivery_method = False
                return
            if (
                rec.order_id.website_id
                and rec.order_id.carrier_id
                and "pickup" in rec.order_id.carrier_id.display_name.lower()
            ):
                rec.delivery_method = "pickup"

    def _get_procurement_group_key(self):
        """Return a key with priority to be used to regroup lines in multiple
        procurement groups

        """
        priority = 11
        key = super()._get_procurement_group_key()
        # Check priority
        if key[0] >= priority:
            return key
        if self.delivery_d_run:
            return priority, self.delivery_d_run
        else:
            return priority

    def _prepare_procurement_group_vals(self):
        vals = super()._prepare_procurement_group_vals()
        # for compatibility with sale_quotation_sourcing
        if self._get_procurement_group_key()[0] == 10:
            if self.delivery_d_run:
                vals["name"] += "/" + self.delivery_d_run
        return vals

    def _prepare_procurement_values(self, group_id=False):
        """Prepare specific key for moves or other components
        that will be created from a stock rule
        comming from a sale order line. This method could be
        override in order to add other custom key that could
        be used in move/po creation.
        """
        values = super()._prepare_procurement_values(group_id)
        self.ensure_one()
        # values['sale_line_id'] = self.id
        if not self.product_id.sale_ok or self.product_id.type == "service":
            return values
        if self.route_id == self.env.ref("stock.route_warehouse0_mto"):
            values["pickup_type"] = "mto-%s" % self.delivery_method
            values["location_id"] = self.order_id.warehouse_id.lot_stock_id
            # if self.location_id or self.order_id.location_id:
            #     values["location_id"] = (
            #         self.location_id.id or self.order_id.location_id.id
            #     )
        elif self.delivery_method:
            if self.location_id:
                values["location_id"] = self.location_id.id
            values["pickup_type"] = self.delivery_method
            if self.route_id == self.env.ref(
                "modula_sale_stock.route_warehouse0_transfer"
            ):
                values["pickup_type"] = "transfer-%s" % self.delivery_method
                values["location_int_id"] = self.location_id.id
                values["sale_line_id"] = self.id
        return values

    def _action_launch_stock_rule(self, previous_product_uom_qty=False):
        """
        Launch procurement group run method.
        """
        if self._context.get("skip_procurement"):
            return True
        precision = self.env["decimal.precision"].precision_get(
            "Product Unit of Measure"
        )
        procurements = []
        groups = {}
        if not previous_product_uom_qty:
            previous_product_uom_qty = {}
        for line in self:
            line = line.with_company(line.company_id)
            if (
                line.state != "sale"
                or line.order_id.locked
                or line.product_id.type != "consu"
            ):
                continue
            qty = line._get_qty_procurement(previous_product_uom_qty)
            if (
                float_compare(qty, line.product_uom_qty, precision_digits=precision)
                == 0
            ):
                continue

            group_id = line._get_procurement_group()

            # Group the sales order lines with same procurement group
            # according to the group key
            for order_line in line.order_id.order_line:
                g_id = order_line.procurement_group_id or False
                if g_id:
                    groups[order_line._get_procurement_group_key()] = g_id
            if not group_id:
                group_id = groups.get(line._get_procurement_group_key())

            if not group_id:
                vals = line._prepare_procurement_group_vals()
                group_id = self.env["procurement.group"].create(vals)
            else:
                # In case the procurement group is already created and the
                # order was cancelled, we need to update certain values
                # of the group.
                updated_vals = {}
                if group_id.partner_id != line.order_id.partner_shipping_id:
                    updated_vals.update(
                        {"partner_id": line.order_id.partner_shipping_id.id}
                    )
                if group_id.move_type != line.order_id.picking_policy:
                    updated_vals.update({"move_type": line.order_id.picking_policy})
                if updated_vals:
                    group_id.write(updated_vals)
            line.procurement_group_id = group_id

            values = line._prepare_procurement_values(group_id=group_id)
            product_qty = line.product_uom_qty - qty

            line_uom = line.product_uom
            quant_uom = line.product_id.uom_id
            origin = (
                f"{line.order_id.name} - {line.order_id.client_order_ref}"
                if line.order_id.client_order_ref
                else line.order_id.name
            )
            product_qty, procurement_uom = line_uom._adjust_uom_quantities(
                product_qty, quant_uom
            )
            procurements += line._create_procurements(
                product_qty, procurement_uom, origin, values
            )
            # We store the procured quantity in the UoM of the line to avoid
            # duplicated procurements, specially for dropshipping and kits.
            previous_product_uom_qty[line.id] = line.product_uom_qty
        if procurements:
            self.env["procurement.group"].run(procurements)
        # This next block is currently needed only because the scheduler trigger is done
        # by picking confirmation rather than stock.move confirmation
        orders = self.mapped("order_id")
        for order in orders:
            pickings_to_confirm = order.picking_ids.filtered(
                lambda p: p.state not in ["cancel", "done"]
            )

            for line_so in self:
                # if not line_so.route_id:
                #     continue
                if line_so.route_id and "transfer" in line_so.route_id.name.lower():
                    pickings = pickings_to_confirm.filtered(
                        lambda p: p.picking_type_code == "internal"
                    )

                    if pickings.mapped("move_ids") and line_so.destination_location_id:
                        pickings.mapped("move_ids").write(
                            {"location_dest_id": line_so.destination_location_id.id}
                        )
                        pickings.write(
                            {"location_dest_id": line_so.destination_location_id.id}
                        )
                        move_lines = pickings_to_confirm.mapped("move_ids").filtered(
                            lambda m: m.sale_line_id
                            == pickings.mapped("move_ids")[0].sale_line_id
                            and m.id not in pickings.mapped("move_ids").ids
                        )
                        if move_lines:
                            move_lines.write(
                                {"location_id": line_so.destination_location_id.id}
                            )
                            move_lines.mapped("picking_id").write(
                                {"location_id": line_so.destination_location_id.id}
                            )
                elif line_so.delivery_method in ["pickup", "delivery"]:
                    pickings = pickings_to_confirm.filtered(
                        lambda p: p.picking_type_code == "outgoing"
                    )
                else:
                    continue

                move_lines = pickings.mapped("move_ids").filtered(
                    lambda m: m.sale_line_id == line_so
                )

                if line_so.route_id == self.env.ref("stock.route_warehouse0_mto"):
                    continue

                location_assign_id = (
                    line_so.location_id
                    or line_so.order_id.location_id
                    or line_so.order_id.warehouse_id.lot_stock_id
                )
                if move_lines and location_assign_id:
                    move_lines.write({"location_id": location_assign_id.id})
                    move_lines.mapped("picking_id").write(
                        {"location_id": location_assign_id.id}
                    )

        return super(
            SaleOrderLine, self.with_context(sale_group_by_line=True)
        )._action_launch_stock_rule(previous_product_uom_qty=previous_product_uom_qty)

    def action_open_available_stock(self):
        """
        Open the available stock for the sale order lines
        """
        for rec in self:
            if not rec.stock_location_quant_manager_id:
                continue
            return {
                "name": "Available Stock On Hand & Delivery Method",
                "type": "ir.actions.act_window",
                "res_model": "stock.location.quant.manager",
                "view_mode": "form",
                "view_id": self.env.ref(
                    "modula_sale_stock.stock_location_quant_manager_form_view"
                ).id,
                "res_id": rec.stock_location_quant_manager_id.id,
                "context": {
                    "dialog_size": "extra-large",
                    "active_id": rec.order_id.id,
                },
                "target": "new",
            }
