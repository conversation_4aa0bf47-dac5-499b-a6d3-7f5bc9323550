from odoo import _, api, fields, models
from odoo.exceptions import UserError


class StockLocationQuantManager(models.Model):
    _name = "stock.location.quant.manager"
    _description = "Stock Location Quant Manager"

    line_ids = fields.One2many(
        "stock.location.quant.line",
        "stock_location_quant_manager_id",
        string="Source Locations",
        domain="[('is_display_stock', '=', False)]",
    )
    line_ids_display_stock = fields.One2many(
        "stock.location.quant.line",
        "stock_location_quant_manager_display_stock_id",
        string="Source Locations Display Stock",
        domain="[('is_display_stock', '=', True)]",
    )
    line_id = fields.Many2one(
        "stock.location.quant.line",
        string="Source Location",
        compute="_compute_line_id",
        store=True,
    )
    location_id = fields.Many2one(
        "stock.location",
        string="Source Location",
        compute="_compute_line_id",
        store=True,
    )
    stock_quant_ids = fields.One2many(related="product_id.stock_quant_ids")
    is_select = fields.Boolean(related="line_id.select", store=True)
    quantity = fields.Float("Quantity", related="line_id.quantity", store=True)
    product_id = fields.Many2one("product.product", "Product")
    sale_line_id = fields.Many2one("sale.order.line")
    display_stock = fields.Boolean(default=False, string="Display Stock")
    delivery_method = fields.Selection(
        [
            ("pickup", "Pickup"),
            ("delivery", "Delivery"),
        ],
        string="Delivery Method",
        related="sale_line_id.delivery_method",
        readonly=False,
        store=True,
    )
    destination_location_id = fields.Many2one(
        "stock.location",
        string="Destination Location",
        domain="[('usage', '=', 'internal'), ('is_display_stock', '=', False)]",
    )

    @api.depends("line_ids.select", "line_ids_display_stock.select", "display_stock")
    def _compute_line_id(self):
        for rec in self:
            # Get selected lines based on the display_stock flag
            if rec.display_stock:
                selected_lines = rec.line_ids_display_stock.filtered(lambda l: l.select)
                rec.line_ids.write({"select": False})
            else:
                selected_lines = rec.line_ids.filtered(lambda l: l.select)
                rec.line_ids_display_stock.write({"select": False})

            # If there are no selected lines or more than one is selected
            if not selected_lines or len(selected_lines) != 1:
                # Unselect lines that match the current location_id
                selected_lines.filtered(
                    lambda l: l.location_id == rec.location_id
                ).write({"select": False})
                # Refilter to get remaining selected lines
                selected_lines = selected_lines.filtered(lambda l: l.select)

                if selected_lines:
                    rec.line_id = selected_lines[0]
                    rec.location_id = selected_lines[0].location_id
                else:
                    rec.line_id = False
                    rec.location_id = False
            else:
                rec.line_id = selected_lines[0]
                rec.location_id = selected_lines[0].location_id

    def write(self, vals):
        res = super(StockLocationQuantManager, self).write(vals)
        if "line_ids" in vals or "line_ids_display_stock" in vals:
            if self.line_id and self.line_id.quantity > 0 and self.is_select:
                self.sale_line_id.product_uom_qty = self.quantity
        return res


class StockLocationQuantLine(models.Model):
    _name = "stock.location.quant.line"
    _description = "Stock Location Quant Line"
    _rec_name = "display_name"
    _order = "id desc"

    stock_location_quant_manager_id = fields.Many2one(
        "stock.location.quant.manager",
        string="Stock Location Quant Manager",
    )
    stock_location_quant_manager_display_stock_id = fields.Many2one(
        "stock.location.quant.manager",
        string="Stock Location Quant Manager Display Stock",
    )
    location_id = fields.Many2one(
        "stock.location",
        string="Source Location",
    )
    is_display_stock = fields.Boolean(default=False)
    select = fields.Boolean(default=False)
    branch_id = fields.Many2one("res.branch", string="Branch")
    qty_onhand = fields.Float("On Hand Quantity", readonly=True)
    sale_line_id = fields.Many2one("sale.order.line")
    quantity = fields.Float("Quantity")
    display_name = fields.Char(compute="_compute_display_name", store=True)

    @api.depends("location_id", "qty_onhand")
    def _compute_display_name(self):
        for rec in self:
            rec.display_name = f"{rec.location_id.display_name}  [ {rec.qty_onhand} ]"
