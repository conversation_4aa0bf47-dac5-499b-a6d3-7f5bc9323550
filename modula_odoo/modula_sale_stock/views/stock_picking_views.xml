<?xml version="1.0" encoding="utf-8"?>
<odoo>
   <record id="view_picking_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='location_id']" position="attributes">
                <attribute name="domain">[('company_id', 'in', [False, company_id]), ('is_display_stock', '=', False)]</attribute>
            </xpath>
            <xpath expr="//field[@name='location_id'][2]" position="attributes">
                <attribute name="domain">[('company_id', 'in', [False, company_id]), ('is_display_stock', '=', False)]</attribute>
            </xpath>
            <xpath expr="//header" position="inside">
                <button
                    invisible="state in ('draft', 'done','cancel') or not picking_type_code == 'outgoing'"
                    name="action_open_delivery_confirm_mail" type="object" class="btn-secondary" string="Delivery Confirmation Mail" />
            </xpath>
        </field>
   </record>
</odoo>
