<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="mail_template_post_delivery" model="mail.template">
            <field name="name">Sales: Post Delivery</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="subject">Delivery complete - Order {{ object.name or 'n/a' }}</field>
            <field name="email_from">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Delivery Complete Automatic Mail</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 14px;">
        Dear <span style="font-weight:bold;" t-out="object.partner_id.name or ''"></span>,
        <br/><br/>
        We hope you're enjoying your new furniture! We're pleased to confirm that your order was successfully delivered on
        <span style="font-weight:bold;" t-out="format_date(object._get_today()) or ''"></span>.
        <br/><br/>
        Thank you once again for choosing us. If you have any questions, need further assistance, or would like care tips for your new pieces, we’re always here to help.
        <br/><br/>
        If you were happy with our service, we’d love to hear your feedback.
        <br/><br/>
        We want to ensure that you’re completely satisfied with your purchase, so if there were any issues, please feel free to contact us.
        <br/><br/>
        Warm regards,
    </p>
</div>
            </field>
            <!-- <field name="report_template_ids" eval="[(4, ref('sale.action_report_saleorder'))]"/> -->
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="False"/>
        </record>

        <record id="mail_template_delivery_schedule" model="mail.template">
            <field name="name">Delivery Schedule</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="subject">Delivery confirmed - Order {{ object.name or 'n/a' }}</field>
            <field name="email_from">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Delivery Schedule Mail</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 14px;">
        Dear <span style="font-weight:bold;" t-out="object.partner_id.name or ''"></span>,
        <br/><br/>
        We’re pleased to confirm that your furniture delivery is scheduled for
        <span style="font-weight:bold;" t-out="format_date(object.scheduled_date) or ''"></span>
        between [Time Window].
        <br/><br/>
        Please ensure someone is available to receive the delivery at <span style="font-weight:bold;" t-out="object.partner_id._display_address() or ''"></span>,
        and that the space is clear and accessible. If you have any special instructions or need to reschedule, kindly let us know as soon as possible.
        <br/><br/>
        Thank you again for your order—we look forward to delivering your furniture!
        <br/><br/>
        Warm regards,
    </p>
</div>
            </field>
            <!-- <field name="report_template_ids" eval="[(4, ref('sale.action_report_saleorder'))]"/> -->
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="False"/>
        </record>
        <record id="mail_template_balance_due" model="mail.template">
            <field name="name">Balance Due</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="subject">Balance Due - Order {{ object.name or 'n/a' }}</field>
            <field name="email_from">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Balance Due Mail</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 14px;">
        Dear <span style="font-weight:bold;" t-out="object.partner_id.name or ''"></span>,
        <br/><br/>
        We are excited to inform you that your furniture is now ready for delivery.
        <br/><br/>
        To ensure timely processing and delivery, please arrange final payment by [Due Date]. Payment can be made via [Payment Methods] or by contacting your store.
        <br/><br/>
        Should you have any questions or need assistance, feel free to contact us—we’re happy to help
        <br/><br/>
        Once final payment is made, we will contact you with delivery confirmation.
        <br/><br/>
        Thank you again for your business!
        <br/><br/>
        Warm regards,
    </p>
</div>
            </field>
            <!-- <field name="report_template_ids" eval="[(4, ref('sale.action_report_saleorder'))]"/> -->
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="False"/>
        </record>
</odoo>
