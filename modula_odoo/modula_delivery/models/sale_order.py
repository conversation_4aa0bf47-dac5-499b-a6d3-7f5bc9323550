# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging

from odoo import _, api, fields, models
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = "sale.order"

    unavailable_delivery_postcode = fields.Boolean(
        string="Unavailable Delivery Postcode",
        default=False,
        help="Checked when the delivery is currently unavailable.",
    )
    is_delivery_mail_sent = fields.Boolean(
        string="Delivery Unavailable Email Sent",
        default=False,
        help="Checked when an email is sent to inform that delivery is currently unavailable.",
    )

    def _send_mail_delivery_unavailable_sales_team(self):
        # orders = self.env["sale.order"].search(
        #     [
        #         ("website_id", "!=", False),
        #         ("is_delivery_mail_sent", "=", False),
        #         ("state", "=", "draft"),
        #         ("unavailable_delivery_postcode", "=", True),
        #     ]
        # )

        # if not orders:
        #     return

        # Send email to sales person (or sales team)
        # for order in orders:
        template = self.env.ref(
            "modula_delivery.email_template_delivery_unavailable",
            raise_if_not_found=False,
        )
        if template:
            template.send_mail(self.id, force_send=True)
            self.is_delivery_mail_sent = True
        else:
            _logger.warning(
                "Delivery unavailable email template not found. Cannot notify sales team."
            )

    def _get_delivery_methods(self):
        """
        Override to include postcode-based delivery methods.
        This method is called to get available delivery methods for the order.
        """
        # Get the standard delivery methods first
        carriers = super()._get_delivery_methods()
        zip_code = self.partner_shipping_id.zip if self.partner_shipping_id else False
        self.unavailable_delivery_postcode = False

        # Giao hàng nội địa theo postcode cấu hình
        if zip_code and self.website_id:
            postcode_carriers = carriers.filtered(
                lambda c: c.delivery_type == "postcode"
            )
            matched_carriers = postcode_carriers.filtered(
                lambda c: any(
                    p.postcode_from == zip_code for p in c.postcode_pricelist_ids
                )
            )

            if matched_carriers:
                return matched_carriers
            else:
                self.unavailable_delivery_postcode = True
                return self.env["delivery.carrier"]
        else:
            return carriers

    def _has_postcode_pricelist(self, carrier):
        """
        Check if a carrier has any postcode pricelist entries.

        :param carrier: delivery.carrier record
        :return: True if carrier has postcode pricelists
        """
        return bool(
            self.env["delivery.postcode.pricelist"].search(
                [("delivery_carrier_id", "=", carrier.id), ("active", "=", True)],
                limit=1,
            )
        )

    def _get_postcode_delivery_price(self, carrier):
        """
        Get delivery price based on postcode pricelist.

        :param carrier: delivery.carrier record
        :return: dict with success, price, error_message, warning_message
        """
        if not self.partner_shipping_id:
            return {
                "success": False,
                "price": 0.0,
                "error_message": _("No delivery address or postcode specified."),
                "warning_message": False,
            }

        postcode = self.partner_shipping_id.zip
        country = self.partner_shipping_id.country_id
        state = self.partner_shipping_id.state_id
        city = self.partner_shipping_id.city

        # Look up price in postcode pricelist
        price = self.env["delivery.postcode.pricelist"].find_delivery_price(
            carrier, postcode, country, state, city
        )

        if price is not False:
            return {
                "success": True,
                "price": price,
                "error_message": False,
                "warning_message": False,
            }
        elif carrier.postcode_fixed_price > 0:
            return {
                "success": True,
                "price": carrier.postcode_fixed_price,
                "error_message": False,
                "warning_message": False,
            }
        else:
            return {
                "success": False,
                "price": 0.0,
                "error_message": _(
                    "No delivery price found for postcode %s with carrier %s."
                )
                % (postcode, carrier.name),
                "warning_message": False,
            }

    def set_delivery_line(self, carrier, amount):
        """
        Override to use postcode-based pricing when available.

        :param carrier: delivery.carrier record
        :param amount: delivery amount (may be overridden by postcode pricing)
        """
        # Check if this carrier has postcode-based pricing
        if self._has_postcode_pricelist(carrier):
            postcode_result = self._get_postcode_delivery_price(carrier)

            if postcode_result["success"]:
                # Use postcode-based price instead of the provided amount
                amount = postcode_result["price"]
                _logger.info(
                    "Using postcode-based price %.2f for carrier %s on order %s",
                    amount,
                    carrier.name,
                    self.name,
                )
            else:
                # If postcode lookup fails, raise an error
                raise UserError(postcode_result["error_message"])

        # Call the parent method with the (possibly updated) amount
        return super().set_delivery_line(carrier, amount)

    @api.onchange("partner_shipping_id")
    def _onchange_partner_shipping_id(self):
        """
        Recompute delivery when shipping address changes.
        This ensures delivery costs are updated when postcode changes.
        """
        # Call parent onchange first
        result = (
            super()._onchange_partner_shipping_id()
            if hasattr(super(), "_onchange_partner_shipping_id")
            else {}
        )

        # If we have a carrier and delivery line, recompute delivery cost
        if self.carrier_id and self.order_line.filtered("is_delivery"):
            if self._has_postcode_pricelist(self.carrier_id):
                # Mark for delivery price recomputation
                self.recompute_delivery_price = True

                # Try to update delivery price immediately
                try:
                    postcode_result = self._get_postcode_delivery_price(self.carrier_id)
                    if postcode_result["success"]:
                        # Update existing delivery line
                        delivery_lines = self.order_line.filtered("is_delivery")
                        if delivery_lines:
                            delivery_lines[0].price_unit = postcode_result["price"]
                            _logger.info(
                                "Updated delivery price to %.2f for order %s due to address change",
                                postcode_result["price"],
                                self.name,
                            )
                except Exception as e:
                    _logger.warning(
                        "Failed to update delivery price for order %s: %s",
                        self.name,
                        str(e),
                    )

        return result

    def _compute_amount_total_without_delivery(self):
        """
        Override to ensure proper calculation without delivery costs.
        This method is used by the delivery system to calculate order total
        excluding delivery charges.
        """
        # Use the parent implementation which already handles this correctly
        return super()._compute_amount_total_without_delivery()
