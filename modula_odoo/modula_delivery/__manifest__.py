# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    "name": "Modula Delivery",
    "version": "********.0",
    "category": "Inventory/Delivery",
    "summary": "Postcode-based delivery pricing for Odoo 18",
    "description": """
Modula Delivery - Postcode-Based Delivery Pricing
==================================================

This module implements a postcode-based delivery pricing system that integrates
with Odoo's existing delivery functionality. It allows delivery providers to
store charges based on postal codes and automatically applies these charges
to sales orders.

    """,
    "author": "Modula",
    "website": "https://www.modula.com",
    "depends": ["delivery", "sale", "website_sale"],
    "data": [
        "security/ir.model.access.csv",
        "data/mail_template.xml",
        "data/ir_cron_data.xml",
        "views/delivery_postcode_pricelist_views.xml",
        "views/deilvery_carrier_views.xml",
        "views/template.xml",
    ],
    "installable": True,
    "auto_install": False,
    "application": False,
    "license": "LGPL-3",
}
