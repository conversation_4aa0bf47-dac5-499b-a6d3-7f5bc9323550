<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="email_template_delivery_unavailable" model="mail.template">
        <field name="name">Delivery Unavailable Notification</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="subject">[Action Required] Customer Outside Delivery Area for Order: {{object.name}}</field>
        <field name="email_from">{{ (object.company_id.email_formatted or user.email_formatted or '') }}</field>
        <field name="email_to">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
        <field name="auto_delete" eval="True"/>
        <field name="body_html" type="html">
            <p>Hello <t t-out="object.user_id.name or 'Sales Team'"/>,</p>
            <p>A customer has placed a quotation <b t-out="object.name"></b> from website with a delivery address outside the configured delivery area (postcode: <b t-out="object.partner_shipping_id.zip"></b>, state: <b t-out="object.partner_shipping_id.state_id.name"></b>).</p>
            <p>Please contact the customer at <b t-out="object.partner_id.phone or object.partner_id.mobile or object.partner_id.email"></b> to discuss delivery options and complete the order.</p>
            <p>Customer Name: <b t-out="object.partner_id.name"></b><br/>
            Address: <b t-out="object.partner_shipping_id.display_name"></b></p>
            <p>Thank you,<br/>Odoo System</p>
        </field>
    </record>
</odoo>
