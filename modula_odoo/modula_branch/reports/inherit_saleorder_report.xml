<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_inherit_sale_order" inherit_id="sale.report_saleorder_document">
        <xpath expr="//t[@t-call='web.external_layout']"  position="attributes">
            <attribute name="t-call">web.external_layout_bubble</attribute>
        </xpath>
    </template>

    <template id="external_layout_bubble_inherit" inherit_id="web.external_layout_bubble">
        <xpath expr="//t/div[1]" position="before">
            <t t-if="not o" t-set="o" t-value="doc"/>

            <t t-if="not company">
                <!-- Multicompany -->
                <t t-if="company_id">
                    <t t-set="company" t-value="company_id"/>
                </t>
                <t t-elif="o and 'company_id' in o and o.company_id.sudo()">
                    <t t-set="company" t-value="o.company_id.sudo()"/>
                </t>
                <t t-else="else">
                    <t t-set="company" t-value="res_company"/>
                </t>
            </t>
        </xpath>
        <xpath expr="//img" position="attributes">
            <attribute name="class">o_company_logo_bigger</attribute>
        </xpath>
    </template>
</odoo>
