# -*- coding: utf-8 -*-

from odoo import api, fields, models


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    delivery_group_id = fields.Many2one(
        "delivery.group",
        string="Delivery Group",
        help="Delivery group this line belongs to (for delivery lines only)",
    )
    delivery_group_type = fields.Selection(
        [("mto", "Make to Order"), ("stock", "In Stock"), ("custom", "Custom")],
        string="Delivery Group Type",
        compute="_compute_delivery_group_type",
        store=True,
        help="Type of delivery group based on product routing",
    )
    select = fields.Boolean(
        string="Select",
        help="Select this order line",
        copy=False,
    )
    line_stock_id = fields.Many2one(
        "stock.location.quant.line",
        string="Line Stock",
        domain="[('stock_location_quant_manager_id', '=', stock_location_quant_manager_id)]",
    )
    line_display_stock_id = fields.Many2one(
        "stock.location.quant.line",
        string="Line Display Stock",
        domain="[('stock_location_quant_manager_display_stock_id', '=', stock_location_quant_manager_id)]",
    )
    location_id = fields.Many2one(
        "stock.location",
        string="Source Location",
    )

    @api.depends("product_id", "route_id", "is_mto", "is_delivery")
    def _compute_delivery_group_type(self):
        """Determine delivery group based on product routing"""
        for line in self:
            if line.is_delivery:
                # Delivery lines don't have a group type based on routing
                line.delivery_group_type = False
            elif line.is_mto:
                line.delivery_group_type = "mto"
            else:
                line.delivery_group_type = "stock"

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to handle delivery group assignment"""
        lines = super().create(vals_list)

        # Process each created line
        for line in lines:
            # If this is a delivery line and order has multi-delivery enabled
            if line.is_delivery and line.order_id.multi_delivery_enabled:
                # The delivery_group_id should be set by the calling code
                pass

        return lines

    def write(self, vals):
        """Override write to handle delivery group changes"""
        result = super().write(vals)

        # If product or routing changed, recompute delivery group type
        if any(field in vals for field in ["product_id", "route_id"]):
            self._compute_delivery_group_type()

        return result

    def unlink(self):
        """Override unlink to handle delivery group cleanup"""
        # If deleting delivery lines, clean up delivery groups
        groups_to_check = False
        order_id = self.order_id
        if self.filtered("is_delivery"):
            groups_to_check = self.mapped("delivery_group_id")
        result = super().unlink()

        if groups_to_check:
            for line in order_id.order_line.filtered(
                lambda l: l.delivery_group_id in groups_to_check
            ):
                if line.display_type == "line_section":
                    line.unlink()
                else:
                    line.write({"delivery_group_id": False, "select": False})

        return result
