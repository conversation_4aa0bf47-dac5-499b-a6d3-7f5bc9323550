# -*- coding: utf-8 -*-

from odoo import _, api, fields, models
from odoo.exceptions import UserError


class SaleOrder(models.Model):
    _inherit = "sale.order"

    # Multi-delivery fields
    multi_delivery_enabled = fields.<PERSON><PERSON>an(
        string="Multiple Delivery Charges",
        help="Enable multiple delivery charges based on product routing",
        compute="_compute_multi_delivery_enabled",
    )

    # Computed fields for UI
    has_mto_products = fields.<PERSON><PERSON><PERSON>(
        string="Has MTO Products",
        compute="_compute_product_routing",
        help="Order contains Make-to-Order products",
    )
    has_stock_products = fields.<PERSON><PERSON><PERSON>(
        string="Has Stock Products",
        compute="_compute_product_routing",
        help="Order contains In-Stock products",
    )
    can_disable_multi_delivery = fields.<PERSON><PERSON><PERSON>(
        string="Can Disable Multi Delivery",
        compute="_compute_can_disable_multi_delivery",
        help="Order is eligible for multiple delivery charges",
    )

    def _compute_multi_delivery_enabled(self):
        """Compute multi-delivery enabled"""
        for order in self:
            order.multi_delivery_enabled = order.order_line.filtered(
                lambda l: l.product_id
                and l.product_id.type == "consu"
                and not l.delivery_group_id
            )

    @api.depends("order_line.is_mto", "order_line.is_delivery")
    def _compute_product_routing(self):
        """Compute whether order has MTO and/or stock products"""
        for order in self:
            product_lines = order.order_line.filtered(lambda l: not l.is_delivery)
            order.has_mto_products = any(line.is_mto for line in product_lines)
            order.has_stock_products = any(not line.is_mto for line in product_lines)

    def _compute_can_disable_multi_delivery(self):
        """Determine if order can use multiple delivery charges"""
        for order in self:
            # Can use multi-delivery if:
            # 1. Order has both MTO and stock products, OR
            # 2. Order is in draft/sent state (for manual grouping)
            order.can_disable_multi_delivery = order.order_line.filtered(
                lambda l: l.delivery_group_id
            )

    def _remove_delivery_line(self):
        """Enhanced to handle multi-delivery scenario"""
        if self.multi_delivery_enabled:
            # Only remove delivery lines not associated with groups
            delivery_lines = self.order_line.filtered(
                lambda l: l.is_delivery and not l.delivery_group_id
            )
        else:
            # Standard behavior: remove all delivery lines
            delivery_lines = self.order_line.filtered("is_delivery")

        if delivery_lines:
            to_delete = delivery_lines.filtered(lambda x: x.qty_invoiced == 0)
            if to_delete:
                to_delete.unlink()

    def get_delivery_group_ids(self):
        """Get delivery group ids"""
        return self.order_line.filtered(lambda l: l.is_delivery).mapped(
            "delivery_group_id"
        )

    def set_multi_delivery_lines(self, delivery_groups):
        """Create multiple delivery lines based on groups"""
        self.ensure_one()

        # Create delivery lines for each group
        for group in delivery_groups:
            if not group.carrier_id:
                continue
            delivery_line = self._create_delivery_line(
                group.carrier_id, group.delivery_price
            )
            if not delivery_line:
                continue
            delivery_line.write({"delivery_group_id": group.id})
            group.order_line_ids.write({"delivery_group_id": group.id})

            # Create delivery section
            sequence = len(self.get_delivery_group_ids())
            if sequence < 4 and sequence > 0:
                delivery_line.write({"delivery_d_run": str(sequence)})
                group.order_line_ids.write({"delivery_d_run": str(sequence)})

            order_line_sequence = self.order_line.filtered(
                lambda l: l.delivery_group_id and l.delivery_group_id != group
            )
            sequence_order = (
                order_line_sequence
                and (max(order_line_sequence.mapped("sequence")) + sequence)
                or sequence
            )

            section = (
                self.env["sale.order.line"]
                .sudo()
                .create(
                    {
                        "order_id": self.id,
                        "name": "Delivery %s" % sequence,
                        "display_type": "line_section",
                        "sequence": sequence_order,
                        "delivery_group_id": group.id,
                    }
                )
            )
            for line in self.order_line.filtered(
                lambda l: l.delivery_group_id == group
            ).sorted(key=lambda l: l.sequence):
                sequence_order += 1
                if line.delivery_group_id == group and line.id != section.id:
                    line.write({"sequence": sequence_order})
                # elif line.delivery_group_id != group and not line.is_delivery and line.id != section.id:
                #     line.write({'sequence': sequence_order + len(self.order_line.filtered(lambda l: l.delivery_group_id == group))})

            # delivery_line.write({'sequence': sequence_order + 1})
        # Enable multi-delivery mode

    def _group_order_lines_by_delivery(self):
        """Group order lines by delivery requirements"""
        self.ensure_one()

        groups = {}
        product_lines = self.order_line.filtered(lambda l: not l.is_delivery)

        for line in product_lines:
            if line.is_mto:
                group_key = "mto"
            else:
                group_key = "stock"

            if group_key not in groups:
                groups[group_key] = self.env["sale.order.line"]
            groups[group_key] |= line

        # Only return groups that have lines
        return {k: v for k, v in groups.items() if v}

    def action_open_multi_delivery_wizard(self):
        """Open multi-delivery carrier selection wizard"""
        self.ensure_one()

        if not self.has_mto_products and not self.has_stock_products:
            raise UserError(
                self.env._(
                    "This order is not eligible for multiple delivery charges. "
                    "The order must contain both MTO and stock products, or be in draft state."
                )
            )

        # Create wizard context
        context = dict(self.env.context)
        context.update(
            {
                "default_order_id": self.id,
                "active_id": self.id,
                "active_model": "sale.order",
            }
        )

        return {
            "name": self.env._("Select Multiple Delivery Methods"),
            "type": "ir.actions.act_window",
            "res_model": "choose.multi.delivery.carrier",
            "view_mode": "form",
            "target": "new",
            "context": context,
        }

    def action_view_delivery_groups(self):
        """View delivery groups for this order"""
        self.ensure_one()

        action = self.env.ref("modula_delivery_multi.action_delivery_group").read()[0]
        action["domain"] = [("order_id", "=", self.id)]
        action["context"] = {
            "default_order_id": self.id,
            "search_default_order_id": self.id,
        }

        if len(self.get_delivery_group_ids()) == 1:
            action["views"] = [(False, "form")]
            action["res_ids"] = self.get_delivery_group_ids().ids

        return action

    def action_disable_multi_delivery(self):
        """Disable multi-delivery and revert to single delivery"""
        self.ensure_one()

        if self.get_delivery_group_ids():
            # Remove all delivery groups and their associated lines
            # self.delivery_group_ids.unlink()
            delivery_lines = self.order_line.filtered(
                lambda l: l.is_delivery
                and l.delivery_group_id in self.get_delivery_group_ids()
            )
            if delivery_lines:
                delivery_lines.unlink()
        self.order_line.write({"delivery_group_id": False, "select": False})

        # Disable multi-delivery mode
        # self.multi_delivery_enabled = False

        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": self.env._("Multi-delivery Disabled"),
                "message": self.env._(
                    "Multiple delivery charges have been disabled. You can now use standard delivery methods."
                ),
                "type": "success",
            },
        }

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to handle multi-delivery initialization"""
        orders = super().create(vals_list)

        # Process each created order
        for order in orders:
            # Auto-enable multi-delivery if order has mixed routing
            if order.has_mto_products and order.has_stock_products:
                # Don't auto-enable, let user decide
                pass

        return orders

    def write(self, vals):
        """Override write to handle multi-delivery state changes"""
        result = super().write(vals)

        # If order lines changed, recompute delivery groups if needed
        if "order_line" in vals and self.multi_delivery_enabled:
            for order in self:
                # Check if delivery groups are still valid
                for group in order.get_delivery_group_ids():
                    # Ensure group lines still belong to the order
                    invalid_lines = group.order_line_ids.filtered(
                        lambda l: l.order_id != order
                    )
                    if invalid_lines:
                        group.order_line_ids = [(3, line.id) for line in invalid_lines]

        return result
