# -*- coding: utf-8 -*-

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError


class ChooseMultiDeliveryCarrier(models.TransientModel):
    _name = "choose.multi.delivery.carrier"
    _description = "Multi-Delivery Carrier Selection Wizard"

    order_id = fields.Many2one(
        "sale.order",
        string="Sale Order",
        required=True,
        help="Sale order for which to configure multiple deliveries",
    )
    delivery_group_ids = fields.One2many(
        "delivery.group.wizard",
        "wizard_id",
        string="Delivery Groups",
        help="Delivery groups with carrier selection",
    )
    order_line_ids = fields.Many2many(
        "sale.order.line",
        "carrier_group_sale_order_line_rel",
        "carrier_group_id",
        "sale_order_line_id",
        string="Order Lines",
        help="Order lines included in this delivery group",
    )
    # Summary fields
    total_delivery_cost = fields.Float(
        string="Total Delivery Cost",
        # compute='_compute_totals',
        help="Total cost of all delivery groups",
    )
    group_count = fields.Integer(
        string="Number of Groups",
        # compute='_compute_totals',
        help="Number of delivery groups",
    )
    group_type = fields.Selection(
        [("mto", "Make to Order"), ("stock", "In Stock"), ("custom", "Custom")],
        string="Group Type",
    )
    destination_location_id = fields.Many2one(
        "stock.location",
        string="Destination Location",
        domain="[('usage', '=', 'internal'), ('is_display_stock', '=', False)]",
    )
    location_id = fields.Many2one(
        "stock.location",
        string="Source Location",
    )
    carrier_id = fields.Many2one(
        "delivery.carrier",
        string="Delivery Method",
        help="Carrier for this delivery group",
    )
    available_carrier_ids = fields.Many2many(
        "delivery.carrier",
        compute="_compute_available_carriers",
        help="Available carriers for this group",
    )

    delivery_price = fields.Float(
        string="Delivery Cost",
        help="Calculated delivery cost for this group",
        compute="_compute_delivery_price",
        store=True,
        readonly=False,
    )
    display_stock = fields.Boolean(default=False, string="Display Stock")
    message = fields.Text(string="Message")

    def set_delivery_method_order_line(self):
        for line in self.order_line_ids.filtered(lambda l: l.select):
            if not line.line_stock_id and not line.line_display_stock_id:
                continue
            if self.carrier_id and self.carrier_id.name.lower() == "pickup":
                line.delivery_method = "pickup"
            else:
                line.delivery_method = "delivery"
            line.stock_location_quant_manager_id.destination_location_id = (
                self.destination_location_id
            )
            line.stock_location_quant_manager_id.quantity = line.product_uom_qty
            line.stock_location_quant_manager_id.delivery_method = line.delivery_method
            if self.display_stock:
                line.stock_location_quant_manager_id.location_id = (
                    line.line_display_stock_id.location_id
                )
            else:
                line.stock_location_quant_manager_id.location_id = (
                    line.line_stock_id.location_id
                )

    @api.depends("order_id", "order_line_ids", "group_type")
    def _compute_available_carriers(self):
        """Compute available carriers for this group"""
        for group in self:
            if group.order_id and group.order_line_ids:
                carriers = self.env["delivery.carrier"].get_carriers_for_delivery_group(
                    group.wizard_id.order_id, group.group_type, group.order_line_ids
                )
                group.available_carrier_ids = carriers
            else:
                group.available_carrier_ids = self.env["delivery.carrier"]

    # @api.depends('delivery_group_ids.delivery_price')
    # def _compute_totals(self):
    #     """Compute total delivery cost and group count"""
    #     for wizard in self:
    #         wizard.total_delivery_cost = sum(wizard.delivery_group_ids.mapped('delivery_price'))
    #         wizard.group_count = len(wizard.delivery_group_ids)

    @api.model
    def default_get(self, fields_list):
        """Pre-populate delivery groups based on order lines"""
        res = super().default_get(fields_list)

        if "default_order_id" in self.env.context:
            order = self.env["sale.order"].browse(self.env.context["default_order_id"])
            # groups = order._group_order_lines_by_delivery()

            # group_lines = []
            # sequence = 10

            # for group_type, lines in groups.items():
            #     if lines:  # Only create groups for non-empty line sets
            #         group_lines.append((0, 0, {
            #             'group_type': group_type,
            #             'order_line_ids': [(6, 0, lines.ids)],
            #             'sequence': sequence,
            #         }))
            #         sequence += 10
            order_line = order.order_line.filtered(
                lambda l: not l.delivery_group_id and l.delivery_method
            )
            delivery_groups = order.get_delivery_group_ids()
            delivery_price = sum(delivery_groups.mapped("delivery_price"))

            order_line.write(
                {
                    "select": False,
                    "line_stock_id": False,
                    "line_display_stock_id": False,
                }
            )
            res["order_id"] = order.id
            res["order_line_ids"] = [(6, 0, order_line.ids)]
            res["total_delivery_cost"] = delivery_price
            res["group_count"] = len(delivery_groups)
            res["message"] = _(
                "Summary: Successfully configured %d delivery groups with total cost of %s"
            ) % (len(delivery_groups), order.currency_id.symbol + str(delivery_price))

        return res

    def button_add_delivery_group(self):
        """Add a new delivery group"""
        self.ensure_one()
        self.set_delivery_method_order_line()

        """Apply multiple delivery charges"""
        self.ensure_one()

        # Create delivery groups
        delivery_groups = []
        order_line = self.order_line_ids.filtered(lambda l: l.select)
        if self.carrier_id and order_line:
            group = self.env["delivery.group"].create(
                {
                    "order_id": self.order_id.id,
                    "carrier_id": self.carrier_id.id,
                    "delivery_price": self.delivery_price,
                    "order_line_ids": [(6, 0, order_line.ids)],
                }
            )
            delivery_groups.append(group)

        if not delivery_groups:
            raise UserError(self.env._("No valid delivery groups could be created."))

        # Apply delivery groups to order
        self.order_id.set_multi_delivery_lines(delivery_groups)
        # Show success message
        delivery_groups = self.order_id.get_delivery_group_ids()

        order_line = self.order_id.order_line.filtered(
            lambda l: not l.delivery_group_id and l.delivery_method
        )
        delivery_price = sum(delivery_groups.mapped("delivery_price"))
        if not order_line:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": self.env._("Multiple Delivery Charges Applied"),
                    "message": self.env._(
                        "Successfully configured %d delivery groups with total cost of %s"
                    )
                    % (
                        len(delivery_groups),
                        self.order_id.currency_id.symbol + str(delivery_price),
                    ),
                    "type": "success",
                    "next": {"type": "ir.actions.act_window_close"},
                },
            }

        self.order_line_ids = [(6, 0, order_line.ids)]
        self.carrier_id = False
        self.delivery_price = 0.0
        self.destination_location_id = False

        # return {
        #     'type': 'ir.actions.client',
        #     'tag': 'display_notification',
        #     'params': {
        #         'title': self.env._('Multiple Delivery Charges Applied'),
        #         'message': self.env._(
        #             'Successfully configured %d delivery groups with total cost of %s'
        #         ) % (len(delivery_groups), self.order_id.currency_id.symbol + str(self.delivery_price)),
        #         'type': 'success',
        #     }
        # }
        context = dict(self.env.context)
        context.update(
            {
                "default_order_id": self.order_id.id,
                "active_id": self.order_id.id,
                "active_model": "sale.order",
            }
        )

        return {
            "name": self.env._("Select Multiple Delivery Methods"),
            "type": "ir.actions.act_window",
            "res_model": "choose.multi.delivery.carrier",
            "view_mode": "form",
            "target": "new",
            "context": context,
        }

    # def button_confirm(self):
    #     """Apply multiple delivery charges"""
    #     self.ensure_one()
    #     self.set_delivery_method_order_line()

    #     # Create delivery groups
    #     delivery_groups = []
    #     if self.carrier_id and self.order_line_ids.filtered(lambda l: l.select):
    #         group = self.env['delivery.group'].create({
    #             'order_id': self.order_id.id,
    #             'carrier_id': self.carrier_id.id,
    #             'delivery_price': self.delivery_price,
    #             'order_line_ids': [(6, 0, self.order_line_ids.ids)],
    #         })
    #         delivery_groups.append(group)

    #     if not delivery_groups:
    #         raise UserError(self.env._('No valid delivery groups could be created.'))

    #     # Apply delivery groups to order
    #     self.order_id.set_multi_delivery_lines(delivery_groups)
    # Show success message

    def button_calculate_rates(self):
        """Calculate rates for all groups"""
        self.ensure_one()
        order_line = self.order_line_ids.filtered(lambda l: l.select)

        if self.carrier_id and order_line:
            order = self.env["sale.order"].browse(self.env.context["active_id"])
            result = self.carrier_id.rate_shipment_for_lines(order, order_line)
            if result.get("success"):
                self.delivery_price = result["price"]
            else:
                self.delivery_price = 0.0

    @api.depends("carrier_id", "order_line_ids")
    def _compute_delivery_price(self):
        for wizard in self:
            wizard.button_calculate_rates()
