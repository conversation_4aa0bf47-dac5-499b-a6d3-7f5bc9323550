# -*- coding: utf-8 -*-

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError


class DeliveryGroupWizard(models.TransientModel):
    _name = "delivery.group.wizard"
    _description = "Delivery Group Wizard Line"
    _order = "sequence, id"

    wizard_id = fields.Many2one(
        "choose.multi.delivery.carrier",
        string="Wizard",
        required=True,
        ondelete="cascade",
    )
    group_type = fields.Selection(
        [("mto", "Make to Order"), ("stock", "In Stock"), ("custom", "Custom")],
        string="Group Type",
    )

    carrier_id = fields.Many2one(
        "delivery.carrier",
        string="Delivery Method",
        help="Carrier for this delivery group",
    )
    available_carrier_ids = fields.Many2many(
        "delivery.carrier",
        compute="_compute_available_carriers",
        help="Available carriers for this group",
    )

    delivery_price = fields.Float(
        string="Delivery Cost", help="Calculated delivery cost for this group"
    )
    order_line_ids = fields.Many2many(
        "sale.order.line",
        string="Order Lines",
        help="Order lines included in this delivery group",
    )
    order_id = fields.Many2one(
        "sale.order", string="Order", help="Order for this delivery group"
    )
    # Computed summary fields
    total_weight = fields.Float(
        string="Total Weight",
        compute="_compute_totals",
        help="Total weight of products in this group",
    )
    total_value = fields.Float(
        string="Total Value",
        compute="_compute_totals",
        help="Total value of products in this group",
    )
    line_count = fields.Integer(
        string="Line Count",
        compute="_compute_totals",
        help="Number of order lines in this group",
    )

    # UI fields
    sequence = fields.Integer(string="Sequence", default=10)
    display_name = fields.Char(
        string="Display Name",
        compute="_compute_display_name",
        help="Display name for the group",
    )
    destination_location_id = fields.Many2one(
        "stock.location",
        string="Destination Location",
    )
    location_id = fields.Many2one(
        "stock.location",
        string="Source Location",
    )
    display_price = fields.Float(
        string="Display Price",
    )

    def update_price(self):
        """Update the delivery price for this group"""
        self.ensure_one()
        return
        # self.calculate_delivery_rate()

    def default_get(self, fields):
        res = super(DeliveryGroupWizard, self).default_get(fields)
        res["order_id"] = self.env.context.get("active_id")
        order = self.env["sale.order"].browse(res["order_id"])
        res["order_line_ids"] = [(6, 0, order.order_line.ids)]
        return res

    @api.depends("group_type", "carrier_id", "line_count")
    def _compute_display_name(self):
        """Compute display name for the group"""
        for group in self:
            group_type_label = dict(group._fields["group_type"].selection).get(
                group.group_type, ""
            )
            carrier_name = (
                group.carrier_id.name if group.carrier_id else group.env._("No Carrier")
            )
            group.display_name = (
                f"{group_type_label} ({group.line_count} lines) - {carrier_name}"
            )

    @api.depends(
        "order_line_ids.product_qty",
        "order_line_ids.product_id.weight",
        "order_line_ids.price_subtotal",
    )
    def _compute_totals(self):
        """Compute totals for the delivery group"""
        for group in self:
            total_weight = 0.0
            total_value = 0.0

            for line in group.order_line_ids:
                if line.product_id:
                    total_weight += line.product_qty * line.product_id.weight
                    total_value += line.price_subtotal

            group.total_weight = total_weight
            group.total_value = total_value
            group.line_count = len(group.order_line_ids)

    @api.depends("wizard_id.order_id", "order_line_ids", "group_type")
    def _compute_available_carriers(self):
        """Compute available carriers for this group"""
        for group in self:
            if group.wizard_id.order_id and group.order_line_ids:
                carriers = self.env["delivery.carrier"].get_carriers_for_delivery_group(
                    group.wizard_id.order_id, group.group_type, group.order_line_ids
                )
                group.available_carrier_ids = carriers
            else:
                group.available_carrier_ids = self.env["delivery.carrier"]

    @api.onchange("carrier_id")
    def _onchange_carrier_id(self):
        """Calculate delivery price when carrier changes"""
        if self.carrier_id and self.wizard_id.order_id and self.order_line_ids:
            self._calculate_delivery_rate()

    def _calculate_delivery_rate(self):
        """Calculate delivery rate for this group"""
        self.ensure_one()

        if (
            not self.carrier_id
            or not self.order_line_ids
            or not self.wizard_id.order_id
        ):
            self.delivery_price = 0.0
            return

        try:
            result = self.carrier_id.rate_shipment_for_lines(
                self.wizard_id.order_id, self.order_line_ids
            )
            return result

        except Exception as e:
            self.delivery_price = 0.0
            # Could log error or show warning

    @api.constrains("order_line_ids")
    def _check_order_lines_consistency(self):
        """Ensure all order lines belong to the wizard's order"""
        for group in self:
            if group.order_line_ids and group.wizard_id.order_id:
                invalid_lines = group.order_line_ids.filtered(
                    lambda l: l.order_id != group.wizard_id.order_id
                )
                if invalid_lines:
                    raise ValidationError(
                        self.env._(
                            "All order lines in a delivery group must belong to the same sale order."
                        )
                    )

    def create(self, vals_list):
        res = super(DeliveryGroupWizard, self).create(vals_list)
        res.order_id = self.env.context.get("active_id")
        return res
