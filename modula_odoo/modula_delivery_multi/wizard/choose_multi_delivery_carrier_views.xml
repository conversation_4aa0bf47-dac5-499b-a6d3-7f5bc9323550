<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Multi-Delivery Carrier Wizard Form View -->
    <record id="view_choose_multi_delivery_carrier_form" model="ir.ui.view">
        <field name="name">choose.multi.delivery.carrier.form</field>
        <field name="model">choose.multi.delivery.carrier</field>
        <field name="arch" type="xml">
            <form string="Select Multiple Delivery Methods">
                <sheet>
                    <div class="oe_title">
                        <p>Select delivery methods for each product group in your order.</p>
                    </div>
                    <group>
                        <group>
                            <field name="carrier_id" required="1"/>
                            <field name="destination_location_id"/>
                        </group>
                        <group>
                            <field name='delivery_price'/>
                                <!-- <button name="button_calculate_rates" type="object">
                                    <i class="oi oi-arrow-right me-1"/>Get rate
                                </button> -->
                        </group>
                    </group>
                    <div class="d-flex gap-4">
                        <div class="d-flex gap-2">
                            <field name="display_stock" widget="boolean_toggle"/>
                            <label for="display_stock">
                                <span>Display Stock</span>
                            </label>
                        </div>
                    </div>
                    <separator string="Delivery Groups"/>
                    <field name="order_line_ids">
                        <list editable="bottom" create="0" delete="0" edit="1">
                            <field name="select"/>
                            <field name="line_stock_id" column_invisible="parent.display_stock" string="Source Location"/>
                            <field name="line_display_stock_id" column_invisible="not parent.display_stock" string="Source Location"/>
                            <field name="product_id" readonly="1"/>
                            <field name="product_uom_qty"/>
                            <field name="price_unit" readonly="1"/>
                        </list>
                    </field>

                    <!-- Summary section -->
                    <group invisible="group_count == 0" class="mt-3">
                        <div class="alert alert-info d-flex" role="alert">
                            <!-- <strong>Summary: </strong> -->
                            <field name="message" readonly="1"/>
                        </div>
                    </group>
                </sheet>

                <footer>
                    <button name="button_add_delivery_group" string="Add Delivery Group" type="object" class="btn-primary"/>
                    <!-- <button name="button_confirm"
                            string="Apply Delivery Charges"
                            type="object"
                            class="btn-primary"/> -->
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Delivery Group Wizard List View (for embedded use) -->
    <record id="view_delivery_group_wizard_list" model="ir.ui.view">
        <field name="name">delivery.group.wizard.list</field>
        <field name="model">delivery.group.wizard</field>
        <field name="arch" type="xml">
            <list string="Delivery Groups" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="group_type" readonly="1"/>
                <field name="display_name" readonly="1"/>
                <field name="carrier_id" required="1"/>
                <field name="delivery_price" readonly="1"/>
                <field name="total_weight" readonly="1"/>
                <field name="line_count" readonly="1"/>
            </list>
        </field>
    </record>

    <!-- Action for Multi-Delivery Wizard -->
    <record id="action_choose_multi_delivery_carrier" model="ir.actions.act_window">
        <field name="name">Select Multiple Delivery Methods</field>
        <field name="res_model">choose.multi.delivery.carrier</field>
        <field name="path">multi-delivery-wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_choose_multi_delivery_carrier_form"/>
    </record>
</odoo>
