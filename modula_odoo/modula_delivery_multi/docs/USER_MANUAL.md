# Multiple Delivery Charges - User Manual

## Overview

The Multiple Delivery Charges module allows you to apply different delivery charges to different types of products within a single sale order. This is particularly useful when you have both Make-to-Order (MTO) products and In-Stock products that require different shipping methods.

## Key Benefits

### 🚚 **Multiple Delivery Options**
- Apply different carriers to different product groups
- Separate delivery charges for MTO vs Stock products
- Automatic rate calculation for each group

### 💰 **Accurate Pricing**
- Automatic rate calculation using existing postcode pricing
- No more manual pricing guesswork
- Proper integration with carrier rate systems

### 🎯 **Easy to Use**
- Simple wizard interface
- Automatic product grouping
- Visual indicators and status updates

## Getting Started

### Prerequisites

Before using multiple delivery charges, ensure:
1. Your sale order contains both MTO and Stock products, OR
2. Your order is in Draft or Sent state for manual configuration
3. Delivery carriers are properly configured
4. Postcode pricing rules are set up (if using postcode-based pricing)

### Basic Workflow

1. **Create Sale Order** with mixed product types
2. **Open Multi-Delivery Wizard** using the "Multiple Deliveries" button
3. **Configure Carriers** for each product group
4. **Calculate Rates** automatically
5. **Apply Delivery Charges** to your order

## Step-by-Step Instructions

### Step 1: Create Sale Order

1. Navigate to **Sales > Orders > Quotations**
2. Click **Create** to create a new quotation
3. Fill in customer information
4. Add order lines with both MTO and Stock products

**Example Order:**
- 2x Custom Cabinet (MTO Product)
- 5x Standard Shelf (Stock Product)

### Step 2: Identify Multi-Delivery Eligibility

Look for these indicators on your sale order:

#### ✅ **Eligible for Multi-Delivery**
- **Blue Info Box**: "Multiple Delivery Available - This order contains both MTO and stock products"
- **"Multiple Deliveries" Button**: Visible in the header

#### ❌ **Not Eligible**
- Order contains only one type of product
- Order is already confirmed
- No mixed routing detected

### Step 3: Open Multi-Delivery Wizard

1. Click the **"Multiple Deliveries"** button in the sale order header
2. The Multi-Delivery Wizard will open
3. Product groups will be automatically populated based on routing

### Step 4: Configure Delivery Groups

The wizard will show delivery groups like:

#### **Make to Order Group**
- **Products**: Custom Cabinet (2 units)
- **Total Weight**: 50.0 kg
- **Total Value**: $2,000.00
- **Carrier**: [Select from dropdown]

#### **In Stock Group**
- **Products**: Standard Shelf (5 units)
- **Total Weight**: 25.0 kg
- **Total Value**: $500.00
- **Carrier**: [Select from dropdown]

### Step 5: Select Carriers

For each group:
1. **Click the Carrier dropdown**
2. **Select appropriate carrier** (e.g., Express for MTO, Standard for Stock)
3. **Rate will calculate automatically** when carrier is selected

**Example Configuration:**
- **MTO Group**: Express Carrier → $35.00
- **Stock Group**: Standard Carrier → $15.00
- **Total Delivery Cost**: $50.00

### Step 6: Calculate and Apply

1. **Review the summary** showing total delivery cost
2. **Click "Calculate All Rates"** to refresh all pricing (optional)
3. **Click "Apply Delivery Charges"** to confirm

### Step 7: Verify Results

After applying delivery charges:
1. **Multi-delivery indicator** appears on sale order
2. **Delivery Groups tab** shows configured groups
3. **Multiple delivery lines** appear in order lines
4. **Smart button** shows delivery group count

## Advanced Features

### Manual Rate Calculation

If rates don't calculate automatically:
1. **In the wizard**: Click the refresh icon next to each group
2. **After applying**: Use "Recalculate" button in Delivery Groups tab

### Viewing Delivery Groups

Access delivery group details:
1. **Smart Button**: Click the "Delivery Groups" button (shows count)
2. **Delivery Groups Tab**: View details within the sale order
3. **Dedicated View**: Navigate to Inventory > Delivery Groups

### Disabling Multi-Delivery

To revert to single delivery:
1. **Click "Disable Multi-Delivery"** button in sale order header
2. **Confirm the action** (this will remove all delivery groups)
3. **Use standard "Add Shipping"** for single delivery

### Modifying Delivery Groups

After applying delivery charges:
1. **Navigate to Delivery Groups tab**
2. **Click on a group** to edit details
3. **Use "Recalculate" button** to update rates
4. **Save changes** to update delivery lines

## User Interface Guide

### Sale Order Form Enhancements

#### **Header Buttons**
- **"Multiple Deliveries"**: Opens configuration wizard
- **"Disable Multi-Delivery"**: Reverts to single delivery

#### **Status Indicators**
- **Blue Info Box**: Shows multi-delivery availability
- **Green Success Box**: Confirms multi-delivery is enabled

#### **Smart Buttons**
- **"Delivery Groups"**: Shows count and provides quick access

#### **Tabs**
- **"Delivery Groups"**: Manage existing delivery groups

### Multi-Delivery Wizard Interface

#### **Header Section**
- **Order Information**: Shows order reference and customer
- **Summary**: Displays group count and total delivery cost

#### **Delivery Groups Table**
- **Group Type**: MTO, Stock, or Custom
- **Line Count**: Number of products in group
- **Weight/Value**: Totals for the group
- **Carrier Selection**: Dropdown with available carriers
- **Delivery Price**: Automatically calculated rate

#### **Footer Buttons**
- **"Calculate All Rates"**: Refresh all pricing
- **"Apply Delivery Charges"**: Confirm configuration
- **"Cancel"**: Exit without changes

### Delivery Groups Management

#### **Tree View**
- **Sequence Handle**: Drag to reorder groups
- **Group Information**: Type, carrier, pricing
- **Action Buttons**: Calculate, confirm, cancel

#### **Form View**
- **Group Configuration**: Type, carrier, pricing details
- **Order Lines**: Products included in the group
- **State Management**: Draft, confirmed, cancelled

## Troubleshooting

### Common Issues

#### **"Multiple Deliveries" Button Not Visible**
**Cause**: Order not eligible for multi-delivery
**Solution**: 
- Ensure order has both MTO and Stock products
- Check order is in Draft or Sent state
- Verify products have proper routing configuration

#### **No Carriers Available in Dropdown**
**Cause**: No carriers configured or available
**Solution**:
- Check carrier configuration in Inventory > Configuration > Delivery
- Verify carriers are active and properly configured
- Ensure postcode pricing rules exist (if using postcode carriers)

#### **Rate Calculation Shows $0.00**
**Cause**: Carrier configuration or pricing rules missing
**Solution**:
- Verify carrier has proper pricing configuration
- Check postcode pricing rules match shipping address
- Ensure shipping address is complete
- Review carrier geographic restrictions

#### **Wizard Shows No Groups**
**Cause**: No product lines or all lines are delivery lines
**Solution**:
- Add product lines to the order
- Ensure products have proper routing (MTO vs Stock)
- Check that order lines are not all delivery lines

#### **Delivery Groups Not Showing After Apply**
**Cause**: Wizard confirmation failed or browser cache
**Solution**:
- Refresh the sale order form
- Check for error messages in wizard
- Verify carriers were selected for all groups

### Error Messages

#### **"Order not eligible for multiple delivery charges"**
- Order must contain mixed product types or be in draft state
- Check product routing configuration

#### **"Please select a delivery method for all groups"**
- All delivery groups must have carriers selected
- Review carrier availability and configuration

#### **"No delivery price found for postcode"**
- Postcode pricing rule missing for shipping address
- Check postcode pricelist configuration
- Verify fallback pricing is configured

### Getting Help

1. **Check Configuration**: Verify carriers and pricing rules
2. **Review Documentation**: Consult this manual and technical docs
3. **Enable Debug Mode**: Access additional technical information
4. **Contact Support**: Reach out to your system administrator

## Best Practices

### Order Management
1. **Configure carriers** before creating orders
2. **Set up postcode pricing** for accurate rates
3. **Train users** on the new workflow
4. **Monitor delivery costs** for accuracy

### Carrier Configuration
1. **Use descriptive names** for carriers (e.g., "Express MTO", "Standard Stock")
2. **Set up fallback pricing** for postcode carriers
3. **Configure geographic restrictions** appropriately
4. **Test rate calculations** with sample orders

### User Training
1. **Start with simple orders** to learn the workflow
2. **Practice with test data** before using in production
3. **Understand eligibility criteria** for multi-delivery
4. **Know when to use single vs multiple delivery**

---

**Need More Help?** Contact your system administrator or refer to the technical documentation for advanced configuration and troubleshooting.
