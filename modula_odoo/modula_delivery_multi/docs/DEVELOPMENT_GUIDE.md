# Multiple Delivery Charges - Development Guide

## Overview

This guide provides comprehensive development instructions for the Multiple Delivery Charges module, including architecture patterns, coding standards, and extension guidelines.

## Architecture Overview

### Core Problem Solved

**Business Problem**: Odoo's standard "Add Shipping" only supports 1 delivery charge per order, and manual workarounds bypass rate calculation.

**Technical Solution**: Automated multi-delivery system with group-based rate calculation while preserving postcode pricing integration.

### Key Components

#### 1. Delivery Group Model (`delivery.group`)
```python
class DeliveryGroup(models.Model):
    _name = 'delivery.group'
    _description = 'Delivery Group for Multiple Charges'
    
    # Core fields for grouping and pricing
    order_id = fields.Many2one('sale.order', required=True)
    carrier_id = fields.Many2one('delivery.carrier', required=True)
    group_type = fields.Selection([('mto', 'Make to Order'), ('stock', 'In Stock')])
    delivery_price = fields.Float()
    order_line_ids = fields.Many2many('sale.order.line')
```

#### 2. Enhanced Sale Order (`sale.order`)
```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    # Multi-delivery management
    delivery_group_ids = fields.One2many('delivery.group', 'order_id')
    multi_delivery_enabled = fields.Boolean()
    
    def _group_order_lines_by_delivery(self):
        """Template method for product grouping - can be extended"""
        # Automatic MTO vs Stock detection
```

#### 3. Enhanced Delivery Carrier (`delivery.carrier`)
```python
class DeliveryCarrier(models.Model):
    _inherit = 'delivery.carrier'
    
    def rate_shipment_for_lines(self, order, order_lines):
        """Key innovation: Direct rate calculation for product subsets"""
        # Direct calculation without temporary orders
        # Tax-inclusive pricing with modula_delivery integration
        # Delivery-type specific calculation methods
```

## Development Patterns

### 1. Template Method Pattern ⭐ **RECOMMENDED**

The module uses template methods for extensibility:

```python
# Base implementation in delivery.group
def calculate_delivery_rate(self):
    """Template method that can be extended"""
    result = self.carrier_id.rate_shipment_for_lines(self.order_id, self.order_line_ids)
    if result.get('success'):
        self.delivery_price = result['price']
    return result

# Extension in dependent modules
def calculate_delivery_rate(self):
    """Override with additional logic"""
    result = super().calculate_delivery_rate()
    # Add custom business logic
    return result
```

### 2. Tax-Inclusive Rate Calculation ⭐ **CRITICAL**

**Core Innovation**: Direct rate calculation with tax inclusion following modula_delivery patterns:

```python
def _apply_taxes_to_price(self, price, order):
    """Apply taxes following modula_delivery pattern"""
    if not self.product_id or not self.product_id.taxes_id:
        return price

    price_inc_tax = self.product_id.taxes_id.compute_all(
        price,
        order.company_id.currency_id,
        1,
        self.product_id,
        order.partner_id,
    )["total_included"]

    return price_inc_tax

def _calculate_fixed_rate_for_lines(self, order, order_lines):
    """Calculate fixed rate with tax inclusion"""
    price_inc_tax = self._apply_taxes_to_price(self.fixed_price, order)
    return {'success': True, 'price': price_inc_tax}
```

### 3. Direct Rate Calculation (No Temporary Orders) ⭐ **CRITICAL**

**Key Principle**: Never create temporary orders for rate calculations. Use existing order context directly.

```python
def rate_shipment_for_lines(self, order, order_lines):
    """Calculate delivery rate for specific order lines only"""
    # ✅ CORRECT: Use existing order directly
    if self.delivery_type == 'postcode':
        result = self.postcode_rate_shipment_for_lines(order, order_lines)
    elif self.delivery_type == 'fixed':
        result = self._calculate_fixed_rate_for_lines(order, order_lines)
    elif self.delivery_type == 'base_on_rule':
        result = self._calculate_rule_based_rate_for_lines(order, order_lines)

    # ❌ WRONG: Never create temporary orders
    # temp_order = order.copy(...)
    # result = self.rate_shipment(temp_order)
    # temp_order.unlink()

    return result
```

### 4. Postcode Pricing Integration

Seamless integration with `modula_delivery` postcode pricing with tax calculation:

```python
def postcode_rate_shipment_for_lines(self, order, order_lines):
    """Postcode-based rate calculation for specific lines"""
    if self.delivery_type != 'postcode':
        return self.rate_shipment_for_lines(order, order_lines)

    # Use existing postcode logic from modula_delivery
    price = self.env['delivery.postcode.pricelist'].find_delivery_price(
        self, postcode, country, state, city
    )

    if price is not False:
        price_inc_tax = self._apply_taxes_to_price(price, order)
        return {'success': True, 'price': price_inc_tax}

    # Apply fallback pricing with taxes
    if self.postcode_fixed_price > 0:
        price_inc_tax = self._apply_taxes_to_price(self.postcode_fixed_price, order)
        return {
            'success': True,
            'price': price_inc_tax,
            'warning_message': self.env._('Using postcode fixed price fallback')
        }
```

## Coding Standards

### Python Development (Odoo 18)

Follow the guidelines in `docs/Odoo18/odoo_python_development_guidelines.md`:

#### ✅ Correct Patterns
```python
# Use self.env.user.has_group() instead of user_has_groups
if self.env.user.has_group('stock.group_stock_manager'):
    # User has stock manager rights

# Use check_access() instead of separate methods
self.check_access('read')

# Use aggregator instead of group_operator
total_amount = fields.Float(aggregator='sum')

# Use self.env._ for performance optimization
message = self.env._('Optimized translation')
```

#### ❌ Deprecated Patterns
```python
# WRONG: These are deprecated in Odoo 18
if self.user_has_groups('stock.group_stock_manager'):  # ❌
self.check_access_rights('read')  # ❌
total_amount = fields.Float(group_operator='sum')  # ❌
```

### XML Development (Odoo 18)

Follow the guidelines in `docs/Odoo18/odoo_xml_view_guide_line.md`:

#### ✅ Correct Patterns
```xml
<!-- Use path field for better URLs -->
<record id="action_delivery_group" model="ir.actions.act_window">
    <field name="name">Delivery Groups</field>
    <field name="res_model">delivery.group</field>
    <field name="path">delivery-groups</field>
    <field name="view_mode">tree,form</field>
</record>

<!-- Use simplified chatter (if needed) -->
<form>
    <sheet>
        <!-- Form content -->
    </sheet>
    <chatter/>
</form>
```

## Extension Guidelines

### Adding New Grouping Logic

To add custom grouping logic, extend the template method:

```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    def _group_order_lines_by_delivery(self):
        """Override to add custom grouping logic"""
        # Get base groups
        groups = super()._group_order_lines_by_delivery()
        
        # Add custom grouping logic
        if self.custom_delivery_rule:
            # Custom grouping implementation
            pass
        
        return groups
```

### Adding New Rate Calculation Logic

Extend the carrier rate calculation:

```python
class DeliveryCarrier(models.Model):
    _inherit = 'delivery.carrier'
    
    def rate_shipment_for_lines(self, order, order_lines):
        """Override to add custom rate logic"""
        # Get base rate
        result = super().rate_shipment_for_lines(order, order_lines)
        
        # Add custom rate modifications
        if self.custom_rate_rule:
            result['price'] *= self.custom_multiplier
        
        return result
```

### Adding New Wizard Steps

Extend the multi-delivery wizard:

```python
class ChooseMultiDeliveryCarrier(models.TransientModel):
    _inherit = 'choose.multi.delivery.carrier'
    
    custom_field = fields.Char('Custom Configuration')
    
    def button_confirm(self):
        """Override to add custom confirmation logic"""
        # Add custom validation
        if self.custom_field:
            # Custom logic
            pass
        
        # Call parent confirmation
        return super().button_confirm()
```

## Testing Guidelines

### Unit Testing

Create comprehensive unit tests for all functionality:

```python
class TestDeliveryGroup(TransactionCase):
    
    def test_rate_calculation_accuracy(self):
        """Test that rate calculation is accurate for groups"""
        group = self.env['delivery.group'].create({
            'order_id': self.sale_order.id,
            'carrier_id': self.carrier.id,
            'group_type': 'mto',
            'order_line_ids': [(6, 0, [self.mto_line.id])],
        })
        
        result = group.calculate_delivery_rate()
        self.assertTrue(result.get('success'))
        self.assertGreater(result.get('price'), 0)
```

### Integration Testing

Test end-to-end workflows:

```python
def test_complete_multi_delivery_workflow(self):
    """Test complete workflow from order to delivery"""
    # Create order with mixed products
    # Open multi-delivery wizard
    # Configure carriers
    # Apply delivery charges
    # Verify results
```

### Performance Testing

Test with large datasets:

```python
def test_large_order_performance(self):
    """Test performance with orders containing many lines"""
    # Create order with 100+ lines
    # Measure wizard performance
    # Verify rate calculation speed
```

## Debugging Guidelines

### Common Issues

1. **Rate Calculation Fails**
   - Check carrier configuration
   - Verify postcode pricing rules
   - Ensure order lines have proper routing

2. **Wizard Not Populating Groups**
   - Verify order has mixed MTO/Stock products
   - Check product routing configuration
   - Ensure order is in correct state

3. **Delivery Lines Not Created**
   - Check carrier selection in wizard
   - Verify rate calculation success
   - Ensure proper wizard confirmation

### Debug Mode Features

Enable developer mode for additional debugging:
- Delivery group technical details
- Rate calculation logs
- Order line routing information
- Wizard state inspection

## Performance Optimization

### Database Optimization

- Use proper indexing on delivery group fields
- Optimize queries for large order datasets
- Cache rate calculations when possible

### UI Optimization

- Lazy load delivery group data
- Optimize wizard rendering
- Use computed fields efficiently

### Rate Calculation Optimization

- Cache carrier availability checks
- Optimize temporary order creation
- Batch rate calculations when possible

## Security Considerations

### Access Control

- Proper access rights for delivery groups
- Carrier selection permissions
- Order modification restrictions

### Data Validation

- Input validation for all wizard fields
- Rate calculation result validation
- Order line consistency checks

## Deployment Guidelines

### Prerequisites

1. Ensure dependencies are installed:
   - `delivery` (Core Odoo)
   - `sale_stock` (Core Odoo)
   - `modula_delivery` (For postcode pricing)

2. Database backup before installation

### Installation Steps

1. Copy module to addons directory
2. Update module list
3. Install module
4. Configure carriers and pricing rules
5. Test with sample orders

### Post-Installation

1. Train users on new workflow
2. Monitor performance metrics
3. Validate rate calculation accuracy
4. Set up monitoring and logging

---

**Next Steps**: Review `USER_MANUAL.md` for end-user documentation and `TESTING_GUIDE.md` for comprehensive testing procedures.
