# -*- coding: utf-8 -*-
from odoo.http import request
from odoo.exceptions import ValidationError

from odoo import api, fields, models, _


class SaleOrder(models.Model):
    _inherit = "sale.order"

    need_approve = fields.Boolean(string="Need Approve", default=False)

    @api.model_create_multi
    def create(self, vals_list):
        records = super(SaleOrder, self).create(vals_list)
        for record in records:
            if request.session.get("session_owner", False):
                record.employee_id = request.session.get("session_owner", False)
        return records

    def need_employee_selection(self, **kwargs):
        return False

    def action_approve_sale_order(self):
        return {
            'type': 'ir.actions.act_window_close',
        }

    def action_employee_validation_for_sale_order(self):
        self.ensure_one()
        return True
