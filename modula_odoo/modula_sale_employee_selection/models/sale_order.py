# -*- coding: utf-8 -*-
from odoo.http import request
from odoo.exceptions import ValidationError

from odoo import api, fields, models, _


class SaleOrder(models.Model):
    _inherit = "sale.order"

    need_approve = fields.Boolean(string="Need Approve", default=False)

    @api.model_create_multi
    def create(self, vals_list):
        records = super(SaleOrder, self).create(vals_list)
        for record in records:
            if request.session.get("session_owner", False):
                record.employee_id = request.session.get("session_owner", False)
        return records

    def need_employee_selection(self, **kwargs):
        return False


    def action_approve_sale_order(self):
        """Template inheritance Approve button action - same functionality as original Approve button

        This method provides the same functionality as action_approve_sale_order
        but is designed to work with the template inheritance approach.
        The actual approval logic is handled in the frontend JavaScript.
        """
        # The approval logic is handled in the frontend JavaScript
        # This method exists to satisfy the button's action requirement
        return {
            'type': 'ir.actions.act_window_close',
        }

    def action_apply_discount_with_employee_validation(self, discount_type, discount_percentage=None, discount_amount=None):
        """Apply discount after employee validation

        This method is called from the frontend after employee selection and validation.
        It creates a discount wizard instance and applies the discount.

        Args:
            discount_type (str): Type of discount ('so_discount', 'sol_discount', 'amount')
            discount_percentage (float): Discount percentage (for percentage-based discounts)
            discount_amount (float): Discount amount (for amount-based discounts)

        Returns:
            dict: Action result or success status
        """
        self.ensure_one()

        try:
            # Create discount wizard instance
            wizard_vals = {
                'sale_order_id': self.id,
                'discount_type': discount_type,
            }

            if discount_percentage is not None:
                wizard_vals['discount_percentage'] = discount_percentage
            if discount_amount is not None:
                wizard_vals['discount_amount'] = discount_amount

            wizard = self.env['sale.order.discount'].create(wizard_vals)

            # Apply the discount using the wizard
            result = wizard.action_apply_discount()

            return {
                'success': True,
                'message': 'Discount applied successfully with employee validation',
                'result': result
            }

        except ValidationError as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Discount validation failed: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Error applying discount: {str(e)}'
            }
