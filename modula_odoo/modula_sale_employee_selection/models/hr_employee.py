# -*- coding: utf-8 -*-
from datetime import datetime

from odoo import fields, models
from odoo.http import request

EMPLOYEES_CONNECTED = "employees_connected"
SESSION_OWNER = "session_owner"
MANAGER_APPROVE = "manager_approve"

class HrEmployee(models.Model):
    _inherit = "hr.employee"

    # def fetch(self, field_names):
    #     if self.env.context.get('params', {}).get('action') == 'sales':
    #         self = self.sudo()
    #     return super().fetch(field_names)

    def pin_validation(self, pin=False):
        if not pin:
            pin = False
        if self.sudo().pin == pin:
            if self.env.context.get('action_approve_sale_order') and request:
                request.session[MANAGER_APPROVE] = self.id
            return True
        return False

    def login(self, pin=False, set_in_session=True):
        if self.pin_validation(pin) and set_in_session:
            if self.env.context.get('action_approve_sale_order'):
                request.session[MANAGER_APPROVE] = self.id
            else:
                self._connect_employee()
                request.session[SESSION_OWNER] = self.id
            return True
        return False

    def logout(self, pin=False, unchecked=False):
        # For some reason, if the session owner does not get changed during a run of this logout function, the writes
        # to the connected employees are not persisted in the session. To hack around this we always set the owner
        # to the user that is logging out, and we change it back after (or to False if the admin logs out).
        employees = request.session.get(EMPLOYEES_CONNECTED, [])
        owner = request.session.get(SESSION_OWNER, False)
        if (self.pin_validation(pin) or unchecked) and self.id in employees:
            request.session[SESSION_OWNER] = self.id
            employees.remove(self.id)
            request.session[EMPLOYEES_CONNECTED] = employees
            if owner == self.id:
                owner = False
            request.session[SESSION_OWNER] = owner

            if self.env.context.get('action_approve_sale_order'):
                request.session[MANAGER_APPROVE] = self.id

            return True
        return False

    def remove_session_owner(self):
        self.ensure_one()
        if self.id == request.session.get(SESSION_OWNER):
            request.session[SESSION_OWNER] = False

            if self.env.context.get('action_approve_sale_order') and request:
                request.session[MANAGER_APPROVE] = self.id

    def _get_employee_fields_for_tablet(self):
        return [
            "id",
            "name",
        ]

    def _connect_employee(self):
        """
        This function sets the employee that is connecting (or that is already connected)
        as the first element of the array
        """
        employees = request.session.get(EMPLOYEES_CONNECTED, [])
        if len(employees) == 0:
            request.session[EMPLOYEES_CONNECTED] = [self.id]
            return
        if self.id not in employees:
            request.session[EMPLOYEES_CONNECTED] = [self.id] + employees

    def get_employees_wo_by_employees(self, employees_ids):
        """
        returns the workorders "in progress" associated to the employees passed in params (where they have already timesheeted)
        """
        employees = [{"id": id} for id in employees_ids]
        for emp in employees:
            emp["workorder"] = []
        return employees

    def get_wo_time_by_employees_ids(self, wo_id):
        """
        return the time timesheeted by an employee on a workorder
        """
        time_ids = self.env["mrp.workcenter.productivity"].search(
            [("employee_id", "=", self.id), ("workorder_id", "=", wo_id)]
        )
        sum_time_seconds = 0
        for time in time_ids:
            if time.date_end:
                sum_time_seconds += (time.date_end - time.date_start).total_seconds()
            else:
                sum_time_seconds += int(
                    (datetime.now() - time.date_start).total_seconds()
                )
        return sum_time_seconds / 60

    def stop_all_workorder_from_employee(self):
        """
        This stops all the workorders that the employee is currently working on
        We could use the stop_employee from mrp_workorder but it implies that me make several calls to the backend:
        1) get all the WO
        2) stop the employee on these WO
        """
        work_orders = self.env["mrp.workorder"].search(
            ["&", ("state", "=", "progress"), ("employee_ids.id", "in", self.ids)]
        )
        work_orders.stop_employee(self.ids)

    def get_employees_connected(self):
        if request:
            return request.session.get(EMPLOYEES_CONNECTED, [])
        else:
            # Test cases
            return [self.env.user.employee_id.id]

    def get_session_owner(self):
        if request:
            return request.session.get(SESSION_OWNER, [])
        else:
            # Test cases
            return [self.env.user.employee_id.id]

    def login_user_employee(self):
        if self.get_session_owner():
            return
        # If no admin is set, try to set the users employee as admin.
        user_employee = self.env.user.employee_id
        if user_employee and user_employee.login():
            return
        # If the user does not have an employee set, try the other logged in employees.
        for employee in self.get_employees_connected():
            if self.browse(employee).login():
                return
        # Just show the user's employee in the list.
        if user_employee:
            user_employee._connect_employee()

    def get_employees_retail_sale_by_employees(self, employees_ids):
        """
        returns the workorders "in progress" associated to the employees passed in params (where they have already timesheeted)
        """
        employees = [{"id": id} for id in employees_ids]
        for emp in employees:
            emp["workorder"] = []
        return employees

    def get_all_employees(self, login=False):
        self = self.sudo()
        if login:
            self.login_user_employee()
        all_employees = self.search_read(
            [("employee_type", "=", "employee")], ["id", "name", "barcode"]
        )

        for employee in all_employees:
            employee["is_show"] = self._get_employee_is_show(employee)
        all_employees_ids = {employee["id"] for employee in all_employees}
        employees_connected = list(
            filter(
                lambda employee_id: employee_id in all_employees_ids,
                self.get_employees_connected(),
            )
        )
        employees_connected = []
        out = {
            "admin": self.get_session_owner(),
            "connected": self.get_employees_retail_sale_by_employees(
                employees_connected
            ),
        }
        if self.env.user.has_group("base.group_system"):
            all_employees = list(all_employees)
        else:
            branch_ids = self.env.context.get("allowed_branch_ids")
            if not branch_ids:
                if self.env.user.branch_id:
                    branch_ids = self.env.user.branch_id.ids
                elif self.env.user.branch_ids:
                    branch_ids = self.env.user.branch_ids.ids
                else:
                    branch_ids = []
            
            if branch_ids:
                employee_ids = (
                    self.env["res.branch"].browse(branch_ids).mapped("employee_ids")
                )
                if employee_ids:
                    all_employees = list(
                        filter(
                            lambda employee: employee["id"] in employee_ids.ids,
                            all_employees,
                        )
                    )
                else:
                    all_employees = []
            else:
                all_employees = list(all_employees)

        out["all"] = all_employees
        return out

    def _get_employee_is_show(self, employee_data):
        return True

    def set_manager_approve_session(self, employee_id):
        """Set MANAGER_APPROVE in session when employee with approval action is selected

        This method sets the manager approval session variable when an employee
        with action_approve_sale_order capability is selected for approval workflows.

        Args:
            employee_id (int): ID of the selected employee

        Returns:
            dict: Success response
        """
        try:
            if request:
                request.session[MANAGER_APPROVE] = employee_id
                return {
                    'success': True,
                    'message': 'Manager approval session set successfully',
                    'employee_id': employee_id
                }
            else:
                return {
                    'success': False,
                    'message': 'No request session available'
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error setting manager approval session: {str(e)}'
            }


class HrEmployeePublic(models.Model):
    _inherit = "hr.employee.public"

    employee_type = fields.Selection(
        related="employee_id.employee_type",
        string="Employee Type",
    )
