# -*- coding: utf-8 -*-
from odoo.http import request
from odoo import api, fields, models

from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = "stock.picking"

    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
    )

    def button_validate(self):
        if not self.env.context.get('skip_backorder'):
            if not request.session.get("session_owner", False):
                raise ValidationError(self.env._("Please select an employee before validating the delivery order."))
            else:
                self.employee_id = request.session.get("session_owner", False)
                request.session["session_owner"] = False
        return super(StockPicking, self).button_validate()

    def _create_backorder(self, backorder_moves=None):
        self = self.with_context(erase_employee_id=True)
        return super()._create_backorder(backorder_moves)

    def copy(self, default=None):
        if self.env.context.get('erase_employee_id'):
            default['employee_id'] = None
        return super().copy(default)

    def action_validate_with_employee_selection(self):
        return {
            "type": "ir.actions.client",
            "tag": "action_validate_with_employee_selection",
            "params": {
                "model": "stock.picking",
                "picking_id": self.id,
                "picking_state": self.state,
                "picking_name": self.name,
                "picking_type": self.picking_type_id.name,
                "move_lines": self.move_ids.read(['id', 'product_id', 'quantity', 'product_uom_qty']),
            },
        }

    def action_employee_validation_for_stock_picking(self):
        try:
            if not request.session.get("session_owner", False):
                raise ValidationError(self.env._("Please select an employee before validating the delivery order."))

            result = self.with_context(skip_sms=True).button_validate()

            if isinstance(result, dict) and result.get('type') == 'ir.actions.act_window':
                return {
                    'success': True,
                    'wizard_action': result,
                    'message': 'Validation requires wizard confirmation'
                }
            else:
                return result
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Validation failed: {str(e)}'
            }
