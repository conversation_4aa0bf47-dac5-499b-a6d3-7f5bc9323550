# -*- coding: utf-8 -*-
from odoo.http import request
from odoo import api, fields, models

from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = "stock.picking"

    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
    )

    def button_validate(self):
        if not request.session.get("session_owner", False):
            raise ValidationError(self.env._("Please select an employee before validating the delivery order."))
        else:
            self.employee_id = request.session.get("session_owner", False)
            request.session["session_owner"] = False
        return super(StockPicking, self).button_validate()

    def _create_backorder(self, backorder_moves=None):
        self = self.with_context(erase_employee_id=True)
        return super()._create_backorder(backorder_moves)

    def copy(self, default=None):
        if self.env.context.get('erase_employee_id'):
            default['employee_id'] = None
        return super().copy(default)

    def action_validate_with_employee_selection(self):
        """Trigger client action for employee validation before stock picking validation

        This method replaces the direct button_validate() call with a client action
        that handles employee selection, PIN validation, and potential backorder wizards.

        Returns:
            dict: ir.actions.client action with parameters for employee validation
        """
        self.ensure_one()

        return {
            "type": "ir.actions.client",
            "tag": "action_validate_with_employee_selection",
            "params": {
                "model": "stock.picking",
                "picking_id": self.id,
                "picking_state": self.state,
                "picking_name": self.name,
                "picking_type": self.picking_type_id.name,
                "move_lines": self.move_ids.read(['id', 'product_id', 'quantity_done', 'product_uom_qty']),
                "next_action": {
                    "action": "button_validate",
                    "model": "stock.picking",
                    "method": "action_employee_validation_for_stock_picking"
                }
            },
        }

    def action_employee_validation_for_stock_picking(self):
        """Template method for employee validation workflow for stock picking

        This method is called after employee validation is complete.
        Base implementation calls button_validate() and handles wizard responses.

        Returns:
            dict: Success status or wizard action if backorder confirmation needed
        """
        self.ensure_one()

        try:
            # Set employee from session
            if not request.session.get("session_owner", False):
                raise ValidationError(self.env._("Please select an employee before validating the delivery order."))

            self.employee_id = request.session.get("session_owner", False)
            request.session["session_owner"] = False

            # Call the original button_validate method
            result = self.with_context(skip_sms=True).button_validate()

            # Check if result is a wizard action (backorder confirmation)
            if isinstance(result, dict) and result.get('type') == 'ir.actions.act_window':
                return {
                    'success': True,
                    'wizard_action': result,
                    'message': 'Validation requires wizard confirmation'
                }
            else:
                return {
                    'success': True,
                    'message': 'Picking validated successfully',
                    'refresh_form': True
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Validation failed: {str(e)}'
            }
