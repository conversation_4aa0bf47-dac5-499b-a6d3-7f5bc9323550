<odoo>
    <record id="view_picking_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <!-- Hide original Validate buttons -->
            <xpath expr="//button[@name='button_validate'][1]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='button_validate'][2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <!-- Add new Validate button with employee selection -->
            <xpath expr="//button[@name='button_validate'][1]" position="after">
                <button type="object"
                        string="Validate"
                        name="action_validate_with_employee_selection"
                        class="btn btn-primary"
                        data-hotkey="v"
                        invisible="state not in ('assigned', 'confirmed') or not move_ids"/>
            </xpath>
        </field>
    </record>
</odoo>