<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- Inherit the StatusBarButtons template to add Approve 2 button -->
    <t t-name="web.StatusBarButtons" t-inherit="web.StatusBarButtons" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('o_statusbar_buttons')]" position="inside">
            <!-- Approve Button for Sale Orders -->
            <t t-if="shouldShowApproveButton">
                <button
                    name="action_approve_sale_order"
                    string="Approve"
                    type="button"
                    class="btn btn-primary o_approve_button"
                    t-on-click="onPopEmployeeSelection"
                    data-hotkey="a">
                    Approve
                </button>
            </t>
            <!-- Validate Button for Stock Pickings -->
            <t t-if="shouldShowSelectEmployeeButton">
                <button
                    name="action_validate_with_employee"
                    string="Validate"
                    type="button"
                    t-att-class="getValidateButtonClass()"
                    t-on-click="onPopEmployeeSelection"
                    data-hotkey="v">
                    Validate
                </button>
            </t>
        </xpath>
    </t>

</templates>
