

import { StatusBarButtons } from "@web/views/form/status_bar_buttons/status_bar_buttons";
import { patch } from "@web/core/utils/patch";
import { pickUseConnectedEmployee } from "../../../employee_selection/employee_hooks";
import { useEffect, useState } from "@odoo/owl";
import { useBus } from "@web/core/utils/hooks";

patch(StatusBarButtons.prototype, {

    setup() {
        super.setup();
        this.modelAllowed = ['sale.order', 'stock.picking'];
        if (this.modelAllowed.includes(this.env.model?.root.resModel)) {
            this.useEmployee = pickUseConnectedEmployee("form", this.props.context, this.env);
            this.useEmployee.getConnectedEmployees();

            this.needApprove = useState({ value: false });

            useBus(this.env.model.bus, "update", async () => {
                await this.updateNeedApproveFromBackend();
            });

            useEffect(
                () => {
                    this.updateNeedApproveFromBackend();
                },
                () => [this.env.model.root.data.order_line]
            );
        }
    },

    async updateNeedApproveFromBackend() {
        try {
            if (this.env.model?.root.resModel !== 'sale.order') {
                return;
            }

            const record = this.env.model.root;
            if (!record || !record.data) {
                return;
            }

            // Get current order lines that are dirty or recently changed
            const orderLines = record.data.order_line.records;
            if (!orderLines || !Array.isArray(orderLines) || orderLines.length === 0) {
                this.needApprove.value = false;
                return;
            }

            let needsApproval = false;
            const order_line_data = orderLines.map(line => ({
                id: line.resId,
                product_id: line.data.product_id[0],
                discount: line.data.discount
            }));

            needsApproval = await this.env.services.orm.call(
                "sale.order",
                "need_employee_selection",
                [record.resId],
                {
                    'order_line': order_line_data,
                }
            );

            // Update reactive state
            if (this.needApprove.value !== needsApproval) {
                this.needApprove.value = needsApproval;
                console.log("Backend check - need_approve updated to:", needsApproval);
            }

        } catch (error) {
            console.error("Error checking need_employee_selection:", error);
            // On error, default to showing button if model data suggests it
            this.needApprove.value = this.env.model?.root?.data?.need_approve || false;
        }
    },
    async onPopEmployeeSelection(ev) {
        ev.preventDefault();
        ev.stopPropagation();

        try {
            if (!this.modelAllowed.includes(this.env.model?.root.resModel)) {
                console.warn("Employee selection only works on sale orders and stock pickings");
                return;
            }

            const record = this.env.model.root;

            console.log(`Validate with employee selection triggered for ${record.resModel} ID: ${record.resId}, state: ${record.data.state}`);

            // This for record to refresh, should keep
            if (record.resModel === 'stock.picking') {
                this.useEmployee.setFormSaveCallbacks({
                    refreshForm: this.refreshStockPickingForm.bind(this),
                    executeWizardAction: this.executeWizardAction.bind(this),
                    getRecordId: () => record.resId,
                    resModel: record.resModel
                });
            } else if (record.resModel === 'sale.order') {
                // Sale order callbacks (existing functionality)
                this.useEmployee.setFormSaveCallbacks({
                    saveForm: this.saveSaleOrderForm.bind(this),
                    refreshForm: this.refreshSaleOrderForm.bind(this),
                    getRecordId: () => record.resId,
                    resModel: record.resModel
                });
            }

            await this.useEmployee.getAllEmployees(record.resModel, record.resId);

            if (record.resModel === 'sale.order') {
                this.useEmployee.filterEmployeesByIsShow();
                this.useEmployee.popupAddEmployee();
            } else if (record.resModel === 'stock.picking') {
                this.useEmployee.popupAddEmployee();
            }

        } catch (error) {
            console.error("Error in employee selection button click:", error);
            this.env.services.notification.add("Error opening employee selection", { type: "danger" });
        }
    },

    async executeWizardAction(wizardAction) {
        try {
            const result = await this.env.services.action.doAction(wizardAction);

            setTimeout(async () => {
                try {
                    await this.refreshStockPickingForm();
                    console.log("Form refreshed after wizard completion");
                } catch (error) {
                    console.error("Error refreshing form after wizard:", error);
                }
            }, 1000);

            return result;
        } catch (error) {
            console.error("Error executing wizard action:", error);
            this.env.services.notification.add("Error executing wizard", { type: "danger" });
            throw error;
        }
    },

    async refreshStockPickingForm() {
        try {
            // Reload the model to get updated data from backend
            // Trigger re-render to update UI
            await this.env.model.root.load();
            this.render();

        } catch (error) {
            console.error("Error refreshing stock picking form:", error);
            this.env.services.notification.add("Error refreshing form", { type: "danger" });
        }
    },

    async refreshSaleOrderForm() {
        try {
            console.log("Refreshing sale order form after employee selection");
            await this.env.model.root.load();
            this.render();

            console.log("Sale order form refreshed successfully");

        } catch (error) {
            console.error("Error refreshing sale order form:", error);
            this.env.services.notification.add("Error refreshing form", { type: "danger" });
        }
    },

    async saveSaleOrderForm() {
        try {
            console.log("Saving sale order form after employee selection");
            // Should save order to avoid bypass trick from user 
            await this.env.model.root.save();
            this.render();

            console.log("Sale order form saved successfully");

        } catch (error) {
            console.error("Error saving sale order form:", error);
            this.env.services.notification.add("Error saving form", { type: "danger" });
        }
    },

    get shouldShowApproveButton() {
        if (this.env.model?.root.resModel !== 'sale.order') {
            return false;
        }

        const record = this.env.model.root;
        const modelNeedApprove = record && record.data && record.data.need_approve;
        const stateNeedApprove = this.needApprove?.value;

        const needApprove = this.env.model?.root ? (modelNeedApprove || stateNeedApprove) : false;

        if (needApprove) {
            console.log("shouldShowApproveButton: true - need_approve:", needApprove);
        }

        return needApprove;
    },

    get shouldShowSelectEmployeeButton() {
        if (this.env.model?.root.resModel !== 'stock.picking' || !this.env.model?.root.resId) {
            return false;
        }
        const record = this.env.model.root;
        const needSelectEmployee = record && record.data && record.data.state !== 'done';

        if (needSelectEmployee) {
            console.log("shouldShowSelectEmployeeButton: true - need_validate_with_employee:", needSelectEmployee, "state:", record.data.state);
        }

        return needSelectEmployee;
    },

    getValidateButtonClass() {
        if (this.env.model?.root.resModel !== 'stock.picking') {
            return "btn btn-secondary o_validate_employee_button";
        }

        const record = this.env.model.root;
        const state = record && record.data && record.data.state;

        // Follow stock module pattern: use oe_highlight (btn-primary) when state is 'assigned'
        if (state === 'assigned') {
            return "btn btn-primary oe_highlight o_validate_employee_button";
        } else {
            return "btn btn-secondary o_validate_employee_button";
        }
    },


});
