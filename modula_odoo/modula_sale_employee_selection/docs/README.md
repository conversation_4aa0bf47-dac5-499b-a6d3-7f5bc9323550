# Employee Selection Module Documentation

## 📋 **Documentation Overview**

This documentation provides comprehensive guidance for the `modula_sale_employee_selection` module, a production-ready employee selection and validation system for Odoo 18.

## 🎯 **Module Overview**

The `modula_sale_employee_selection` module provides a comprehensive employee selection and validation system for Odoo 18 with the following key features:

- **Employee PIN Authentication**: Secure PIN-based employee authentication with session management
- **Sales Order Approval**: Automatic approval workflows for discount changes requiring manager validation
- **Stock Picking Validation**: Employee selection before picking validation with backorder wizard support
- **Cross-Module Integration**: Client action support for discount approval workflows
- **Template Method Pattern**: Extensible architecture for dependent modules to customize employee selection logic
- **Session Management**: Robust employee session handling with multiple session types
- **Reactive UI**: Dynamic button visibility based on form state and business rules
- **Branch-based Employee Filtering**: Employee access control based on user branch permissions

## 🏗️ **Module Architecture**

### **Backend Models**
- **`hr_employee.py`** (294 lines) - Core employee management, session handling, PIN validation, branch filtering
- **`sale_order.py`** (38 lines) - Template methods for sales order approval workflows and employee assignment
- **`sale_order_line.py`** (9 lines) - Template methods for sales order lines approval logic
- **`stock_picking.py`** (31 lines) - Employee validation for stock picking operations with backorder support

### **Frontend Components**
- **`employee_hooks.js`** (642 lines) - Core employee selection logic, session management, and client action implementation
- **`employee_selection_button.js`** (233 lines) - Reactive button implementation with dynamic visibility
- **`controller.js`** (55 lines) - Sales form controller integration with employee selection
- **`views.js`** (12 lines) - Form view registration and override

### **UI Templates**
- **`popup.xml`** (19 lines) - Employee selection popup interface
- **`pin_popup.xml`** (35 lines) - PIN validation popup with numpad interface
- **`dialog_wrapper.js`** (20 lines) - Dialog wrapper component for consistent popup handling
- **`employee_selection_button.xml`** (35 lines) - Status bar button templates for approve/validate actions
- **`stock_picking_views.xml`** (15 lines) - Hide original validate buttons in stock picking forms

### **Security**
- **`ir.model.access.csv`** (3 lines) - Access control definitions for module models

## 🎯 **Start Here for AI Handoff**

### **Primary Document**
- **[AI_HANDOFF_DOCUMENT.md](AI_HANDOFF_DOCUMENT.md)** - **START HERE**
  - Complete module overview and current state
  - Critical implementation patterns and gotchas
  - Architecture and workflow documentation
  - Essential for understanding the module

## 📚 **Development Guidelines (Odoo18/)**

### **Core Guidelines**
- **[ODOO18_JAVASCRIPT_GUIDELINES.md](Odoo18/ODOO18_JAVASCRIPT_GUIDELINES.md)**
  - JavaScript patterns and best practices
  - Form state management (critical: _askChanges() vs save())
  - DOM manipulation for UI control
  - Service usage and error handling
  - **Updated with real experience from this project**

- **[odoo_python_development_guidelines.md](Odoo18/odoo_python_development_guidelines.md)**
  - Python development patterns for Odoo 18
  - Template method pattern implementation
  - Breaking changes and migration guidelines
  - **Updated with template method pattern experience**

- **[ODOO18_XML_TEMPLATE_GUIDELINES.md](Odoo18/ODOO18_XML_TEMPLATE_GUIDELINES.md)**
  - XML template development patterns
  - Component template structure
  - Template naming conventions

- **[odoo_xml_view_guide_line.md](Odoo18/odoo_xml_view_guide_line.md)**
  - XML view development guidelines
  - Form view patterns and best practices
  - View inheritance and customization

- **[CLIENT_ACTION_IMPLEMENTATION_GUIDE.md](Odoo18/CLIENT_ACTION_IMPLEMENTATION_GUIDE.md)**
  - Complete guide for implementing `ir.actions.client` actions
  - Service access patterns and parameter passing
  - Template method integration and testing patterns
  - **Based on successful `action_validate_with_employee_selection` implementation**

- **[DIALOG_MANAGEMENT_PATTERNS.md](Odoo18/DIALOG_MANAGEMENT_PATTERNS.md)**
  - Proven patterns for dialog management in Odoo 18
  - Critical `env.services.dialog.closeAll()` pattern
  - Nested dialog handling and state management
  - **Based on employee selection + PIN validation implementation**

- **[STOCK_PICKING_CLIENT_ACTION_IMPLEMENTATION.md](STOCK_PICKING_CLIENT_ACTION_IMPLEMENTATION.md)**
  - Migration from JavaScript template to client action approach
  - XML button implementation with `ir.actions.client` pattern
  - Unified client action handler for multiple models
  - **Based on successful stock picking validation implementation**

## 🤖 **AI Development Support**

### **AI Workflow Documents**
- **[AI_DEVELOPMENT_WORKFLOW.md](AI_DEVELOPMENT_WORKFLOW.md)**
  - Step-by-step AI development process
  - Context management and recovery strategies
  - Best practices for AI-assisted development

- **[AI_PROMPT_TEMPLATES.md](AI_PROMPT_TEMPLATES.md)**
  - Ready-to-use prompt templates
  - Context-aware prompting strategies
  - Specific prompts for different development tasks

## 🧪 **Testing and Quality**

### **Testing Documentation**
- **[TESTING_INSTRUCTIONS.md](TESTING_INSTRUCTIONS.md)**
  - Comprehensive testing procedures
  - Functional and technical test cases
  - Verification checklists

## 🎯 **Quick Reference for AI**

### **Critical Patterns from This Project**

#### **1. Form State Management**
```javascript
// ✅ CRITICAL: Preserve field values without saving
await this.model._askChanges(); // Commits to memory only

// ❌ WRONG: This saves to backend
await this.model.root.save();
```

#### **2. Button Visibility Control**
```javascript
// ✅ CORRECT: DOM manipulation for immediate feedback
document.getElementsByName('action_name')[0].style.display = 'none';

// ❌ WRONG: Backend changes won't persist without save
await orm.call("model", "write", [[id], {field: value}]);
```

#### **3. Client Action Pattern**
```javascript
// Client action implementation
export async function MyClientAction(env, action) {
    try {
        // Use env.services directly (not useService hooks)
        const result = await env.services.orm.call("model", "method", [params]);

        if (result) {
            env.services.notification.add(_t("Success"), { type: "success" });
            env.services.action.doAction({ type: 'ir.actions.act_window_close' });
        }
    } catch (error) {
        env.services.notification.add(_t("Error: ") + error.message, { type: "danger" });
    }
}

registry.category("actions").add("my_client_action", MyClientAction);
```

#### **4. Dialog Management Pattern**
```javascript
// ✅ CRITICAL: Use closeAll() for reliable dialog closing
env.services.dialog.closeAll(); // Closes all dialogs reliably

// ❌ WRONG: Individual dialog closing may fail
dialog.close(); // May not work consistently
```

#### **5. Template Method Pattern**
```python
# Base module - template method
def _get_employee_is_show(self, employee_data):
    return True  # Default behavior

# Dependent module - override
def _get_employee_is_show(self, employee_data):
    # Specific business logic
    return employee.job_id.name == 'Store Manager'
```

#### **6. Employee Session Management**
```python
# Session constants
EMPLOYEES_CONNECTED = "employees_connected"
SESSION_OWNER = "session_owner"
MANAGER_APPROVE = "manager_approve"

# Employee login with context
def login(self, pin=False, set_in_session=True):
    if self.pin_validation(pin) and set_in_session:
        if self.env.context.get('action_approve_sale_order'):
            request.session[MANAGER_APPROVE] = self.id
        else:
            self._connect_employee()
            request.session[SESSION_OWNER] = self.id
        return True
    return False
```

#### **7. Reactive Button Implementation**
```javascript
// Reactive state for button visibility
this.needApprove = useState({ value: false });

// Listen to model changes
useBus(this.env.model.bus, "update", async () => {
    await this.updateNeedApproveFromBackend();
});

// Watch order line changes
useEffect(
    () => {
        this.updateNeedApproveFromBackend();
    },
    () => [this.env.model.root.data.order_line]
);
```

#### **8. Branch-based Employee Filtering**
```python
# Employee filtering based on user branch permissions
def get_all_employees(self, login=False):
    # Get user's branch access
    branch_ids = self.env.context.get("allowed_branch_ids")
    if self.env.user.branch_id:
        branch_ids = self.env.user.branch_id.ids
    if not branch_ids:
        branch_ids = self.env.user.branch_ids.ids
    
    # Filter employees by branch
    if self.env.user.has_group("base.group_system"):
        all_employees = list(all_employees)
    elif branch_ids:
        employee_ids = self.env["res.branch"].browse(branch_ids).mapped("employee_ids")
        if employee_ids:
            all_employees = list(
                filter(
                    lambda employee: employee["id"] in employee_ids.ids,
                    all_employees,
                )
            )
```

#### **9. Stock Picking Validation with Backorder Support**
```python
# Employee validation for stock picking
def button_validate(self):
    if not request.session.get("session_owner", False):
        raise ValidationError(self.env._("Please select an employee before validating the delivery order."))
    else:
        self.employee_id = request.session.get("session_owner", False)
        request.session["session_owner"] = False
    return super(StockPicking, self).button_validate()

# Backorder creation without employee assignment
def _create_backorder(self, backorder_moves=None):
    self = self.with_context(erase_employee_id=True)
    return super()._create_backorder(backorder_moves)
```

#### **10. Sales Order Employee Assignment**
```python
# Automatic employee assignment on sale order creation
@api.model_create_multi
def create(self, vals_list):
    for vals in vals_list:
        if request.session.get("session_owner", False):
            vals["employee_id"] = request.session.get("session_owner", False)
    request.session['session_owner'] = False
    records = super(SaleOrder, self).create(vals_list)
    return records
```

## 🔧 **Module Dependencies**

- **`web`** - Core web framework and UI components
- **`hr`** - Human resources module for employee management
- **`sale_management`** - Sales order management functionality
- **`stock`** - Stock picking and inventory management

## 🚀 **Key Features**

### **Employee Authentication**
- PIN-based authentication system
- Session management with multiple session types
- Branch-based employee access control
- Manager approval workflows

### **Sales Order Integration**
- Automatic employee assignment on order creation
- Discount approval workflows requiring manager validation
- Template methods for extensible approval logic
- Reactive UI with dynamic button visibility

### **Stock Picking Integration**
- Employee selection before picking validation
- Backorder wizard support
- Employee assignment tracking
- Validation error handling

### **Frontend Architecture**
- Client action implementation for discount approval
- Dialog management with nested popup support
- Form controller integration
- Status bar button customization

## 📝 **Usage Examples**

### **Basic Employee Selection**
```javascript
// Trigger employee selection for stock picking
await this.useEmployee.popupAddEmployee('validate_with_employee_selection');
```

### **PIN Validation**
```javascript
// Validate employee PIN
const loginResult = await env.services.orm.call(
    "hr.employee",
    "login",
    [employeeId, pin],
    { context }
);
```

### **Client Action Implementation**
```javascript
// Register client action for discount approval
registry.category("actions").add("action_validate_with_employee_selection", ActionValidateWithEmployeeSelection);
```

## 🔍 **Testing Scenarios**

1. **Employee PIN Authentication**
   - Valid PIN login
   - Invalid PIN rejection
   - Session management

2. **Sales Order Approval**
   - Discount threshold validation
   - Manager approval workflow
   - Employee assignment

3. **Stock Picking Validation**
   - Employee selection requirement
   - Backorder wizard handling
   - Validation error scenarios

4. **Branch-based Access Control**
   - Employee filtering by branch
   - Permission-based access
   - System administrator override

## 📚 **Related Documentation**

- **[CURRENT_IMPLEMENTATION_SUMMARY.md](CURRENT_IMPLEMENTATION_SUMMARY.md)** - Detailed implementation overview
- **[MODULE_OVERVIEW.md](MODULE_OVERVIEW.md)** - High-level module architecture
- **[FIXES_AND_UPDATES.md](FIXES_AND_UPDATES.md)** - Recent changes and improvements
- **[IMPLEMENTATION_SUCCESS_SUMMARY.md](IMPLEMENTATION_SUCCESS_SUMMARY.md)** - Success metrics and outcomes
- **[STOCK_PICKING_CLIENT_ACTION_IMPLEMENTATION.md](STOCK_PICKING_CLIENT_ACTION_IMPLEMENTATION.md)** - Stock picking client action migration

## Recent Updates (Latest Changes)

### Enhanced Branch-Based Employee Filtering (Latest Update)
**File**: `models/hr_employee.py` - `get_all_employees` method

**Key Enhancements**:
- **Enhanced Branch Logic**: Improved branch filtering to handle users without assigned branches
- **New Requirement Implementation**: Users without assigned branches can now see all employees without filtering
- **Improved Code Structure**: Cleaner, more maintainable branch filtering logic
- **Enhanced Error Handling**: Better handling of edge cases in branch assignment

**Business Logic**:
1. **System Administrators**: Always see all employees
2. **Users with Branch Assignment**: See only employees from their assigned branches
3. **Users without Branch Assignment**: See all employees without filtering (NEW REQUIREMENT)
4. **Context Override**: `allowed_branch_ids` in context can override user's branch assignment
