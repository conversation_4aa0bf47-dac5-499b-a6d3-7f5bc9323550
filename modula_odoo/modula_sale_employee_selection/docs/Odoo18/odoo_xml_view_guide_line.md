# Odoo 18 XML View Creation Guide

This guide provides best practices and examples for creating XML views in Odoo modules, with special focus on avoiding common XML errors that cause module loading failures.

## ⚠️ CRITICAL: Common XML Errors and How to Avoid Them

### Error 1: External ID Not Found (Most Common)
**Problem**: `External ID not found in the system: module_name.xml_id`

**Root Cause**: Referencing external IDs from other modules incorrectly

## 🆕 NEW ODOO 18 GUIDELINES

### 1. Define Field Paths in Actions for Better URLs 🔗

**New Feature**: Odoo 18 supports the `path` field in `ir.actions.act_window` records to create nicer, more SEO-friendly URLs.

#### ✅ Correct Odoo 18 Pattern with Path Field
```xml
<!-- CORRECT: Use path field for better URLs -->
<record id="action_delivery_postcode_pricelist" model="ir.actions.act_window">
    <field name="name">Postcode Pricelists</field>
    <field name="res_model">delivery.postcode.pricelist</field>
    <field name="path">postcode-pricelists</field>  <!-- ✅ NEW: Better URL -->
    <field name="view_mode">list,form</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first postcode pricelist!
        </p>
    </field>
</record>
```

#### 🔍 Real-World Examples from Odoo 18 Core
```xml
<!-- From account/views/account_move_views.xml -->
<record id="action_move_in_invoice" model="ir.actions.act_window">
    <field name="name">Bills</field>
    <field name="res_model">account.move</field>
    <field name="path">vendor-bills</field>  <!-- ✅ Creates /web#action=...&path=vendor-bills -->
    <field name="view_mode">list,kanban,form,activity</field>
</record>

<!-- From hr_work_entry/views/hr_work_entry_views.xml -->
<record id="hr_work_entry_action" model="ir.actions.act_window">
    <field name="name">Work Entry</field>
    <field name="res_model">hr.work.entry</field>
    <field name="path">work-entries</field>  <!-- ✅ Creates /web#action=...&path=work-entries -->
    <field name="view_mode">calendar,list,form,pivot</field>
</record>

<!-- From iap/views/iap_views.xml -->
<record id="iap_account_action" model="ir.actions.act_window">
    <field name="name">IAP Account</field>
    <field name="res_model">iap.account</field>
    <field name="path">iap-accounts</field>  <!-- ✅ Creates /web#action=...&path=iap-accounts -->
    <field name="view_mode">list,form</field>
</record>
```

#### 📋 Path Field Best Practices
- **Use kebab-case**: `postcode-pricelists`, `vendor-bills`, `work-entries`
- **Be descriptive**: Path should clearly indicate the content
- **Keep it short**: Avoid overly long paths
- **Use hyphens**: Not underscores or spaces
- **Be consistent**: Follow the same naming pattern across your module

### 2. Simplified Chatter Implementation 💬

**Breaking Change**: The old `<div class="oe_chatter">` pattern has been replaced with a simple `<chatter/>` tag.

#### ✅ Correct Odoo 18 Chatter Pattern
```xml
<!-- CORRECT: New simplified chatter tag -->
<form string="My Model">
    <sheet>
        <group>
            <field name="name"/>
            <field name="description"/>
        </group>
    </sheet>
    <chatter/>  <!-- ✅ NEW: Simple chatter tag -->
</form>
```

#### ❌ Deprecated Odoo 17 Chatter Pattern
```xml
<!-- WRONG: Old complex chatter structure -->
<form string="My Model">
    <sheet>
        <group>
            <field name="name"/>
            <field name="description"/>
        </group>
    </sheet>
    <!-- ❌ OLD: Complex chatter structure -->
    <div class="oe_chatter">
        <field name="message_follower_ids" widget="mail_followers"/>
        <field name="activity_ids" widget="mail_activity"/>
        <field name="message_ids" widget="mail_thread"/>
    </div>
</form>
```

#### 🔍 Real-World Examples from Odoo 18 Core
```xml
<!-- From account/views/res_company_views.xml -->
<form string="Company">
    <sheet>
        <!-- Company fields here -->
    </sheet>
    <chatter/>  <!-- ✅ Simple chatter implementation -->
</form>

<!-- From website_forum/views/forum_tag_views.xml -->
<form string="Tag">
    <sheet>
        <group>
            <field name="name"/>
            <field name="color" widget="color_picker"/>
            <field name="forum_id" options="{'no_create_edit': True}"/>
        </group>
    </sheet>
    <chatter/>  <!-- ✅ Simple chatter implementation -->
</form>

<!-- From website_slides/views/slide_channel_views.xml -->
<form string="Course">
    <sheet>
        <!-- Course fields here -->
    </sheet>
    <chatter/>  <!-- ✅ Simple chatter implementation -->
</form>
```

#### 📋 Chatter Migration Guide
```xml
<!-- BEFORE (Odoo 17 and earlier) -->
<div class="oe_chatter">
    <field name="message_follower_ids" widget="mail_followers"/>
    <field name="activity_ids" widget="mail_activity"/>
    <field name="message_ids" widget="mail_thread"/>
</div>

<!-- AFTER (Odoo 18) -->
<chatter/>
```

**Benefits of New Chatter Tag**:
- **Simplified syntax**: Just one tag instead of complex div structure
- **Better performance**: Optimized rendering and loading
- **Automatic features**: All chatter functionality included automatically
- **Responsive design**: Better mobile and tablet support
- **Future-proof**: Easier to maintain and update

## 🔧 CRITICAL: Common XML Errors and How to Avoid Them

### Error 1: External ID Not Found (Most Common)
**Problem**: `External ID not found in the system: module_name.xml_id`

**Root Cause**: Referencing external IDs from other modules incorrectly

#### ✅ Correct External ID Format
```xml
<!-- CORRECT: Use module_name.xml_id format -->
<field name="parent_id" ref="stock.menu_stock_root"/>
<field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
<field name="product_id" ref="delivery.product_product_delivery"/>
```

#### ❌ Common Mistakes to Avoid
```xml
<!-- WRONG: Missing module prefix -->
<field name="parent_id" ref="menu_stock_root"/>

<!-- WRONG: Incorrect module name -->
<field name="inherit_id" ref="delivery_carrier.view_delivery_carrier_form"/>

<!-- WRONG: Non-existent ID -->
<field name="product_id" ref="delivery.product_product_delivery_normal"/>
```

#### 🔍 How to Find Correct External IDs
1. **Check the source module's XML files**
2. **Use Odoo's developer mode**: Settings > Activate Developer Mode > View metadata
3. **Search in Odoo source code**: `grep -r "id=\"xml_id\"" addons/module_name/`
4. **Common external IDs reference table**:

```xml
<!-- Stock Module -->
stock.menu_stock_root
stock.group_stock_user
stock.group_stock_manager
stock.picking_type_out
stock.stock_location_stock
stock.stock_location_customers

<!-- Delivery Module -->
delivery.view_delivery_carrier_form
delivery.product_product_delivery
delivery.product_category_deliveries

<!-- Sales Module -->
sales_team.group_sale_salesman
sale.group_sale_user
sale.group_sale_manager

<!-- Base Module -->
base.group_user
base.group_system
base.group_no_one
```

### Error 2: XML ID Order Dependencies (Second Most Common)
**Problem**: `Element with xml_id 'current_module.xml_id' not found`

**Root Cause**: XML files loaded in wrong order, referencing IDs that don't exist yet

**Solutions**:

#### ✅ Correct File Order in __manifest__.py
```python
# CORRECT: Define views before actions, actions before menus
'data': [
    'security/ir.model.access.csv',           # 1. Security first
    'data/initial_data.xml',                  # 2. Initial data
    'views/model_views.xml',                  # 3. Views first
    'views/other_model_views.xml',            # 4. More views
    'views/menu_actions.xml',                 # 5. Actions and menus last
],
```

#### ❌ Wrong File Order
```python
# WRONG: Menus before views, actions before views
'data': [
    'views/menu_actions.xml',                 # ERROR: References views not yet loaded
    'views/model_views.xml',
    'security/ir.model.access.csv',          # ERROR: Should be first
],
```

#### 🔧 File Organization Best Practices
1. **Single file approach** (recommended for small modules):
```python
'data': [
    'security/ir.model.access.csv',
    'views/all_views.xml',  # Contains views, actions, and menus in correct order
    'data/demo_data.xml',
],
```

2. **Multiple files approach** (for larger modules):
```python
'data': [
    'security/ir.model.access.csv',
    'views/model1_views.xml',      # Views only
    'views/model2_views.xml',      # Views only
    'views/actions.xml',           # Actions only
    'views/menus.xml',             # Menus only (references actions)
    'data/initial_data.xml',
],
```

#### 🔍 Within XML File Order
```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 1. Views first -->
    <record id="view_model_form" model="ir.ui.view">
        <!-- View definition -->
    </record>

    <record id="view_model_list" model="ir.ui.view">
        <!-- View definition -->
    </record>

    <!-- 2. Actions second (can reference views) -->
    <record id="action_model" model="ir.actions.act_window">
        <field name="view_mode">list,form</field>
        <!-- Action references views defined above -->
    </record>

    <!-- 3. Menus last (can reference actions) -->
    <menuitem id="menu_model"
        action="action_model"
        parent="external_module.parent_menu"/>
</odoo>
```

## Basic Structure

All views should be defined using the `record` tag with `model='ir.ui.view'`. Here's the basic structure:

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- View Definition -->
    <record id="view_model_name_form" model="ir.ui.view">
        <field name="name">model.name.form</field>
        <field name="model">model.name</field>
        <field name="arch" type="xml">
            <form>
                <!-- Form view content -->
            </form>
        </field>
    </record>

    <!-- List View (using list tag instead of tree) -->
    <record id="view_model_name_list" model="ir.ui.view">
        <field name="name">model.name.list</field>
        <field name="model">model.name</field>
        <field name="arch" type="xml">
            <list>
                <field name="field1"/>
                <field name="field2"/>
            </list>
        </field>
    </record>

    <!-- Action Definition -->
    <record id="action_model_name" model="ir.actions.act_window">
        <field name="name">Model Name</field>
        <field name="res_model">model.name</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first record!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_model_name"
        name="Model Name"
        action="action_model_name"
        parent="parent_menu_id"
        sequence="10"/>
</odoo>
```

## ⚠️ CRITICAL: Odoo 18 Breaking Changes

### 1. MANDATORY: Use `<list>` Instead of `<tree>` ❌➡️✅

**BREAKING CHANGE**: Odoo 18 requires `<list>` tags instead of `<tree>` for list views.

#### ✅ CORRECT Odoo 18 Pattern
```xml
<!-- CORRECT: Use <list> tag -->
<record id="view_model_list" model="ir.ui.view">
    <field name="name">model.list</field>
    <field name="model">model.name</field>
    <field name="arch" type="xml">
        <list string="Model List">
            <field name="name"/>
            <field name="date"/>
            <field name="state"/>
        </list>
    </field>
</record>

<!-- CORRECT: Use list in view_mode -->
<record id="action_model" model="ir.actions.act_window">
    <field name="name">Models</field>
    <field name="res_model">model.name</field>
    <field name="view_mode">list,form</field>  <!-- ✅ Use "list" not "tree" -->
</record>
```

#### ❌ WRONG Odoo 17 Pattern (Will Cause Errors)
```xml
<!-- WRONG: <tree> tag is deprecated -->
<record id="view_model_tree" model="ir.ui.view">
    <field name="name">model.tree</field>
    <field name="model">model.name</field>
    <field name="arch" type="xml">
        <tree string="Model Tree">  <!-- ❌ Will cause errors -->
            <field name="name"/>
            <field name="date"/>
            <field name="state"/>
        </tree>  <!-- ❌ Will cause errors -->
    </field>
</record>

<!-- WRONG: Use tree in view_mode -->
<record id="action_model" model="ir.actions.act_window">
    <field name="name">Models</field>
    <field name="res_model">model.name</field>
    <field name="view_mode">tree,form</field>  <!-- ❌ Will cause errors -->
</record>
```

#### 🔍 Complete Migration Checklist
- [ ] **Replace ALL `<tree>` tags with `<list>`**
- [ ] **Update view_mode from "tree" to "list"**
- [ ] **Update view IDs from "tree" to "list" (recommended)**
- [ ] **Update view names from "tree" to "list" (recommended)**

#### 📋 Search and Replace Guide
```bash
# Find all tree tags in your module
grep -r "<tree" your_module/views/

# Find all tree view modes
grep -r "view_mode.*tree" your_module/views/

# Replace patterns:
<tree>          → <list>
</tree>         → </list>
tree,form       → list,form
form,tree       → form,list
view_model_tree → view_model_list
model.tree      → model.list
```

### 2. Field Reference Validation ⚠️ **CRITICAL**

**PROBLEM**: Referencing non-existent fields in XML views causes module loading failures.

#### ✅ CORRECT Field Reference Patterns
```xml
<!-- CORRECT: Reference fields that exist on the model -->
<record id="view_delivery_group_form" model="ir.ui.view">
    <field name="model">delivery.group</field>
    <field name="arch" type="xml">
        <form>
            <!-- ✅ These fields exist on delivery.group model -->
            <field name="name"/>
            <field name="carrier_id"/>
            <field name="group_type"/>
            <field name="delivery_price"/>

            <!-- ✅ CORRECT: Reference related model fields through relationship -->
            <field name="order_line_ids">
                <list>
                    <!-- ✅ These fields exist on sale.order.line model -->
                    <field name="product_id"/>
                    <field name="name"/>
                    <field name="product_uom_qty"/>
                </list>
            </field>
        </form>
    </field>
</record>
```

#### ❌ WRONG Field Reference Patterns
```xml
<!-- WRONG: Referencing fields that don't exist on the model -->
<record id="view_delivery_group_form_wrong" model="ir.ui.view">
    <field name="model">delivery.group</field>
    <field name="arch" type="xml">
        <form>
            <!-- ❌ product_id doesn't exist on delivery.group -->
            <field name="product_id"/>

            <!-- ❌ customer_name doesn't exist on delivery.group -->
            <field name="customer_name"/>

            <!-- ❌ Wrong field names -->
            <field name="delivery_cost"/>  <!-- Should be delivery_price -->
        </form>
    </field>
</record>
```

#### 🔍 Field Validation Checklist
- [ ] **Verify field exists on the model** before referencing in XML
- [ ] **Check field names match exactly** (case-sensitive)
- [ ] **Use related fields correctly** through relationships
- [ ] **Test XML loading** to catch field errors early

#### 📋 Field Validation Commands
```bash
# Check if field exists in model definition
grep -r "field_name.*=" your_module/models/

# Validate XML syntax
odoo-bin -d test_db --test-enable --stop-after-init -i your_module

# Check field references in views
grep -r "field name=" your_module/views/
```

### 3. External ID Existence Validation ⚠️ **CRITICAL**

**PROBLEM**: Referencing non-existent external IDs in `inherit_id` causes module loading failures.

#### ✅ CORRECT External ID Validation Process

**MANDATORY STEPS before using any external ID:**

1. **Locate Source Module Files**
   ```bash
   # Find XML files in target module
   find odoo/addons/sale -name "*.xml" | head -10
   # Result: odoo/addons/sale/views/sale_order_views.xml, etc.
   ```

2. **Search for Specific ID**
   ```bash
   # Check if external ID actually exists
   grep -r "id=\"view_order_line_form\"" odoo/addons/sale/
   # If no results, the ID doesn't exist!
   ```

3. **Find Available IDs**
   ```bash
   # List all view IDs in target module
   grep -r "record.*id.*view.*model.*ir.ui.view" odoo/addons/sale/views/
   # Shows actual available view IDs
   ```

4. **Verify ID Format**
   ```bash
   # Check exact ID format in source
   grep -A 5 -B 5 "id=\"actual_view_id\"" odoo/addons/sale/views/sale_order_views.xml
   ```

#### ❌ WRONG External ID Usage Patterns

```xml
<!-- WRONG: Assuming ID exists without verification -->
<record id="my_inherit_view" model="ir.ui.view">
    <field name="model">sale.order.line</field>
    <field name="inherit_id" ref="sale.view_order_line_form"/>  <!-- ❌ Doesn't exist -->
</record>

<!-- WRONG: Using incorrect module prefix -->
<record id="my_inherit_view" model="ir.ui.view">
    <field name="inherit_id" ref="sales.view_order_form"/>  <!-- ❌ Wrong module name -->
</record>

<!-- WRONG: Using made-up ID names -->
<record id="my_inherit_view" model="ir.ui.view">
    <field name="inherit_id" ref="sale.order_line_form_view"/>  <!-- ❌ Doesn't exist -->
</record>
```

#### ✅ CORRECT External ID Usage Patterns

```xml
<!-- CORRECT: Verified ID exists in source module -->
<record id="my_inherit_view" model="ir.ui.view">
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>  <!-- ✅ Verified to exist -->
</record>

<!-- CORRECT: Using developer mode to find ID -->
<!-- 1. Enable developer mode -->
<!-- 2. Go to target view -->
<!-- 3. Click "Edit View: Form" -->
<!-- 4. Check "External ID" field -->
```

#### 🔍 External ID Discovery Methods

**Method 1: Source Code Search**
```bash
# Find all view IDs for a specific model
grep -r "model.*sale.order.line.*record.*id" odoo/addons/sale/views/

# Find views by name pattern
grep -r "sale.*order.*line.*form" odoo/addons/sale/views/

# List all external IDs in a module
grep -r "record.*id=" odoo/addons/sale/views/ | grep "ir.ui.view"
```

**Method 2: Developer Mode (Recommended)**
```
1. Enable Developer Mode in Odoo
2. Navigate to the target view (e.g., Sale Order Line form)
3. Click "Edit View: Form" in developer menu
4. Check "External ID" field - this shows the actual ID
5. Use this exact ID in your inheritance
```

**Method 3: Database Query**
```sql
-- Find external IDs for specific model views
SELECT name, module, res_id
FROM ir_model_data
WHERE model = 'ir.ui.view'
AND name LIKE '%order_line%form%';
```

#### 📋 Common External ID Patterns

**Sale Module Common IDs:**
```xml
<!-- ✅ VERIFIED: These IDs exist in sale module -->
<field name="inherit_id" ref="sale.view_order_form"/>              <!-- Sale Order Form -->
<field name="inherit_id" ref="sale.view_quotation_tree"/>          <!-- Sale Order List -->
<field name="inherit_id" ref="sale.view_order_tree"/>              <!-- Sale Order Tree -->

<!-- ⚠️ VERIFY FIRST: These may not exist -->
<field name="inherit_id" ref="sale.view_order_line_form"/>         <!-- May not exist -->
<field name="inherit_id" ref="sale.sale_order_line_form_view"/>    <!-- May not exist -->
```

**Stock Module Common IDs:**
```xml
<!-- ✅ VERIFIED: Common stock view IDs -->
<field name="inherit_id" ref="stock.view_picking_form"/>
<field name="inherit_id" ref="stock.vpicktree"/>
```

#### 🛠️ Debugging External ID Errors

**Error**: `ValueError: External ID not found in the system: sale.view_order_line_form`

**Solution Steps**:

1. **Verify Module Installation**
   ```bash
   # Check if module is installed
   odoo-bin shell -d your_database
   >>> self.env['ir.module.module'].search([('name', '=', 'sale')])
   ```

2. **Find Correct External ID**
   ```bash
   # Search for similar IDs
   grep -r "order_line.*form" odoo/addons/sale/views/
   grep -r "sale.*order.*line" odoo/addons/sale/views/
   ```

3. **Use Alternative Approach**
   ```xml
   <!-- Instead of inheriting non-existent view, create new view -->
   <record id="view_order_line_custom_form" model="ir.ui.view">
       <field name="name">sale.order.line.custom.form</field>
       <field name="model">sale.order.line</field>
       <field name="arch" type="xml">
           <form>
               <!-- Custom form definition -->
           </form>
       </field>
   </record>
   ```

4. **Remove Invalid Inheritance**
   ```xml
   <!-- Comment out or remove invalid inheritance -->
   <!--
   <record id="invalid_inherit_view" model="ir.ui.view">
       <field name="inherit_id" ref="sale.non_existent_view"/>
   </record>
   -->
   ```

### 4. View Inheritance XPath Validation ⚠️ **CRITICAL**

**PROBLEM**: Using XPath expressions that reference non-existent elements in parent views causes inheritance failures.

#### ✅ CORRECT View Inheritance Patterns
```xml
<!-- STEP 1: Always check the parent view structure first -->
<!-- Check odoo/addons/sale/views/sale_order_views.xml for available fields -->

<!-- CORRECT: Reference fields that exist in parent view -->
<record id="view_order_form_inherit_multi_delivery" model="ir.ui.view">
    <field name="name">sale.order.form.inherit.multi.delivery</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <!-- ✅ partner_shipping_id exists in base sale.view_order_form -->
        <xpath expr="//field[@name='partner_shipping_id']" position="after">
            <field name="custom_field"/>
        </xpath>

        <!-- ✅ notebook always exists in form views -->
        <xpath expr="//notebook" position="inside">
            <page string="Custom Tab">
                <!-- Custom content -->
            </page>
        </xpath>
    </field>
</record>
```

#### ❌ WRONG View Inheritance Patterns
```xml
<!-- WRONG: Referencing fields that don't exist in parent view -->
<record id="view_order_form_inherit_wrong" model="ir.ui.view">
    <field name="name">sale.order.form.inherit.wrong</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <!-- ❌ carrier_id might not exist in base sale view -->
        <!-- It's added by delivery module, not guaranteed to be present -->
        <xpath expr="//field[@name='carrier_id']" position="after">
            <field name="custom_field"/>
        </xpath>

        <!-- ❌ custom_section doesn't exist in parent view -->
        <xpath expr="//group[@name='custom_section']" position="inside">
            <field name="another_field"/>
        </xpath>
    </field>
</record>
```

#### 🔍 Parent View Validation Process

**MANDATORY STEPS before creating view inheritance:**

1. **Locate Parent View File**
   ```bash
   # Find the parent view definition
   find odoo/addons -name "*.xml" -exec grep -l "id=\"view_order_form\"" {} \;
   # Result: odoo/addons/sale/views/sale_order_views.xml
   ```

2. **Examine Parent View Structure**
   ```bash
   # Check available fields and groups in parent view
   grep -A 50 -B 5 'id="view_order_form"' odoo/addons/sale/views/sale_order_views.xml
   ```

3. **Verify Field Existence**
   ```bash
   # Check if specific field exists in parent view
   grep -n "field.*name.*partner_shipping_id" odoo/addons/sale/views/sale_order_views.xml
   ```

4. **Test XPath Expression**
   ```bash
   # Validate inheritance works
   odoo-bin -d test_db --test-enable --stop-after-init -i your_module
   ```

#### 📋 Safe XPath Targets for Common Views

**Sale Order Form (`sale.view_order_form`)**
```xml
<!-- ✅ SAFE: These fields typically exist -->
<xpath expr="//field[@name='partner_id']" position="after">
<xpath expr="//field[@name='partner_shipping_id']" position="after">
<xpath expr="//field[@name='partner_invoice_id']" position="after">
<xpath expr="//field[@name='date_order']" position="after">
<xpath expr="//notebook" position="inside">
<xpath expr="//header" position="inside">

<!-- ⚠️ RISKY: These fields may not exist in base view -->
<xpath expr="//field[@name='carrier_id']" position="after">  <!-- Added by delivery module -->
<xpath expr="//field[@name='incoterm']" position="after">     <!-- Added by stock module -->
<xpath expr="//field[@name='commitment_date']" position="after"> <!-- Added by sale_stock -->
```

**Product Form (`product.product_template_only_form_view`)**
```xml
<!-- ✅ SAFE: These fields typically exist -->
<xpath expr="//field[@name='name']" position="after">
<xpath expr="//field[@name='categ_id']" position="after">
<xpath expr="//field[@name='list_price']" position="after">
<xpath expr="//notebook" position="inside">
```

#### 🛠️ Debugging XPath Inheritance Issues

**Error 1**: `Element '<xpath expr="//field[@name='field_name']">' cannot be located in parent view`

**Solution Steps**:
1. **Check Parent View Source**
   ```bash
   # Find and examine parent view
   find odoo/addons -name "*.xml" -exec grep -l "inherit_id_here" {} \;
   ```

2. **Use Alternative XPath**
   ```xml
   <!-- Instead of missing field, use existing field -->
   <xpath expr="//field[@name='existing_field']" position="after">

   <!-- Or use structural elements -->
   <xpath expr="//group[1]" position="after">
   <xpath expr="//notebook" position="inside">
   ```

**Error 2**: `Element '<xpath expr="//field[@name='order_line']/tree/field[@name='name']">' cannot be located in parent view`

**Root Cause**: Using `tree` in XPath chain when Odoo 18 uses `list`

**Solution Steps**:
1. **Update XPath Chain for Odoo 18**
   ```xml
   <!-- ❌ WRONG: Uses deprecated tree in XPath -->
   <xpath expr="//field[@name='order_line']/tree/field[@name='name']" position="after">

   <!-- ✅ CORRECT: Uses list in XPath for Odoo 18 -->
   <xpath expr="//field[@name='order_line']/list/field[@name='name']" position="after">
   ```

2. **Verify Parent View Structure**
   ```bash
   # Check actual structure in parent view
   grep -A 20 "field.*name.*order_line" odoo/addons/sale/views/sale_order_views.xml
   ```

3. **XPath Chain Validation Rules**
   ```xml
   <!-- ✅ CORRECT: Odoo 18 XPath patterns -->
   //field[@name='field_name']/list/field[@name='subfield']
   //field[@name='field_name']/form/group/field[@name='subfield']
   //notebook/page[@name='page_name']/field[@name='field_name']

   <!-- ❌ WRONG: Odoo 17 XPath patterns (will fail in Odoo 18) -->
   //field[@name='field_name']/tree/field[@name='subfield']
   //field[@name='field_name']/tree/button[@name='action']
   ```

#### 📋 XPath Chain Migration Guide for Odoo 18

**CRITICAL**: All XPath chains using `tree` must be updated to `list` in Odoo 18.

##### Common XPath Chain Patterns

| **Context** | **❌ Odoo 17 (Wrong)** | **✅ Odoo 18 (Correct)** |
|-------------|-------------------------|---------------------------|
| **Order Lines** | `//field[@name='order_line']/tree/field[@name='name']` | `//field[@name='order_line']/list/field[@name='name']` |
| **Invoice Lines** | `//field[@name='invoice_line_ids']/tree/field[@name='product_id']` | `//field[@name='invoice_line_ids']/list/field[@name='product_id']` |
| **Move Lines** | `//field[@name='move_ids_without_package']/tree/field[@name='product_id']` | `//field[@name='move_ids_without_package']/list/field[@name='product_id']` |
| **Any One2many** | `//field[@name='line_ids']/tree/field[@name='name']` | `//field[@name='line_ids']/list/field[@name='name']` |
| **Tree Buttons** | `//field[@name='order_line']/tree/button[@name='action']` | `//field[@name='order_line']/list/button[@name='action']` |

##### XPath Chain Validation Commands

```bash
# Find all XPath chains using tree (WRONG in Odoo 18)
grep -r "xpath.*tree.*field" your_module/views/

# Find all XPath chains using list (CORRECT in Odoo 18)
grep -r "xpath.*list.*field" your_module/views/

# Search and replace XPath chains
sed -i 's|/tree/field|/list/field|g' your_module/views/*.xml
sed -i 's|/tree/button|/list/button|g' your_module/views/*.xml
```

##### Real-World Examples

**Sale Order Lines Enhancement**
```xml
<!-- ❌ WRONG: Will fail in Odoo 18 -->
<xpath expr="//field[@name='order_line']/tree/field[@name='name']" position="after">
    <field name="custom_field"/>
</xpath>

<!-- ✅ CORRECT: Works in Odoo 18 -->
<xpath expr="//field[@name='order_line']/list/field[@name='name']" position="after">
    <field name="custom_field"/>
</xpath>
```

**Invoice Lines Modification**
```xml
<!-- ❌ WRONG: Will fail in Odoo 18 -->
<xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='product_id']" position="before">
    <field name="sequence" widget="handle"/>
</xpath>

<!-- ✅ CORRECT: Works in Odoo 18 -->
<xpath expr="//field[@name='invoice_line_ids']/list/field[@name='product_id']" position="before">
    <field name="sequence" widget="handle"/>
</xpath>
```

4. **Add Dependency Check**
   ```xml
   <!-- Only inherit if dependency module is installed -->
   <xpath expr="//field[@name='carrier_id']" position="after"
          invisible="not context.get('delivery_module_installed')">
   ```

#### 📚 Module Dependency Considerations

**When inheriting views that depend on other modules:**

```python
# In __manifest__.py - Add proper dependencies
'depends': [
    'sale',           # Base module
    'delivery',       # If using carrier_id
    'sale_stock',     # If using stock-related fields
    'account',        # If using invoice-related fields
],
```

```xml
<!-- Use conditional inheritance for optional modules -->
<record id="view_inherit_with_delivery" model="ir.ui.view">
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <!-- Only add if delivery module creates this field -->
        <xpath expr="//field[@name='carrier_id']" position="after">
            <field name="custom_delivery_field"
                   invisible="not carrier_id"/>
        </xpath>
    </field>
</record>
```

## Key Points

1. **View Definition**
   - Always use `record` tag with `model='ir.ui.view'`
   - Use meaningful IDs for views (e.g., `view_model_name_form`)
   - Set proper name and model fields

2. **List Views** ⚠️ **CRITICAL**
   - **MANDATORY**: Use `<list>` tag instead of `<tree>`
   - **MANDATORY**: Use "list" in view_mode instead of "tree"
   - Define fields to display in the list view
   - Example:
   ```xml
   <list>
       <field name="name"/>
       <field name="date"/>
       <field name="state"/>
   </list>
   ```

3. **Window Actions**
   - Create using `ir.actions.act_window` model
   - Define view modes (list,form)
   - Set proper name and model
   - Include help message for empty state

4. **Menu Items**
   - Create using `menuitem` tag
   - Link to the corresponding action
   - Set proper parent menu
   - Define sequence for ordering

## Best Practices

1. **Naming Conventions**
   - View IDs: `view_model_name_viewtype`
   - Action IDs: `action_model_name`
   - Menu IDs: `menu_model_name`

2. **View Inheritance**
   - Use `inherit_id` field to extend existing views
   - Example:
   ```xml
   <record id="view_model_name_form_inherit" model="ir.ui.view">
       <field name="name">model.name.form.inherit</field>
       <field name="model">model.name</field>
       <field name="inherit_id" ref="view_model_name_form"/>
       <field name="arch" type="xml">
           <xpath expr="//field[@name='field1']" position="after">
               <field name="new_field"/>
           </xpath>
       </field>
   </record>
   ```

3. **Security**
   - Always define proper access rights
   - Use groups attribute when needed
   - Example:
   ```xml
   <record id="view_model_name_form" model="ir.ui.view" groups="base.group_user">
   ```

4. **View Modes**
   - Common view modes: list, form, kanban, calendar, graph, pivot
   - Define in action: `<field name="view_mode">list,form,kanban</field>`

## Example Complete View Definition

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_employee_form" model="ir.ui.view">
        <field name="name">employee.form</field>
        <field name="model">hr.employee</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="department_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List View -->
    <record id="view_employee_list" model="ir.ui.view">
        <field name="name">employee.list</field>
        <field name="model">hr.employee</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="department_id"/>
                <field name="job_title"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="action_employee" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first employee!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_employee"
        name="Employees"
        action="action_employee"
        parent="hr_employee.menu_hr_root"
        sequence="10"/>
</odoo>
```

## 🔍 XML Validation Checklist

### Before Creating XML Files
- [ ] **Check dependencies**: Ensure all referenced modules are in `depends` list in `__manifest__.py`
- [ ] **Verify external IDs**: Use developer mode or source code to confirm external ID existence
- [ ] **Plan file order**: Organize XML files in logical dependency order
- [ ] **Validate parent views**: Check parent view structure before creating inheritance

### External ID Validation ⚠️ **CRITICAL**
- [ ] **Format check**: All external references use `module_name.xml_id` format
- [ ] **Module verification**: Referenced modules exist and are installed
- [ ] **ID existence**: Referenced IDs actually exist in the target module
- [ ] **Source file verification**: Check actual XML files for ID definitions
- [ ] **Inheritance ID validation**: Verify inherit_id references exist before using

### XPath Inheritance Validation ⚠️ **CRITICAL**
- [ ] **Parent view examination**: Check parent view source code for available fields
- [ ] **XPath target verification**: Ensure XPath expressions reference existing elements
- [ ] **XPath chain validation**: Use `list` instead of `tree` in XPath chains for Odoo 18
- [ ] **Safe target usage**: Use commonly available fields (partner_id, notebook, header)
- [ ] **Dependency awareness**: Know which fields are added by optional modules
- [ ] **Test inheritance**: Validate view inheritance loads without errors

### File Order Validation
- [ ] **Manifest order**: Files listed in correct dependency order in `__manifest__.py`
- [ ] **Within file order**: Views before actions, actions before menus
- [ ] **Security first**: `ir.model.access.csv` listed first in data files

### Common External ID Reference Guide

#### Stock Module References
```xml
<!-- Menus -->
stock.menu_stock_root
stock.menu_stock_config
stock.menu_delivery

<!-- Groups -->
stock.group_stock_user
stock.group_stock_manager
stock.group_adv_location

<!-- Picking Types -->
stock.picking_type_out
stock.picking_type_in
stock.picking_type_internal

<!-- Locations -->
stock.stock_location_stock
stock.stock_location_customers
stock.stock_location_suppliers
```

#### Delivery Module References
```xml
<!-- Views -->
delivery.view_delivery_carrier_form
delivery.view_delivery_carrier_tree

<!-- Products -->
delivery.product_product_delivery
delivery.product_category_deliveries

<!-- Data -->
delivery.delivery_carrier_manual_delivery
```

#### Sales Module References
```xml
<!-- Groups -->
sales_team.group_sale_salesman
sales_team.group_sale_manager
sale.group_sale_user

<!-- Menus -->
sale.sale_menu_root
sale.menu_sale_config
```

#### Base Module References
```xml
<!-- Groups -->
base.group_user
base.group_system
base.group_no_one
base.group_public

<!-- Countries -->
base.us (United States)
base.uk (United Kingdom)
base.fr (France)

<!-- Currencies -->
base.USD
base.EUR
base.GBP
```

## 🚨 Error Prevention Strategies

### Strategy 1: Incremental Development
1. **Start with basic views** (no external references)
2. **Add actions** (reference internal views only)
3. **Add menus** (reference internal actions)
4. **Add inheritance** (reference external IDs last)

### Strategy 2: Validation Testing
```bash
# Test XML syntax
python3 -c "import xml.etree.ElementTree as ET; ET.parse('views/file.xml')"

# Test module loading
odoo-bin -d test_db -i module_name --test-enable --stop-after-init
```

### Strategy 3: Reference Verification
```python
# In Odoo shell, verify external ID exists
self.env.ref('module_name.xml_id')
# Should not raise ValueError
```

## 📋 AI Development Guidelines

### When Requesting XML Creation from AI

#### ✅ Good Prompts
```
Create XML views for model.name with:
- List view with fields: field1, field2
- Form view with proper grouping
- Action referencing the views
- Menu under stock.menu_stock_root
- Use proper external ID format: module_name.xml_id
- Ensure correct file order: views before actions before menus
```

#### ❌ Avoid These Prompts
```
Create views for the model
Add menu somewhere in stock
Make it inherit from delivery views
```

#### 🔧 Specific Instructions to Include
1. **Specify exact external IDs**: "Use stock.menu_stock_root as parent menu"
2. **Request validation**: "Verify all external IDs exist in referenced modules"
3. **Specify file order**: "Create views first, then actions, then menus"
4. **Include dependencies**: "Ensure all referenced modules are in depends list"

Remember to:
- ✅ Always use proper XML formatting
- ✅ Follow Odoo's naming conventions
- ✅ **Verify external ID format: module_name.xml_id**
- ✅ **Check file order in __manifest__.py**
- ✅ **Validate external ID existence before use**
- ✅ Test views in different view modes
- ✅ Consider mobile responsiveness
- ✅ Add proper security groups when needed

## 🔧 Troubleshooting XML Errors

### Error: "External ID not found in the system"

#### Step 1: Identify the Problem
```
Error message: External ID not found in the system: delivery.product_product_delivery_normal
```

#### Step 2: Check External ID Format
```xml
<!-- Check if format is correct -->
<field name="product_id" ref="delivery.product_product_delivery_normal"/>
                              ↑
                              module_name.xml_id format ✓
```

#### Step 3: Verify Module Dependency
```python
# In __manifest__.py, check depends list
'depends': ['delivery', 'sale'],  # ✓ delivery module included
```

#### Step 4: Find Correct External ID
```bash
# Search in delivery module files
grep -r "product_product_delivery" addons/delivery/
# Result: delivery.product_product_delivery (not delivery_normal)
```

#### Step 5: Fix the Reference
```xml
<!-- FIXED -->
<field name="product_id" ref="delivery.product_product_delivery"/>
```

### Error: "Element with xml_id not found"

#### Step 1: Identify the Problem
```
Error: Element with xml_id 'my_module.action_my_model' not found
```

#### Step 2: Check File Order in Manifest
```python
# Check __manifest__.py data order
'data': [
    'views/menus.xml',        # ❌ ERROR: References action not yet defined
    'views/my_model_views.xml',  # Contains the action
],
```

#### Step 3: Fix File Order
```python
# FIXED: Views before menus
'data': [
    'security/ir.model.access.csv',
    'views/my_model_views.xml',  # ✓ Action defined here
    'views/menus.xml',           # ✓ Now can reference the action
],
```

#### Step 4: Check Within-File Order
```xml
<!-- WRONG ORDER -->
<menuitem id="menu_my_model" action="action_my_model"/>  <!-- ❌ References action below -->
<record id="action_my_model" model="ir.actions.act_window">  <!-- Defined after menu -->

<!-- CORRECT ORDER -->
<record id="action_my_model" model="ir.actions.act_window">  <!-- ✓ Define action first -->
<menuitem id="menu_my_model" action="action_my_model"/>      <!-- ✓ Reference after definition -->
```

### Quick Fix Commands

#### Validate XML Syntax
```bash
# Check XML syntax before loading
python3 -c "
import xml.etree.ElementTree as ET
try:
    ET.parse('views/your_file.xml')
    print('✓ XML syntax is valid')
except ET.ParseError as e:
    print(f'❌ XML syntax error: {e}')
"
```

#### Test Module Installation
```bash
# Test module installation in safe environment
odoo-bin -d test_database -i your_module --test-enable --stop-after-init --log-level=debug
```

#### Find External IDs
```bash
# Search for external IDs in Odoo source
find addons/ -name "*.xml" -exec grep -l "id=\"your_id\"" {} \;

# Search for specific pattern
grep -r "menu_stock" addons/stock/
```

## 📚 Additional Resources

### Official Odoo Documentation
- [Odoo XML Views Documentation](https://www.odoo.com/documentation/18.0/developer/reference/backend/views.html)
- [Odoo Data Files](https://www.odoo.com/documentation/18.0/developer/reference/backend/data.html)

### Debugging Tools
- **Developer Mode**: Settings > Activate Developer Mode
- **View Metadata**: Click on view > Edit View > View Metadata
- **External ID Lookup**: Technical > Database Structure > External Identifiers

### Common Module Dependencies
```python
# Most common dependencies for delivery modules
'depends': [
    'base',          # Always required
    'delivery',      # For delivery functionality
    'sale',          # For sales integration
    'stock',         # For stock operations
    'product',       # For product management
],
```

## 🎯 AI Development Guidelines for Odoo 18 XML

### When Requesting XML Code from AI

#### ✅ Good Prompts for Odoo 18 XML
```
Create an Odoo 18 action with:
- Use path field for better URLs (kebab-case)
- Use <chatter/> tag instead of div class="oe_chatter"
- Follow proper external ID format (module_name.xml_id)
- Include proper help text with o_view_nocontent_smiling_face
- Use correct view inheritance patterns
```

#### ❌ Avoid These Patterns in Prompts
```
Use div class="oe_chatter" for chatter
Create action without path field
Use old external ID patterns
Skip help text in actions
```

#### 🔧 Specific Instructions to Include
1. **Specify Odoo 18 compliance**: "Ensure XML follows Odoo 18 patterns"
2. **Request path fields**: "Add path field to actions for better URLs"
3. **Use new chatter**: "Use <chatter/> tag for mail functionality"
4. **Include help text**: "Add proper help text with nocontent styling"

### 📋 Odoo 18 XML Migration Checklist

#### Pre-Migration Audit
- [ ] **Search for old chatter patterns**: Find `<div class="oe_chatter">` and replace with `<chatter/>`
- [ ] **Add path fields to actions**: Update `ir.actions.act_window` records with path fields
- [ ] **Verify external ID format**: Ensure all refs use `module_name.xml_id` format
- [ ] **Check XML file order**: Ensure dependencies exist before use in manifest
- [ ] **Update help text**: Use modern `o_view_nocontent_smiling_face` styling

#### Code Quality Validation
- [ ] All actions have descriptive path fields
- [ ] All form views use `<chatter/>` tag
- [ ] External IDs follow correct format
- [ ] Help text uses modern styling
- [ ] Views inherit properly from parent modules

#### Testing Requirements
- [ ] Test URL generation with path fields
- [ ] Verify chatter functionality works correctly
- [ ] Check all external ID references resolve
- [ ] Validate view inheritance works as expected
- [ ] Test responsive design on mobile/tablet

### 🚨 Common Migration Errors

#### Error 1: Missing Path Field
```xml
<!-- ERROR: Action without path field -->
<record id="my_action" model="ir.actions.act_window">
    <field name="name">My Records</field>
    <field name="res_model">my.model</field>
    <!-- ❌ Missing path field -->
</record>

<!-- FIX: Add path field -->
<record id="my_action" model="ir.actions.act_window">
    <field name="name">My Records</field>
    <field name="res_model">my.model</field>
    <field name="path">my-records</field>  <!-- ✅ Better URL -->
</record>
```

#### Error 2: Old Chatter Pattern
```xml
<!-- ERROR: Old chatter structure -->
<form>
    <sheet>
        <!-- fields -->
    </sheet>
    <div class="oe_chatter">  <!-- ❌ Old pattern -->
        <field name="message_follower_ids" widget="mail_followers"/>
        <field name="activity_ids" widget="mail_activity"/>
        <field name="message_ids" widget="mail_thread"/>
    </div>
</form>

<!-- FIX: Use new chatter tag -->
<form>
    <sheet>
        <!-- fields -->
    </sheet>
    <chatter/>  <!-- ✅ New simplified pattern -->
</form>
```

#### Error 3: Incorrect External ID Format
```xml
<!-- ERROR: Wrong external ID format -->
<field name="inherit_id" ref="view_delivery_carrier_form"/>  <!-- ❌ Missing module -->

<!-- FIX: Include module name -->
<field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>  <!-- ✅ Correct format -->
```

## 🔍 Advanced Odoo 18 XML Patterns

### Complete Action with Path and Modern Help
```xml
<record id="action_postcode_pricelist" model="ir.actions.act_window">
    <field name="name">Postcode Pricelists</field>
    <field name="res_model">delivery.postcode.pricelist</field>
    <field name="path">postcode-pricelists</field>  <!-- ✅ SEO-friendly URL -->
    <field name="view_mode">list,form</field>
    <field name="context">{'default_active': True}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">  <!-- ✅ Modern styling -->
            Create your first postcode pricelist!
        </p>
        <p>
            Define delivery pricing based on postal codes and geographic areas.
        </p>
    </field>
</record>
```

### Modern Form View with Chatter
```xml
<record id="view_postcode_pricelist_form" model="ir.ui.view">
    <field name="name">delivery.postcode.pricelist.form</field>
    <field name="model">delivery.postcode.pricelist</field>
    <field name="arch" type="xml">
        <form string="Postcode Pricelist">
            <sheet>
                <group>
                    <group>
                        <field name="name"/>
                        <field name="postcode_from"/>
                        <field name="postcode_to"/>
                    </group>
                    <group>
                        <field name="country_id"/>
                        <field name="state_id"/>
                        <field name="city"/>  <!-- ✅ New city field -->
                        <field name="price"/>
                    </group>
                </group>
            </sheet>
            <chatter/>  <!-- ✅ Simple chatter implementation -->
        </form>
    </field>
</record>
```

### Proper View Inheritance
```xml
<record id="view_delivery_carrier_form_inherit" model="ir.ui.view">
    <field name="name">delivery.carrier.form.inherit.postcode</field>
    <field name="model">delivery.carrier</field>
    <field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>  <!-- ✅ Correct external ID -->
    <field name="arch" type="xml">
        <field name="fixed_price" position="after">
            <field name="postcode_fixed_price"
                   invisible="delivery_type != 'postcode'"/>  <!-- ✅ Conditional visibility -->
        </field>
    </field>
</record>
```

---

**Guide Status**: ✅ **COMPREHENSIVE ODOO 18 XML COMPLIANCE**
**Focus**: XML view creation, new Odoo 18 features, and common error prevention
**Usage**: Essential reference for Odoo 18 XML development
**Companion**: Use with `odoo_python_development_guidelines.md` for complete Odoo 18 development