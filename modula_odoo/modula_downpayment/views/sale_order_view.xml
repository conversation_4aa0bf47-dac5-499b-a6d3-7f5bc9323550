<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.form.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form" />
        <field name="arch" type="xml">
            <xpath expr="//button[@id='create_invoice_percentage']" position="after">
                <button name="button_create_down_payment"
                    invisible="1"
                    type="object"
                    string="Create Down Payment" />
            </xpath>
            <xpath expr="//button[@id='create_invoice_percentage']" position="attributes">
                <attribute name="context">{}</attribute>
            </xpath>
            <xpath expr="//button[@name='action_view_invoice']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//button[@name='action_view_invoice']" position="after">
                <button name="button_view_down_payment" type="object" class="oe_stat_button"
                    icon="fa-money" invisible="downpayment_count == 0">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="downpayment_count" /> / <field
                                style="color:red" name="remaining_amount"
                                invisible="remaining_amount == 0" />
                            <field name="remaining_amount"
                                invisible="remaining_amount != 0" />
                        </span>
                        <span class="o_stat_text">Receipts</span>
                    </div>
                </button>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='price_subtotal']" position="after">
                <field name="minimum_deposit" string="Dep. $" context="{'compute_deposite_amount': True}" optional="hide"/>
                <field name="minimum_deposit_percent" string="Dep. %" width="7%" optional="show" context="{'compute_deposite_amount': True}" widget="percentage"/>
                <!-- <field name="deposited_amount" sum="Deposited Amount" optional="show"/> -->
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='product_uom_qty']" position="attributes">
                <attribute name="context">{'compute_deposite_amount': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="attributes">
                <attribute name="context">{'compute_deposite_amount': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']/list//field[@name='price_total']" position="attributes">
                <attribute name="context">{'compute_deposite_amount': True}</attribute>
            </xpath>
            <!-- <xpath expr="//field[@name='tax_totals']" position="after">
                <div class="d-flex float-end" colspan="2" groups="base.group_user">
                    <label for="minimum_deposit_total" string="Deposit Amount"/>
                    <div>
                        <field name="minimum_deposit_total" class="oe_inline"/>
                    </div>
                </div>
            </xpath> -->
        </field>
    </record>

    <record id="view_sale_advance_payment_inv_inherit" model="ir.ui.view">
        <field name="name">view.sale.advance.payment.inv.inherit</field>
        <field name="model">sale.advance.payment.inv</field>
        <field name="inherit_id" ref="sale.view_sale_advance_payment_inv" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='advance_payment_method']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>
