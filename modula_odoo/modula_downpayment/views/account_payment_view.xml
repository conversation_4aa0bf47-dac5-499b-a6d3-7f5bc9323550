<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_payment_form_inherit" model="ir.ui.view">
        <field name="name">view.account.payment.form.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='memo']" position="before">
                <field name="sale_order_id"/>
                <field name="is_downpayment" invisible="0" readonly="1"/>
            </xpath>
            <xpath expr="//div[@name='amount_div']" position="after">
                <field name="so_totals" invisible="is_downpayment == False"/>
                <field name="downpayment_total" invisible="is_downpayment == False"/>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="Line Allocations" name="line_allocations" invisible="payment_type == 'outbound'">
                    <field name="line_allocation_ids">
                        <list editable="bottom" create="false">
                            <field name="sale_order_line_id"/>
                            <field name="line_price_total" string="Sale Amount(inc gst)"/>
                            <field name="minimum_deposit_percent" widget="percentage" readonly="1"/>
                            <field name="deposit_percent_allocated" widget="percentage" />
                            <field name="amount"/>
                            <field name="is_release_goods" widget="boolean"/>
                            <field name="date"/>
                        </list>
                    </field>
                    <group>
                        <field name="allocated_amount"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    <record id="view_account_payment_tree_inherit" model="ir.ui.view">
        <field name="name">view.account.payment.list.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="sale_order_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='amount_company_currency_signed']" position="before">
                <field name="remaining_amount" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="view_account_payment_search_inherit" model="ir.ui.view">
        <field name="name">view.account.payment.search.inherit</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='company']" position="after">
                <separator/>
                <filter name="downpayment_filter" string="Down Payment" domain="[('is_downpayment', '=', True)]"/>
                <separator/>
            </xpath>
        </field>
    </record>
    <record id="action_account_downpayment" model="ir.actions.act_window">
        <field name="name">Down Payments</field>
        <field name="res_model">account.payment</field>
        <field name="view_mode">list,kanban,form,graph</field>
        <field name="context">{
            'default_payment_type': 'inbound',
            'default_partner_type': 'customer',
            'search_default_inbound_filter': 1,
            'default_move_journal_types': ('bank', 'cash'),
            'default_is_downpayment': True,
            'search_default_downpayment_filter': 1,
        }</field>
        <field name="view_id" ref="account.view_account_payment_tree"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Register a payment
            </p>
            <p>
                Payments are used to register liquidity movements. You can process those payments by your own means or by using installed facilities.
            </p>
        </field>
    </record>

    <menuitem id="menu_account_downpayment" action="action_account_downpayment" parent="account.menu_finance_receivables" sequence="16"/>
</odoo>
