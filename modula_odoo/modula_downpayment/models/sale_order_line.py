from odoo import api, fields, models


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    payment_allocation_ids = fields.One2many(
        "sale.order.line.payment.allocation",
        "sale_order_line_id",
        string="Payment Allocations",
    )
    
    deposited_amount = fields.Monetary(
        string="Deposited Amount",
        compute="_compute_deposited_amount",
    )

    fully_paid_received = fields.<PERSON><PERSON>an(
        string="Fully Paid Received",
        compute="_compute_deposite_status",
    )
    is_minimum_deposit_met = fields.Boolean(
        string="Is Minimum Deposit Met",
        compute="_compute_deposite_status",
    )

    minimum_deposit = fields.Monetary(
        "Deposit Amount",
        compute="_compute_minimum_deposit",
        inverse="_inverse_minimum_deposit",
        store=True,
        readonly=False,
    )
    minimum_deposit_percent = fields.Float(
        "Deposit %",
    )

    def _inverse_minimum_deposit(self):
        for line in self:
            if self.env.context.get('compute_deposite_amount'):
                if line.price_total:
                    line.minimum_deposit_percent = line.minimum_deposit / line.price_total
                else:
                    line.minimum_deposit_percent = 0.5

    @api.depends("minimum_deposit_percent", "price_total")
    def _compute_minimum_deposit(self):
        for line in self:
            if line.price_total:
                line.minimum_deposit = line.price_total * line.minimum_deposit_percent
            else:
                line.minimum_deposit = line.price_total/2

    @api.onchange("price_subtotal")
    def onchange_price_subtotal(self):
        if self.price_total:
            if self.minimum_deposit_percent and self.minimum_deposit: 
                self.minimum_deposit = self.price_total * self.minimum_deposit_percent
            else:
                self.minimum_deposit = self.price_total * 0.5

    @api.onchange("minimum_deposit")
    def onchange_minimum_deposit(self):
        if self.price_total:
            if self.minimum_deposit/self.price_total > 1:
                self.minimum_deposit_percent = 1
                self.minimum_deposit = self.price_total
            else:
                self.minimum_deposit_percent = self.minimum_deposit/self.price_total
            if self.deposited_amount > 0.0 and self.deposited_amount < self.minimum_deposit:
                return {
                    "warning": {
                        "title": "Deposited Amount Warning",
                        "message": "The deposited amount is less than the minimum deposit.",
                    }
                }
            if self.minimum_deposit_percent < 0.45:
                return {
                    "warning": {
                        "title": "Minimum Deposit Warning",
                        "message": "A minimum deposit of 50% is required.",
                    }
                }

    @api.onchange("minimum_deposit_percent")
    def onchange_minimum_deposit_percent(self):
        if self.minimum_deposit_percent > 1:
            self.minimum_deposit_percent = 1
            self.minimum_deposit = self.price_total
        elif self.minimum_deposit_percent and self.minimum_deposit_percent < 0.45:
                return {
                    "warning": {
                        "title": "Minimum Deposit Warning",
                        "message": "A minimum deposit of 50% is required.",
                    }
                }
        else:
            self.minimum_deposit = self.price_total * self.minimum_deposit_percent
        if self.deposited_amount > 0.0 and self.deposited_amount < self.minimum_deposit:
            return {
                "warning": {
                    "title": "Deposited Amount Warning",
                    "message": "The deposited amount is less than the minimum deposit.",
                }
            }

    @api.onchange("price_total")
    def onchange_price_total(self):
        if self.price_total:
            if self.minimum_deposit_percent and self.minimum_deposit or self.env.context.get('compute_deposite_amount'): 
                self.minimum_deposit = self.price_total * self.minimum_deposit_percent
            else:
                self.minimum_deposit = self.price_total * 0.5


    @api.depends("payment_allocation_ids.amount")
    def _compute_deposited_amount(self):
        for line in self:
            line.deposited_amount = sum(
                line.payment_allocation_ids.mapped("amount")
            )

    @api.depends("deposited_amount", "price_total", "minimum_deposit_percent")
    def _compute_deposite_status(self):
        for line in self:
            fully_paid_received = (
                line.deposited_amount == line.price_total
            )
            minimum_deposit_met = False
            if line.price_total:
                minimum_deposit_met = (
                    line.deposited_amount / line.price_total
                ) >= line.minimum_deposit_percent

            line.fully_paid_received = fully_paid_received
            line.is_minimum_deposit_met = minimum_deposit_met

    def _get_remaining_allocated_amount(self):
        """
        Returns the remaining amount that can be allocated to this sale order line
        """
        self.ensure_one()
        return self.price_total - self.deposited_amount

    def get_available_fully_paid_quantity(self):
        self.ensure_one()
        if self.deposited_amount <= 0.0:
            return 0.0
        
        # Calculate unit price with tax
        amount_per_unit = self.price_unit 
        if self.tax_id:
            amount_per_unit = self.tax_id.compute_all(
                self.price_unit,
                self.order_id.currency_id,
                1.0,
                product=self.product_id,
                partner=self.order_id.partner_id)["total_included"]
        if amount_per_unit <= 0.0:
            return 0.0
        
        # Calculate available fully paid quantity
        available_qty = int(self.deposited_amount / amount_per_unit) - self.qty_delivered
        
        # Return the minimum between available quantity and ordered quantity
        return min(available_qty, self.product_uom_qty)
