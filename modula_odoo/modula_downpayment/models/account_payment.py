# -*- coding: utf-8 -*-
from datetime import date

from odoo import Command, _, api, fields, models
from odoo.exceptions import UserError


class AccountPayment(models.Model):
    _inherit = "account.payment"

    sale_order_id = fields.Many2one("sale.order", string="Sale Order")
    is_downpayment = fields.Boolean(
        string="Is Downpayment",
        default=False,
    )
    remaining_amount = fields.Monetary(
        currency_field="currency_id",
        compute="_compute_remaining_amount",
        string="Remaining Amount",
        store=True,
    )
    so_totals = fields.Monetary(
        string="Total Amount", related="sale_order_id.amount_total"
    )
    downpayment_total = fields.Monetary(
        string="Downpayment Taken", compute="_compute_downpayment_total"
    )
    state = fields.Selection(
        selection_add=[("in_process", "In Payment")],
    )

    line_allocation_ids = fields.One2many(
        "sale.order.line.payment.allocation",
        "payment_id",
        string="Line Allocations",
    )

    allocated_amount = fields.Monetary(
        string="Total Allocated Amount",
        compute="_compute_allocated_amount",
    )

    allocated_release_goods_amount = fields.Monetary(
        string="Total Allocated Release Goods Amount",
        compute="_compute_allocated_release_goods_amount",
    )
    reverse_payment_id = fields.Many2one(
        "account.payment",
        string="Reverse Payment",
    )

    @api.depends("line_allocation_ids.is_release_goods")
    def _compute_allocated_release_goods_amount(self):
        for payment in self:
            payment.allocated_release_goods_amount = sum(
                payment.line_allocation_ids.filtered(
                    lambda x: x.is_release_goods
                ).mapped("amount")
            )

    @api.depends("line_allocation_ids.amount")
    def _compute_allocated_amount(self):
        for payment in self:
            payment.allocated_amount = sum(payment.line_allocation_ids.mapped("amount"))

    @api.depends(
        "sale_order_id.downpayment_ids.amount", "sale_order_id.downpayment_ids.state"
    )
    def _compute_downpayment_total(self):
        for rec in self:
            rec.downpayment_total = sum(
                rec.sale_order_id.downpayment_ids.filtered(
                    lambda x: x.state in ["paid", "in_process"]
                ).mapped("amount_signed")
            )

    # @api.depends("reconciled_invoice_ids.amount_residual_signed")
    def _compute_remaining_amount(self):
        for payment in self:
            paid_amount = 0
            for invoice in payment.reconciled_invoice_ids:
                paid_amount = (
                    invoice.amount_total_signed - invoice.amount_residual_signed
                )
            remaining_amount = payment.amount - paid_amount
            if remaining_amount < 0:
                remaining_amount = 0
            payment.remaining_amount = remaining_amount

    @api.depends("move_id.name", "state")
    def _compute_name(self):
        records_to_update = self.filtered(
            lambda x: x.is_downpayment and (not x.name or "DP" not in x.name)
        )
        for rec in records_to_update:
            rec.name = self.env["ir.sequence"].next_by_code("down.payment.no")
        return super(
            AccountPayment, self.filtered(lambda x: not x.is_downpayment)
        )._compute_name()

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get("memo"):
                temp = ""
                if vals.get("sale_order_id"):
                    temp = self.env["sale.order"].browse(vals.get("sale_order_id")).name
                temp += " " + str(date.today().strftime("%d/%m/%Y"))
                if vals.get("partner_id"):
                    partner_id = self.env["res.partner"].browse(vals.get("partner_id"))
                    if partner_id.name:
                        temp += (
                            " "
                            + self.env["res.partner"]
                            .browse(vals.get("partner_id"))
                            .name
                        )
                vals["memo"] = temp
            self._process_create_line_allocations(vals)
        return super(AccountPayment, self).create(vals_list)

    @api.onchange("payment_type")
    def _onchange_payment_type(self):
        self.with_context(
            skip_create_line_allocations=True
        )._process_create_line_allocations()

    def write(self, vals):
        self.pre_process_update_line_allocation(vals)
        self.with_context(from_write=True)._process_create_line_allocations(vals)
        return super(AccountPayment, self).write(vals)

    def pre_process_update_line_allocation(self, vals):
        if vals.get("sale_order_id"):
            self.line_allocation_ids.unlink()

    def _process_create_line_allocations(self, vals=None):
        if vals:
            if vals.get("payment_type") != "outbound":
                self._create_line_allocations(vals)
            else:
                vals["line_allocation_ids"] = [(5, 0, 0)]
            return vals
        else:
            if self.payment_type != "outbound" and not self.env.context.get(
                "skip_create_line_allocations"
            ):
                self._create_line_allocations(self)
            else:
                self.line_allocation_ids = [(5, 0, 0)]

    def _create_line_allocations(self, vals):
        if vals.get("sale_order_id"):
            sale_order = self.env["sale.order"].browse(vals.get("sale_order_id"))
            not_fully_paid_order_lines = sale_order.order_line.filtered(
                lambda l: l.deposited_amount < l.price_subtotal
            ).sorted(key=lambda x: x.minimum_deposit_percent, reverse=True)
            line_allocations = []
            total_amount = vals.get("amount") or self.amount or 0.0
            amount_to_allocate = total_amount

            # Calculate total remaining amount across all lines
            total_remaining = sum(
                line._get_remaining_allocated_amount()
                for line in not_fully_paid_order_lines
            )
            # First pass: Calculate initial allocations based on percentage
            initial_allocations = {}
            for line in not_fully_paid_order_lines:
                remaining_amount = line._get_remaining_allocated_amount()
                if total_remaining > 0:
                    amount_to_allocate_to_line = line.minimum_deposit
                    initial_allocations[line.id] = min(
                        amount_to_allocate_to_line, remaining_amount
                    )
                else:
                    initial_allocations[line.id] = 0
            # Second pass: Adjust allocations if needed
            total_allocated = sum(initial_allocations.values())
            if total_allocated < total_amount:
                # Recalculate remaining amount to distribute
                remaining_to_distribute = total_amount - total_allocated
                lines_with_remaining = [
                    line
                    for line in not_fully_paid_order_lines
                    if line._get_remaining_allocated_amount()
                    > initial_allocations[line.id]
                ]

                if lines_with_remaining:
                    for line in lines_with_remaining:
                        if remaining_to_distribute > 0:
                            max_additional = (
                                line._get_remaining_allocated_amount()
                                - initial_allocations[line.id]
                            )
                            if remaining_to_distribute > max_additional:
                                initial_allocations[line.id] += max_additional
                                remaining_to_distribute -= max_additional
                            else:
                                initial_allocations[line.id] += remaining_to_distribute
                                remaining_to_distribute = 0
                sale_order.prepayment_amount_allocated = 0.0
            else:
                for line in not_fully_paid_order_lines.sorted(
                    key=lambda x: x.minimum_deposit_percent, reverse=True
                ):
                    possible_allocation = min(
                        line.minimum_deposit, initial_allocations[line.id]
                    )
                    if amount_to_allocate > possible_allocation:
                        initial_allocations[line.id] = possible_allocation
                        amount_to_allocate -= possible_allocation
                    else:
                        initial_allocations[line.id] = amount_to_allocate
                        amount_to_allocate = 0

            # Create final allocations
            for line in not_fully_paid_order_lines:
                if initial_allocations[line.id] > 0:
                    line_allocations.append(
                        (
                            0,
                            0,
                            {
                                "sale_order_line_id": line.id,
                                "minimum_deposit_percent": line.minimum_deposit_percent,
                                "minimum_deposit": line.minimum_deposit,
                                "line_price_total": line.price_subtotal,
                                "is_release_goods": True
                                if line.minimum_deposit_percent == 1.0
                                else False,
                                "amount": initial_allocations[line.id],
                            },
                        )
                    )
            sum_minimum_deposit = sum(sale_order.order_line.mapped("minimum_deposit"))
            sum_downpayment_amount = sum(
                sale_order.downpayment_ids.filtered(
                    lambda x: x.state in ["paid", "in_process"]
                ).mapped("amount_signed")
            )
            sale_order.prepayment_amount_allocated = (
                sum_minimum_deposit - sum_downpayment_amount
            )
            vals["line_allocation_ids"] = line_allocations

    @api.depends("move_id.name", "date")
    def _compute_display_name(self):
        res = super(AccountPayment, self)._compute_display_name()
        for payment in self:
            if payment.name:
                payment.display_name = "({}) {}".format(
                    payment.date.strftime("%d/%m/%Y"), payment.name
                )
        return res

    # @api.constrains("amount", "state", "payment_type")
    # def constraint_amount(self):
    #     for payment in self:
    #         if payment.payment_type == "outbound":
    #             continue
    #         if payment.amount < payment.allocated_release_goods_amount:
    #             raise UserError(
    #                 _(
    #                     "The amount of the payment is less than the allocated locked amount"
    #                 )
    #             )
    #         if payment.state == "draft":
    #             continue
    #         if payment.amount < payment.allocated_amount:
    #             raise UserError(
    #                 _("The amount of the payment is less than the allocated amount")
    #             )

    def _synchronize_to_moves(self, changed_fields):
        # overwrite handle multiple liquidity lines
        if not any(
            field_name in changed_fields
            for field_name in self._get_trigger_fields_to_synchronize()
        ):
            return

        for pay in self:
            pay.move_id.line_ids.unlink()
            liquidity_lines, counterpart_lines, writeoff_lines = pay._seek_for_lines()
            # Make sure to preserve the write-off amount.
            # This allows to create a new payment with custom 'line_ids'.
            write_off_line_vals = []
            if liquidity_lines and counterpart_lines and writeoff_lines:
                write_off_line_vals.append(
                    {
                        "name": writeoff_lines[0].name,
                        "account_id": writeoff_lines[0].account_id.id,
                        "partner_id": writeoff_lines[0].partner_id.id,
                        "currency_id": writeoff_lines[0].currency_id.id,
                        "amount_currency": sum(
                            writeoff_lines.mapped("amount_currency")
                        ),
                        "balance": sum(writeoff_lines.mapped("balance")),
                    }
                )
            line_vals_list = pay._prepare_move_line_default_vals(
                write_off_line_vals=write_off_line_vals
            )
            line_ids_commands = [
                Command.update(liquidity_lines[0].id, line_vals_list[0])
                if liquidity_lines
                else Command.create(line_vals_list[0]),
                Command.update(counterpart_lines.id, line_vals_list[1])
                if counterpart_lines
                else Command.create(line_vals_list[1]),
            ]
            for line in writeoff_lines:
                line_ids_commands.append((2, line.id))
            for extra_line_vals in line_vals_list[2:]:
                line_ids_commands.append((0, 0, extra_line_vals))
            # Update the existing journal items.
            # If dealing with multiple write-off lines, they are dropped and a new one is generated.
            pay.move_id.with_context(skip_invoice_sync=True).write(
                {
                    "name": "/",  # Set the name to '/' to allow it to be changed
                    "date": pay.date,
                    "partner_id": pay.partner_id.id,
                    "currency_id": pay.currency_id.id,
                    "partner_bank_id": pay.partner_bank_id.id,
                    "line_ids": line_ids_commands,
                    "journal_id": pay.journal_id.id,
                }
            )
