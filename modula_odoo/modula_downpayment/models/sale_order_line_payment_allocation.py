from odoo import api, fields, models
from odoo.tools import format_amount

class SaleOrderLinePaymentAllocation(models.Model):
    _name = "sale.order.line.payment.allocation"
    _description = "Sale Order Line Payment Allocation"

    payment_id = fields.Many2one(
        "account.payment", string="Payment", required=True, ondelete="cascade"
    )
    sale_order_line_id = fields.Many2one(
        "sale.order.line", string="Sale Order Line", required=True, ondelete="cascade"
    )
    amount = fields.Monetary(
        string="Allocated Amount",
    )

    line_price_total = fields.Monetary(
        related="sale_order_line_id.price_total",
    )
    currency_id = fields.Many2one(related="payment_id.currency_id", store=True)
    date = fields.Date(
        related="payment_id.date",
        store=True,
    )
    is_release_goods = fields.Boolean(
        string="Release Goods",
        compute="_compute_is_release_goods",
        store=True,
        readonly=False,
    )
    minimum_deposit = fields.Monetary(
        related="sale_order_line_id.minimum_deposit",
    )
    minimum_deposit_percent = fields.Float(
        string="Sale Deposit %",
        related="sale_order_line_id.minimum_deposit_percent",
    )
    deposit_percent_allocated = fields.Float(
        string="Deposit Allocated %",
        compute="_compute_deposit_percent_allocated",
    )

    def _compute_deposit_percent_allocated(self):
        for line in self:
            if line.line_price_total:
                line.deposit_percent_allocated = line.amount / line.line_price_total
            else:
                line.deposit_percent_allocated = 0.0

    @api.depends("minimum_deposit_percent")
    def _compute_is_release_goods(self):
        for line in self:
            if line.minimum_deposit_percent == 1.0:
                line.is_release_goods = True
            else:
                line.is_release_goods = False

    @api.onchange("line_price_total", "minimum_deposit_percent")
    def onchange_minimum_deposit_percent(self):
        if self.line_price_total:
            self.amount = self.line_price_total * self.minimum_deposit_percent

    def write(self, vals):
        if vals.get("amount") and not self.env.context.get("in_allocate_progress"):
            vals = self.with_context(
                in_allocate_progress=True
            ).update_another_line_allocations(vals)

        res = super(SaleOrderLinePaymentAllocation, self).write(vals)
        return res

    def update_another_line_allocations(self, vals):
        """Recalculate allocation amounts for remaining allocation lines."""
        line_allocation_ids = self.payment_id.line_allocation_ids
        total_amount = self.payment_id.amount

        self._reset_unrelease_goods_allocations(line_allocation_ids)

        possible_allocation_amount = self._calculate_initial_allocation(
            vals, total_amount
        )
        remaining_amount_after_allocation = total_amount - possible_allocation_amount

        other_is_release_goods = sum(
            line.amount for line in line_allocation_ids - self if line.is_release_goods
        )
        self_is_release_goods = vals.get("amount", 0) if self.is_release_goods else 0
        total_is_release_goods = other_is_release_goods + self_is_release_goods
        remaining_amount = remaining_amount_after_allocation - total_is_release_goods

        # Allocate remaining amount to other lines
        remaining_amount = self._allocate_remaining_amount(
            line_allocation_ids - self, remaining_amount
        )

        # Add any remaining amount back to current line
        if remaining_amount > 0:
            vals["amount"] = possible_allocation_amount + remaining_amount
        else:
            vals["amount"] = possible_allocation_amount
        return vals

    def _reset_unrelease_goods_allocations(self, line_allocation_ids):
        """Reset amount to 0 for non-locked allocation lines."""
        reset_line_allocation_ids = line_allocation_ids.filtered(
            lambda allocation_line: not allocation_line.is_release_goods
        )
        reset_line_allocation_ids.write({"amount": 0})
        return reset_line_allocation_ids

    def _calculate_initial_allocation(self, vals, total_amount):
        """Calculate initial allocation amount for current line."""
        self_can_allocate_amount = (
            self.sale_order_line_id._get_remaining_allocated_amount()
        )
        possible_allocation_amount = min(
            vals.get("amount", 0), self_can_allocate_amount
        )

        if possible_allocation_amount > 0:
            allocation_amount = min(total_amount, possible_allocation_amount)
            self.with_context(in_allocate_progress=True).write(
                {"amount": allocation_amount}
            )
        else:
            possible_allocation_amount = self_can_allocate_amount

        return possible_allocation_amount

    def _allocate_remaining_amount(self, line_allocation_ids, remaining_amount):
        """Allocate remaining amount to other lines based on their remaining allocated amount."""
        for line in line_allocation_ids:
            if not line.is_release_goods and remaining_amount > 0:
                remaining_allocated_amount = (
                    line.sale_order_line_id._get_remaining_allocated_amount()
                )

                if remaining_amount > 0 and remaining_allocated_amount > 0:
                    allocation_amount = min(
                        remaining_amount, remaining_allocated_amount
                    )
                    line.with_context(in_allocate_progress=True).write(
                        {"amount": allocation_amount}
                    )
                    remaining_amount -= allocation_amount

        return remaining_amount
