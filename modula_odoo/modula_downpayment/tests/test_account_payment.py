# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, Command
from odoo.tests import Form, tagged
from odoo.exceptions import UserError
from freezegun import freeze_time

from .common import ModulaDownpaymentCommon


@tagged('post_install', '-at_install')
class TestAccountPayment(ModulaDownpaymentCommon):

    def test_01_create_downpayment_payment(self):
        """Test creating a downpayment payment."""
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner_a.id,
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'sale_order_id': self.sale_order.id,
            'is_downpayment': True,
        })
        
        self.assertTrue(payment.is_downpayment)
        self.assertEqual(payment.sale_order_id, self.sale_order)
        self.assertEqual(payment.amount, 500.0)
        self.assertEqual(payment.state, 'draft')

    def test_02_downpayment_memo_generation(self):
        """Test automatic memo generation for downpayment payments."""
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner_a.id,
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'sale_order_id': self.sale_order.id,
            'is_downpayment': True,
        })
        
        expected_memo = f"{self.sale_order.name} {fields.Date.today().strftime('%d/%m/%Y')} {self.partner_a.name}"
        self.assertEqual(payment.memo, expected_memo)

    def test_03_downpayment_name_sequence(self):
        """Test downpayment payment name sequence generation."""
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner_a.id,
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'sale_order_id': self.sale_order.id,
            'is_downpayment': True,
        })
        
        # Trigger name computation
        payment._compute_name()
        
        # Check if name contains DP prefix
        self.assertIn('DP', payment.name)
        self.assertIn(str(fields.Date.today().year), payment.name)

    def test_04_compute_remaining_amount(self):
        """Test computation of remaining amount."""
        # Create a payment with some reconciled invoices
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Create and post an invoice
        invoice = self.env['account.move'].create({
            'move_type': 'out_invoice',
            'partner_id': self.partner_a.id,
            'invoice_line_ids': [
                Command.create({
                    'name': 'Test line',
                    'quantity': 1,
                    'price_unit': 800.0,
                })
            ],
        })
        invoice.action_post()
        
        # Reconcile payment with invoice
        payment.action_post()
        (payment + invoice).js_assign_outstanding_line(payment.line_ids[0].id)
        
        # Check remaining amount
        self.assertEqual(payment.remaining_amount, 200.0)

    def test_05_compute_downpayment_total(self):
        """Test computation of downpayment total."""
        # Create multiple downpayments
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0, 'paid')
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0, 'in_process')
        payment3 = self._create_downpayment_payment(self.sale_order, 200.0, 'draft')
        
        # Check downpayment total (should only include paid and in_process)
        self.assertEqual(payment1.downpayment_total, 800.0)  # 500 + 300
        self.assertEqual(payment2.downpayment_total, 800.0)
        self.assertEqual(payment3.downpayment_total, 800.0)

    def test_06_compute_allocated_amount(self):
        """Test computation of allocated amount."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Create line allocations
        allocation1 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': self.sale_order.order_line[0].id,
            'amount': 600.0,
        })
        allocation2 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': self.sale_order.order_line[1].id,
            'amount': 400.0,
        })
        
        self.assertEqual(payment.allocated_amount, 1000.0)

    def test_07_compute_allocated_release_goods_amount(self):
        """Test computation of allocated release goods amount."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Create line allocations with release goods
        allocation1 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': self.sale_order.order_line[0].id,
            'amount': 600.0,
            'is_release_goods': True,
        })
        allocation2 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': self.sale_order.order_line[1].id,
            'amount': 400.0,
            'is_release_goods': False,
        })
        
        self.assertEqual(payment.allocated_release_goods_amount, 600.0)

    def test_08_compute_is_sale_manager(self):
        """Test computation of is_sale_manager field."""
        # Test with admin user
        admin_user = self.env.ref('base.user_admin')
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        with self.with_user(admin_user.id):
            self.assertTrue(payment.is_sale_manager)
        
        # Test with regular user
        regular_user = self.env['res.users'].create({
            'name': 'Test User',
            'login': 'test_user',
            'email': '<EMAIL>',
        })
        
        with self.with_user(regular_user.id):
            self.assertFalse(payment.is_sale_manager)

    def test_09_action_post_context(self):
        """Test action_post with context."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Test that context is properly set
        result = payment.action_post()
        
        # Verify the payment is posted
        self.assertEqual(payment.state, 'posted')

    def test_10_line_allocation_creation_on_write(self):
        """Test line allocation creation when writing payment."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Write to trigger line allocation creation
        payment.write({
            'amount': 1200.0,
        })
        
        # Check if line allocations were created
        self.assertTrue(payment.line_allocation_ids)

    def test_11_pre_process_update_line_allocation(self):
        """Test pre-processing of line allocation updates."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Create some line allocations
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': self.sale_order.order_line[0].id,
            'amount': 500.0,
        })
        
        # Test with sale_order_id change
        result = payment.pre_process_update_line_allocation({'sale_order_id': self.sale_order.id})
        self.assertEqual(len(payment.line_allocation_ids), 0)  # Should be unlinked
        
        # Test with state change to draft
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': self.sale_order.order_line[0].id,
            'amount': 500.0,
        })
        result = payment.pre_process_update_line_allocation({'state': 'draft'})
        self.assertEqual(allocation.amount, 0)  # Should be reset to 0

    def test_12_allow_create_line_allocations(self):
        """Test conditions for allowing line allocation creation."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Test with outbound payment type
        self.assertFalse(payment._allow_create_line_allocations({'payment_type': 'outbound'}))
        
        # Test with draft state
        self.assertFalse(payment._allow_create_line_allocations({'state': 'draft'}))
        
        # Test with in_process state and move_id
        payment.write({'state': 'in_process'})
        self.assertFalse(payment._allow_create_line_allocations({
            'state': 'in_process', 
            'move_id': 1
        }))
        
        # Test normal case
        self.assertTrue(payment._allow_create_line_allocations({}))

    def test_13_create_line_allocations(self):
        """Test creation of line allocations."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Test creation with sale order
        payment._create_line_allocations({'sale_order_id': self.sale_order.id})
        
        # Check if allocations were created
        self.assertTrue(payment.line_allocation_ids)
        
        # Test creation without sale order
        payment2 = self._create_downpayment_payment(self.sale_order, 500.0)
        payment2.sale_order_id = False
        payment2._create_line_allocations({})
        
        # Should not create allocations without sale order
        self.assertEqual(len(payment2.line_allocation_ids), 0)

    def test_14_reverse_payment_relationship(self):
        """Test reverse payment relationship."""
        payment1 = self._create_downpayment_payment(self.sale_order, 1000.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 500.0)
        
        # Set reverse relationship
        payment1.reverse_payment_id = payment2.id
        
        self.assertEqual(payment1.reverse_payment_id, payment2)
        self.assertFalse(payment2.reverse_payment_id)  # Should not be bidirectional

    def test_15_payment_form_creation(self):
        """Test payment creation through form."""
        with Form(self.env['account.payment']) as payment_form:
            payment_form.payment_type = 'inbound'
            payment_form.partner_type = 'customer'
            payment_form.partner_id = self.partner_a
            payment_form.amount = 750.0
            payment_form.journal_id = self.bank_journal
            payment_form.sale_order_id = self.sale_order
            payment_form.is_downpayment = True
            
            payment = payment_form.save()
        
        self.assertTrue(payment.is_downpayment)
        self.assertEqual(payment.sale_order_id, self.sale_order)
        self.assertEqual(payment.amount, 750.0)

    def test_16_payment_memo_with_partner(self):
        """Test memo generation with partner name."""
        partner_with_name = self.env['res.partner'].create({
            'name': 'Test Partner Name',
        })
        
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': partner_with_name.id,
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'sale_order_id': self.sale_order.id,
            'is_downpayment': True,
        })
        
        expected_memo = f"{self.sale_order.name} {fields.Date.today().strftime('%d/%m/%Y')} {partner_with_name.name}"
        self.assertEqual(payment.memo, expected_memo)

    def test_17_payment_memo_without_partner(self):
        """Test memo generation without partner."""
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'sale_order_id': self.sale_order.id,
            'is_downpayment': True,
        })
        
        expected_memo = f"{self.sale_order.name} {fields.Date.today().strftime('%d/%m/%Y')}"
        self.assertEqual(payment.memo, expected_memo)

    def test_18_payment_memo_without_sale_order(self):
        """Test memo generation without sale order."""
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner_a.id,
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'is_downpayment': True,
        })
        
        expected_memo = f" {fields.Date.today().strftime('%d/%m/%Y')} {self.partner_a.name}"
        self.assertEqual(payment.memo, expected_memo)

    @freeze_time('2023-12-01')
    def test_19_payment_name_sequence_with_date(self):
        """Test payment name sequence with frozen date."""
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        # Trigger name computation
        payment._compute_name()
        
        # Check if name contains correct year
        self.assertIn('2023', payment.name)
        self.assertIn('DP', payment.name)

    def test_20_payment_with_different_partners(self):
        """Test payment with different partners."""
        # Test with partner_b
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner_b.id,
            'amount': 500.0,
            'journal_id': self.bank_journal.id,
            'sale_order_id': self.sale_order.id,
            'is_downpayment': True,
        })
        
        self.assertEqual(payment.partner_id, self.partner_b)
        self.assertTrue(payment.is_downpayment) 