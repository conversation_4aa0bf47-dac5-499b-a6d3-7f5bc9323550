# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import odoo.tests
from odoo import Command
from .common import ModulaDownpaymentHttpCommon


@odoo.tests.tagged('post_install', '-at_install')
class TestModulaDownpaymentTour(ModulaDownpaymentHttpCommon):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Ensure we have the necessary data for the tour
        cls.env.ref('base.user_admin').write({
            'company_id': cls.env.company.id,
            'company_ids': [(4, cls.env.company.id)],
        })

    def test_01_modula_downpayment_tour(self):
        """Test the modula downpayment tour."""
        # Start the tour
        self.start_tour("/odoo", 'modula_downpayment_tour', login="admin")

    def test_02_create_downpayment_tour(self):
        """Test creating a downpayment through the UI."""
        # Start the tour for creating downpayment
        self.start_tour("/odoo", 'modula_create_downpayment_tour', login="admin")

    def test_03_allocate_payment_tour(self):
        """Test allocating payment to sale order lines."""
        # Start the tour for payment allocation
        self.start_tour("/odoo", 'modula_allocate_payment_tour', login="admin") 