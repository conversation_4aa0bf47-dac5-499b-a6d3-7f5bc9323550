# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, Command
from odoo.tests import Form, HttpCase, new_test_user
from odoo.addons.sale.tests.common import TestSaleCommon


class ModulaDownpaymentCommon(TestSaleCommon):
    """Base test class for modula_downpayment module tests."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test products
        cls.product_downpayment = cls._create_product(
            name='Downpayment Product',
            lst_price=1000.0,
            standard_price=800.0,
        )
        cls.product_normal = cls._create_product(
            name='Normal Product',
            lst_price=500.0,
            standard_price=400.0,
        )
        
        # Create test sale order
        cls.sale_order = cls.env['sale.order'].create({
            'partner_id': cls.partner.id,
            'order_line': [
                Command.create({
                    'product_id': cls.product_downpayment.id,
                    'product_uom_qty': 1,
                    'price_unit': 1000.0,
                    'minimum_deposit_percent': 0.5,
                }),
                Command.create({
                    'product_id': cls.product_normal.id,
                    'product_uom_qty': 2,
                    'price_unit': 500.0,
                    'minimum_deposit_percent': 0.6,
                }),
            ],
        })
        
        # Create bank journal for payments
        cls.bank_journal = cls.company_data['default_journal_bank']
        
        # Create test payment
        cls.test_payment = cls.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': cls.partner.id,
            'amount': 1000.0,
            'journal_id': cls.bank_journal.id,
            'sale_order_id': cls.sale_order.id,
            'is_downpayment': True,
        })

    @classmethod
    def _create_downpayment_payment(cls, sale_order, amount, state='draft'):
        """Helper method to create downpayment payments."""
        payment = cls.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': sale_order.partner_id.id,
            'amount': amount,
            'journal_id': cls.bank_journal.id,
            'sale_order_id': sale_order.id,
            'is_downpayment': True,
            'state': state,
        })
        return payment

    def assertDownpaymentValues(self, payment, expected_values):
        """Assert downpayment payment values."""
        for field, expected_value in expected_values.items():
            actual_value = payment[field]
            self.assertEqual(actual_value, expected_value, 
                           f"Field {field}: expected {expected_value}, got {actual_value}")

    def assertSaleOrderLineValues(self, line, expected_values):
        """Assert sale order line values."""
        for field, expected_value in expected_values.items():
            actual_value = line[field]
            self.assertEqual(actual_value, expected_value,
                           f"Field {field}: expected {expected_value}, got {actual_value}")


class ModulaDownpaymentHttpCommon(ModulaDownpaymentCommon, HttpCase):
    """Base test class for HTTP/UI tests."""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Additional setup for HTTP tests if needed 