# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, Command
from odoo.tests import Form, tagged
from odoo.exceptions import UserError

from .common import ModulaDownpaymentCommon


@tagged('post_install', '-at_install')
class TestSaleOrder(ModulaDownpaymentCommon):

    def test_01_compute_downpayment_count(self):
        """Test computation of downpayment count."""
        # Initially no downpayments
        self.assertEqual(self.sale_order.downpayment_count, 0)
        
        # Create downpayments
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0)
        
        # Check count
        self.assertEqual(self.sale_order.downpayment_count, 2)

    def test_02_compute_minimum_deposit_total(self):
        """Test computation of minimum deposit total."""
        # Set minimum deposit percentages
        self.sale_order.order_line[0].minimum_deposit_percent = 0.5
        self.sale_order.order_line[1].minimum_deposit_percent = 0.6
        
        # Trigger computation
        self.sale_order._compute_minimum_deposit_total()
        
        expected_total = (1000.0 * 0.5) + (1000.0 * 0.6)  # 500 + 600 = 1100
        self.assertEqual(self.sale_order.minimum_deposit_total, expected_total)

    def test_03_compute_remaining_amount(self):
        """Test computation of remaining amount."""
        # Create downpayments with different states
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0, 'paid')
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0, 'in_process')
        payment3 = self._create_downpayment_payment(self.sale_order, 200.0, 'draft')
        
        # Trigger computation
        self.sale_order._compute_remaining_amount()
        
        # Only paid and in_process payments should be considered
        total_paid = 500.0 + 300.0  # 800
        expected_remaining = self.sale_order.amount_total - total_paid
        self.assertEqual(self.sale_order.remaining_amount, expected_remaining)

    def test_04_compute_remaining_amount_with_reversals(self):
        """Test computation of remaining amount with reversal moves."""
        # Create a downpayment
        payment = self._create_downpayment_payment(self.sale_order, 500.0, 'paid')
        
        # Create a reversal move
        reversal_move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'line_ids': [
                Command.create({
                    'name': 'Reversal line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 0.0,
                    'credit': 500.0,
                }),
            ],
        })
        payment.move_id.reversal_move_ids = reversal_move
        
        # Trigger computation
        self.sale_order._compute_remaining_amount()
        
        # Remaining amount should not include reversed payments
        expected_remaining = self.sale_order.amount_total
        self.assertEqual(self.sale_order.remaining_amount, expected_remaining)

    def test_05_button_create_down_payment(self):
        """Test create down payment button action."""
        action = self.sale_order.button_create_down_payment()
        
        # Check action structure
        self.assertEqual(action['name'], 'Create Down Payment')
        self.assertEqual(action['res_model'], 'account.payment')
        self.assertEqual(action['view_mode'], 'form')
        self.assertEqual(action['target'], 'current')
        
        # Check context
        context = action['context']
        self.assertEqual(context['default_sale_order_id'], self.sale_order.id)
        self.assertEqual(context['default_partner_id'], self.sale_order.partner_id.id)
        self.assertEqual(context['default_payment_type'], 'inbound')
        self.assertEqual(context['default_partner_type'], 'customer')
        self.assertTrue(context['default_is_downpayment'])
        self.assertEqual(context['default_move_journal_types'], ('bank', 'cash'))

    def test_06_button_view_down_payment(self):
        """Test view down payment button action."""
        action = self.sale_order.button_view_down_payment()
        
        # Check action structure
        self.assertEqual(action['name'], 'Create Down Payment')
        self.assertEqual(action['res_model'], 'account.payment')
        self.assertEqual(action['view_mode'], 'list,form')
        self.assertEqual(action['target'], 'current')
        self.assertTrue(action['context']['disable_toolbar'])
        
        # Check domain
        self.assertEqual(action['domain'], [('sale_order_id', '=', self.sale_order.id)])

    def test_07_downpayment_ids_relationship(self):
        """Test downpayment_ids relationship."""
        # Create downpayments
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0)
        
        # Check relationship
        self.assertIn(payment1, self.sale_order.downpayment_ids)
        self.assertIn(payment2, self.sale_order.downpayment_ids)
        self.assertEqual(len(self.sale_order.downpayment_ids), 2)

    def test_08_prepayment_amount_allocated_field(self):
        """Test prepayment_amount_allocated field."""
        # Set prepayment amount
        self.sale_order.prepayment_amount_allocated = 750.0
        
        self.assertEqual(self.sale_order.prepayment_amount_allocated, 750.0)

    def test_09_sale_order_form_with_downpayment(self):
        """Test sale order form with downpayment functionality."""
        with Form(self.env['sale.order']) as order_form:
            order_form.partner_id = self.partner_a
            
            with order_form.order_line.new() as line:
                line.product_id = self.product_downpayment
                line.product_uom_qty = 1
                line.price_unit = 1000.0
                line.minimum_deposit_percent = 0.5
            
            order = order_form.save()
        
        # Check if downpayment fields are accessible
        self.assertEqual(order.minimum_deposit_total, 500.0)
        self.assertEqual(order.downpayment_count, 0)

    def test_10_sale_order_copy_with_downpayments(self):
        """Test copying sale order with downpayments."""
        # Create downpayments for original order
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0)
        
        # Copy the order
        copied_order = self.sale_order.copy()
        
        # Check that downpayments are not copied
        self.assertEqual(copied_order.downpayment_count, 0)
        self.assertEqual(len(copied_order.downpayment_ids), 0)

    def test_11_sale_order_unlink_with_downpayments(self):
        """Test unlinking sale order with downpayments."""
        # Create downpayments
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0)
        
        # Unlink the order
        self.sale_order.unlink()
        
        # Check that payments are also unlinked
        self.assertFalse(payment1.exists())
        self.assertFalse(payment2.exists())

    def test_12_sale_order_confirm_with_downpayments(self):
        """Test confirming sale order with downpayments."""
        # Create downpayments before confirmation
        payment = self._create_downpayment_payment(self.sale_order, 500.0)
        
        # Confirm the order
        self.sale_order.action_confirm()
        
        # Check that downpayments are still linked
        self.assertIn(payment, self.sale_order.downpayment_ids)

    def test_13_sale_order_cancel_with_downpayments(self):
        """Test canceling sale order with downpayments."""
        # Create downpayments
        payment = self._create_downpayment_payment(self.sale_order, 500.0)
        
        # Cancel the order
        self.sale_order.action_cancel()
        
        # Check that downpayments are still linked
        self.assertIn(payment, self.sale_order.downpayment_ids)

    def test_14_sale_order_draft_with_downpayments(self):
        """Test setting sale order to draft with downpayments."""
        # Create downpayments
        payment = self._create_downpayment_payment(self.sale_order, 500.0)
        
        # Set to draft
        self.sale_order.action_draft()
        
        # Check that downpayments are still linked
        self.assertIn(payment, self.sale_order.downpayment_ids)

    def test_15_sale_order_with_multiple_downpayments(self):
        """Test sale order with multiple downpayments."""
        # Create multiple downpayments
        payments = []
        for amount in [200.0, 300.0, 500.0]:
            payment = self._create_downpayment_payment(self.sale_order, amount, 'paid')
            payments.append(payment)
        
        # Check total downpayments
        total_downpayments = sum(payment.amount for payment in payments)
        self.assertEqual(total_downpayments, 1000.0)
        
        # Check remaining amount
        expected_remaining = self.sale_order.amount_total - total_downpayments
        self.assertEqual(self.sale_order.remaining_amount, expected_remaining)

    def test_16_sale_order_downpayment_sequence(self):
        """Test downpayment sequence in sale order."""
        # Create downpayments in sequence
        payment1 = self._create_downpayment_payment(self.sale_order, 300.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 400.0)
        payment3 = self._create_downpayment_payment(self.sale_order, 300.0)
        
        # Check sequence
        self.assertEqual(len(self.sale_order.downpayment_ids), 3)
        self.assertEqual(self.sale_order.downpayment_count, 3)

    def test_17_sale_order_with_zero_downpayments(self):
        """Test sale order with zero downpayments."""
        # Ensure no downpayments
        self.sale_order.downpayment_ids.unlink()
        
        # Check values
        self.assertEqual(self.sale_order.downpayment_count, 0)
        self.assertEqual(self.sale_order.remaining_amount, self.sale_order.amount_total)
        self.assertEqual(self.sale_order.minimum_deposit_total, 1100.0)  # From setup

    def test_18_sale_order_downpayment_partial_payment(self):
        """Test sale order with partial downpayment payments."""
        # Create partial downpayment
        payment = self._create_downpayment_payment(self.sale_order, 500.0, 'in_process')
        
        # Check remaining amount
        expected_remaining = self.sale_order.amount_total - 500.0
        self.assertEqual(self.sale_order.remaining_amount, expected_remaining)

    def test_19_sale_order_downpayment_draft_payment(self):
        """Test sale order with draft downpayment payments."""
        # Create draft downpayment
        payment = self._create_downpayment_payment(self.sale_order, 500.0, 'draft')
        
        # Check remaining amount (draft payments should not be considered)
        self.assertEqual(self.sale_order.remaining_amount, self.sale_order.amount_total)

    def test_20_sale_order_downpayment_cancelled_payment(self):
        """Test sale order with cancelled downpayment payments."""
        # Create cancelled downpayment
        payment = self._create_downpayment_payment(self.sale_order, 500.0, 'cancelled')
        
        # Check remaining amount (cancelled payments should not be considered)
        self.assertEqual(self.sale_order.remaining_amount, self.sale_order.amount_total) 