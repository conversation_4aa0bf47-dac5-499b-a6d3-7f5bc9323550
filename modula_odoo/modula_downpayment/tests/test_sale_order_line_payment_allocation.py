# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, Command
from odoo.tests import Form, tagged
from odoo.exceptions import UserError

from .common import ModulaDownpaymentCommon


@tagged('post_install', '-at_install')
class TestSaleOrderLinePaymentAllocation(ModulaDownpaymentCommon):

    def test_01_create_allocation(self):
        """Test creating a payment allocation."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        self.assertEqual(allocation.payment_id, payment)
        self.assertEqual(allocation.sale_order_line_id, line)
        self.assertEqual(allocation.amount, 500.0)

    def test_02_compute_deposit_percent_allocated(self):
        """Test computation of deposit percent allocated."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 600.0,
        })
        
        # Trigger computation
        allocation._compute_deposit_percent_allocated()
        
        expected_percent = 600.0 / 1000.0
        self.assertEqual(allocation.deposit_percent_allocated, expected_percent)

    def test_03_compute_deposit_percent_allocated_zero_price(self):
        """Test computation of deposit percent allocated with zero price."""
        line = self.sale_order.order_line[0]
        line.price_total = 0.0
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Trigger computation
        allocation._compute_deposit_percent_allocated()
        
        self.assertEqual(allocation.deposit_percent_allocated, 0.0)

    def test_04_compute_is_release_goods_true(self):
        """Test computation of is_release_goods - true case."""
        line = self.sale_order.order_line[0]
        line.minimum_deposit_percent = 1.0
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Trigger computation
        allocation._compute_is_release_goods()
        
        self.assertTrue(allocation.is_release_goods)

    def test_05_compute_is_release_goods_false(self):
        """Test computation of is_release_goods - false case."""
        line = self.sale_order.order_line[0]
        line.minimum_deposit_percent = 0.5
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Trigger computation
        allocation._compute_is_release_goods()
        
        self.assertFalse(allocation.is_release_goods)

    def test_06_onchange_minimum_deposit_percent(self):
        """Test onchange for minimum_deposit_percent."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit_percent = 0.6
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 0.0,
        })
        
        # Trigger onchange
        allocation.onchange_minimum_deposit_percent()
        
        expected_amount = 1000.0 * 0.6
        self.assertEqual(allocation.amount, expected_amount)

    def test_07_write_without_context(self):
        """Test write without allocation progress context."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 300.0,
        })
        
        # Write without context
        allocation.write({'amount': 500.0})
        
        self.assertEqual(allocation.amount, 500.0)

    def test_08_write_with_context(self):
        """Test write with allocation progress context."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 300.0,
        })
        
        # Write with context
        with self.env.context(in_allocate_progress=True):
            allocation.write({'amount': 500.0})
        
        self.assertEqual(allocation.amount, 500.0)

    def test_09_update_another_line_allocations(self):
        """Test updating other line allocations."""
        line1 = self.sale_order.order_line[0]
        line2 = self.sale_order.order_line[1]
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation1 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line1.id,
            'amount': 300.0,
            'is_release_goods': False,
        })
        allocation2 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line2.id,
            'amount': 200.0,
            'is_release_goods': False,
        })
        
        # Update allocation1
        vals = allocation1.update_another_line_allocations({'amount': 500.0})
        
        # Check that vals contains updated amount
        self.assertIn('amount', vals)

    def test_10_reset_unrelease_goods_allocations(self):
        """Test resetting unrelease goods allocations."""
        line1 = self.sale_order.order_line[0]
        line2 = self.sale_order.order_line[1]
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation1 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line1.id,
            'amount': 300.0,
            'is_release_goods': False,
        })
        allocation2 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line2.id,
            'amount': 200.0,
            'is_release_goods': True,
        })
        
        # Reset unrelease goods allocations
        reset_allocations = allocation1._reset_unrelease_goods_allocations(payment.line_allocation_ids)
        
        # Check that unrelease goods allocation was reset
        self.assertEqual(allocation1.amount, 0.0)
        self.assertEqual(allocation2.amount, 200.0)  # Should remain unchanged

    def test_11_calculate_initial_allocation(self):
        """Test calculating initial allocation."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 0.0,
        })
        
        # Calculate initial allocation
        vals = {'amount': 600.0}
        result = allocation._calculate_initial_allocation(vals, 1000.0)
        
        self.assertEqual(result, 600.0)

    def test_12_calculate_initial_allocation_exceeds_remaining(self):
        """Test calculating initial allocation that exceeds remaining amount."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 0.0,
        })
        
        # Try to allocate more than remaining
        vals = {'amount': 1200.0}
        result = allocation._calculate_initial_allocation(vals, 1000.0)
        
        # Should be capped at remaining amount
        self.assertEqual(result, 1000.0)

    def test_13_allocate_remaining_amount(self):
        """Test allocating remaining amount to other lines."""
        line1 = self.sale_order.order_line[0]
        line2 = self.sale_order.order_line[1]
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation1 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line1.id,
            'amount': 0.0,
            'is_release_goods': False,
        })
        allocation2 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line2.id,
            'amount': 0.0,
            'is_release_goods': False,
        })
        
        # Allocate remaining amount
        remaining = allocation1._allocate_remaining_amount(
            payment.line_allocation_ids - allocation1, 500.0
        )
        
        # Check that allocations were made
        self.assertTrue(allocation2.amount > 0 or remaining > 0)

    def test_14_related_fields(self):
        """Test related fields."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit_percent = 0.6
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Check related fields
        self.assertEqual(allocation.line_price_total, 1000.0)
        self.assertEqual(allocation.currency_id, payment.currency_id)
        self.assertEqual(allocation.date, payment.date)
        self.assertEqual(allocation.minimum_deposit, line.minimum_deposit)
        self.assertEqual(allocation.minimum_deposit_percent, 0.6)

    def test_15_allocation_form(self):
        """Test allocation form."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        with Form(self.env['sale.order.line.payment.allocation']) as allocation_form:
            allocation_form.payment_id = payment
            allocation_form.sale_order_line_id = line
            allocation_form.amount = 400.0
            allocation_form.is_release_goods = True
            
            allocation = allocation_form.save()
        
        self.assertEqual(allocation.payment_id, payment)
        self.assertEqual(allocation.sale_order_line_id, line)
        self.assertEqual(allocation.amount, 400.0)
        self.assertTrue(allocation.is_release_goods)

    def test_16_allocation_with_release_goods(self):
        """Test allocation with release goods flag."""
        line = self.sale_order.order_line[0]
        line.minimum_deposit_percent = 1.0  # 100% deposit
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Should automatically set is_release_goods to True
        self.assertTrue(allocation.is_release_goods)

    def test_17_allocation_without_release_goods(self):
        """Test allocation without release goods flag."""
        line = self.sale_order.order_line[0]
        line.minimum_deposit_percent = 0.5  # 50% deposit
        
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Should automatically set is_release_goods to False
        self.assertFalse(allocation.is_release_goods)

    def test_18_allocation_cascade_delete(self):
        """Test cascade delete behavior."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Delete payment
        payment.unlink()
        
        # Allocation should be deleted
        self.assertFalse(allocation.exists())

    def test_19_allocation_cascade_delete_line(self):
        """Test cascade delete when sale order line is deleted."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Delete sale order line
        line.unlink()
        
        # Allocation should be deleted
        self.assertFalse(allocation.exists())

    def test_20_allocation_currency_consistency(self):
        """Test currency consistency in allocations."""
        line = self.sale_order.order_line[0]
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 500.0,
        })
        
        # Check currency consistency
        self.assertEqual(allocation.currency_id, payment.currency_id)
        self.assertEqual(allocation.currency_id, self.sale_order.currency_id) 