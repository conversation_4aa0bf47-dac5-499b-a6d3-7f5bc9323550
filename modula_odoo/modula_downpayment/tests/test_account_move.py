# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, Command
from odoo.tests import Form, tagged
from odoo.exceptions import UserError

from .common import ModulaDownpaymentCommon


@tagged('post_install', '-at_install')
class TestAccountMove(ModulaDownpaymentCommon):

    def test_01_get_last_sequence_domain(self):
        """Test get_last_sequence_domain excludes downpayment sequences."""
        # Create a regular move
        regular_move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'line_ids': [
                Command.create({
                    'name': 'Regular line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Regular line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        # Create a downpayment move
        downpayment_move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': 'DP/2023/00001',  # Downpayment sequence
            'line_ids': [
                Command.create({
                    'name': 'Downpayment line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 500.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Downpayment line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 500.0,
                }),
            ],
        })
        
        # Test sequence domain
        where_string, param = regular_move._get_last_sequence_domain()
        
        # Should exclude downpayment sequences
        self.assertIn("name NOT LIKE 'DP/%'", where_string)

    def test_02_get_last_sequence_domain_relaxed(self):
        """Test get_last_sequence_domain with relaxed parameter."""
        move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'line_ids': [
                Command.create({
                    'name': 'Test line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Test line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        # Test with relaxed=True
        where_string_relaxed, param_relaxed = move._get_last_sequence_domain(relaxed=True)
        
        # Should still exclude downpayment sequences
        self.assertIn("name NOT LIKE 'DP/%'", where_string_relaxed)

    def test_03_sequence_domain_integration(self):
        """Test sequence domain integration with regular sequences."""
        # Create multiple moves with different sequence patterns
        moves = []
        
        # Regular move
        regular_move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': 'MISC/2023/00001',
            'line_ids': [
                Command.create({
                    'name': 'Regular line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Regular line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        moves.append(regular_move)
        
        # Downpayment move
        downpayment_move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': 'DP/2023/00001',
            'line_ids': [
                Command.create({
                    'name': 'Downpayment line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 500.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Downpayment line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 500.0,
                }),
            ],
        })
        moves.append(downpayment_move)
        
        # Another regular move
        regular_move2 = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': 'MISC/2023/00002',
            'line_ids': [
                Command.create({
                    'name': 'Regular line 2',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 750.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Regular line 2',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 750.0,
                }),
            ],
        })
        moves.append(regular_move2)
        
        # Test that sequence domain works for all moves
        for move in moves:
            where_string, param = move._get_last_sequence_domain()
            self.assertIn("name NOT LIKE 'DP/%'", where_string)

    def test_04_sequence_domain_with_different_years(self):
        """Test sequence domain with different years."""
        # Create moves with different years
        move_2022 = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': 'DP/2022/00001',
            'line_ids': [
                Command.create({
                    'name': '2022 line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': '2022 line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        move_2023 = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': 'DP/2023/00001',
            'line_ids': [
                Command.create({
                    'name': '2023 line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': '2023 line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        # Test sequence domain for both
        where_string_2022, param_2022 = move_2022._get_last_sequence_domain()
        where_string_2023, param_2023 = move_2023._get_last_sequence_domain()
        
        # Both should exclude DP sequences
        self.assertIn("name NOT LIKE 'DP/%'", where_string_2022)
        self.assertIn("name NOT LIKE 'DP/%'", where_string_2023)

    def test_05_sequence_domain_with_mixed_patterns(self):
        """Test sequence domain with mixed sequence patterns."""
        # Create moves with various sequence patterns
        patterns = [
            'MISC/2023/00001',
            'DP/2023/00001',
            'INV/2023/00001',
            'DP/2023/00002',
            'BILL/2023/00001',
        ]
        
        moves = []
        for pattern in patterns:
            move = self.env['account.move'].create({
                'move_type': 'entry',
                'date': fields.Date.today(),
                'name': pattern,
                'line_ids': [
                    Command.create({
                        'name': f'Line {pattern}',
                        'account_id': self.company_data['default_account_receivable'].id,
                        'debit': 1000.0,
                        'credit': 0.0,
                    }),
                    Command.create({
                        'name': f'Line {pattern}',
                        'account_id': self.company_data['default_account_revenue'].id,
                        'debit': 0.0,
                        'credit': 1000.0,
                    }),
                ],
            })
            moves.append(move)
        
        # Test sequence domain for all moves
        for move in moves:
            where_string, param = move._get_last_sequence_domain()
            self.assertIn("name NOT LIKE 'DP/%'", where_string)

    def test_06_sequence_domain_performance(self):
        """Test sequence domain performance with many moves."""
        # Create many moves to test performance
        moves = []
        for i in range(10):
            move = self.env['account.move'].create({
                'move_type': 'entry',
                'date': fields.Date.today(),
                'name': f'MISC/2023/{i:05d}',
                'line_ids': [
                    Command.create({
                        'name': f'Line {i}',
                        'account_id': self.company_data['default_account_receivable'].id,
                        'debit': 100.0,
                        'credit': 0.0,
                    }),
                    Command.create({
                        'name': f'Line {i}',
                        'account_id': self.company_data['default_account_revenue'].id,
                        'debit': 0.0,
                        'credit': 100.0,
                    }),
                ],
            })
            moves.append(move)
        
        # Test sequence domain for all moves
        for move in moves:
            where_string, param = move._get_last_sequence_domain()
            self.assertIn("name NOT LIKE 'DP/%'", where_string)

    def test_07_sequence_domain_edge_cases(self):
        """Test sequence domain with edge cases."""
        # Test with None name
        move_none = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': None,
            'line_ids': [
                Command.create({
                    'name': 'None line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'None line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        where_string_none, param_none = move_none._get_last_sequence_domain()
        self.assertIn("name NOT LIKE 'DP/%'", where_string_none)
        
        # Test with empty name
        move_empty = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'name': '',
            'line_ids': [
                Command.create({
                    'name': 'Empty line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Empty line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        where_string_empty, param_empty = move_empty._get_last_sequence_domain()
        self.assertIn("name NOT LIKE 'DP/%'", where_string_empty)

    def test_08_sequence_domain_inheritance(self):
        """Test that sequence domain properly inherits from parent."""
        # Create a move and test inheritance
        move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': fields.Date.today(),
            'line_ids': [
                Command.create({
                    'name': 'Test line',
                    'account_id': self.company_data['default_account_receivable'].id,
                    'debit': 1000.0,
                    'credit': 0.0,
                }),
                Command.create({
                    'name': 'Test line',
                    'account_id': self.company_data['default_account_revenue'].id,
                    'debit': 0.0,
                    'credit': 1000.0,
                }),
            ],
        })
        
        # Test that the method properly extends parent behavior
        where_string, param = move._get_last_sequence_domain()
        
        # Should contain parent's where string plus our addition
        self.assertIn("name NOT LIKE 'DP/%'", where_string)
        
        # Test with relaxed parameter
        where_string_relaxed, param_relaxed = move._get_last_sequence_domain(relaxed=True)
        self.assertIn("name NOT LIKE 'DP/%'", where_string_relaxed) 