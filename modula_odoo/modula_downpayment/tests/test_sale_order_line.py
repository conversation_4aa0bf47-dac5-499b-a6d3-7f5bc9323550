# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, Command
from odoo.tests import Form, tagged
from odoo.exceptions import UserError

from .common import ModulaDownpaymentCommon


@tagged('post_install', '-at_install')
class TestSaleOrderLine(ModulaDownpaymentCommon):

    def test_01_compute_minimum_deposit(self):
        """Test computation of minimum deposit."""
        line = self.sale_order.order_line[0]
        
        # Set minimum deposit percent
        line.minimum_deposit_percent = 0.5
        
        # Trigger computation
        line._compute_minimum_deposit()
        
        expected_deposit = line.price_total * 0.5
        self.assertEqual(line.minimum_deposit, expected_deposit)

    def test_02_compute_minimum_deposit_zero_price(self):
        """Test computation of minimum deposit with zero price."""
        line = self.sale_order.order_line[0]
        line.price_total = 0.0
        
        # Trigger computation
        line._compute_minimum_deposit()
        
        # Should be half of zero
        self.assertEqual(line.minimum_deposit, 0.0)

    def test_03_inverse_minimum_deposit(self):
        """Test inverse computation of minimum deposit."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit = 500.0
        
        # Trigger inverse computation
        with self.env.context(compute_deposite_amount=True):
            line._inverse_minimum_deposit()
        
        expected_percent = 500.0 / 1000.0
        self.assertEqual(line.minimum_deposit_percent, expected_percent)

    def test_04_inverse_minimum_deposit_zero_price(self):
        """Test inverse computation with zero price."""
        line = self.sale_order.order_line[0]
        line.price_total = 0.0
        line.minimum_deposit = 100.0
        
        # Trigger inverse computation
        with self.env.context(compute_deposite_amount=True):
            line._inverse_minimum_deposit()
        
        # Should default to 0.5
        self.assertEqual(line.minimum_deposit_percent, 0.5)

    def test_05_compute_deposited_amount(self):
        """Test computation of deposited amount."""
        line = self.sale_order.order_line[0]
        
        # Create payment allocation
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 300.0,
        })
        
        # Trigger computation
        line._compute_deposited_amount()
        
        self.assertEqual(line.deposited_amount, 300.0)

    def test_06_compute_deposited_amount_multiple_allocations(self):
        """Test computation of deposited amount with multiple allocations."""
        line = self.sale_order.order_line[0]
        
        # Create multiple allocations
        payment1 = self._create_downpayment_payment(self.sale_order, 500.0)
        payment2 = self._create_downpayment_payment(self.sale_order, 300.0)
        
        allocation1 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment1.id,
            'sale_order_line_id': line.id,
            'amount': 200.0,
        })
        allocation2 = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment2.id,
            'sale_order_line_id': line.id,
            'amount': 150.0,
        })
        
        # Trigger computation
        line._compute_deposited_amount()
        
        self.assertEqual(line.deposited_amount, 350.0)

    def test_07_compute_deposite_status_fully_paid(self):
        """Test computation of deposit status - fully paid."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        # Create allocation for full amount
        payment = self._create_downpayment_payment(self.sale_order, 1000.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 1000.0,
        })
        
        # Trigger computation
        line._compute_deposite_status()
        
        self.assertTrue(line.fully_paid_received)
        self.assertTrue(line.is_minimum_deposit_met)

    def test_08_compute_deposite_status_partially_paid(self):
        """Test computation of deposit status - partially paid."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit_percent = 0.5
        
        # Create allocation for partial amount
        payment = self._create_downpayment_payment(self.sale_order, 600.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 600.0,
        })
        
        # Trigger computation
        line._compute_deposite_status()
        
        self.assertFalse(line.fully_paid_received)
        self.assertTrue(line.is_minimum_deposit_met)  # 60% > 50%

    def test_09_compute_deposite_status_below_minimum(self):
        """Test computation of deposit status - below minimum."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit_percent = 0.5
        
        # Create allocation for amount below minimum
        payment = self._create_downpayment_payment(self.sale_order, 300.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 300.0,
        })
        
        # Trigger computation
        line._compute_deposite_status()
        
        self.assertFalse(line.fully_paid_received)
        self.assertFalse(line.is_minimum_deposit_met)  # 30% < 50%

    def test_10_get_remaining_allocated_amount(self):
        """Test getting remaining allocated amount."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        # Create allocation
        payment = self._create_downpayment_payment(self.sale_order, 600.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 600.0,
        })
        
        remaining = line._get_remaining_allocated_amount()
        self.assertEqual(remaining, 400.0)

    def test_11_onchange_price_subtotal(self):
        """Test onchange for price_subtotal."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit_percent = 0.6
        
        # Trigger onchange
        line.onchange_price_subtotal()
        
        expected_deposit = 1000.0 * 0.6
        self.assertEqual(line.minimum_deposit, expected_deposit)

    def test_12_onchange_price_subtotal_default_percent(self):
        """Test onchange for price_subtotal with default percent."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.minimum_deposit_percent = 0.0
        
        # Trigger onchange
        line.onchange_price_subtotal()
        
        expected_deposit = 1000.0 * 0.5  # Default 50%
        self.assertEqual(line.minimum_deposit, expected_deposit)

    def test_13_onchange_minimum_deposit(self):
        """Test onchange for minimum_deposit."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.deposited_amount = 200.0
        
        # Set deposit above 100%
        line.minimum_deposit = 1200.0
        
        # Trigger onchange
        result = line.onchange_minimum_deposit()
        
        # Should cap at 100%
        self.assertEqual(line.minimum_deposit_percent, 1.0)
        self.assertEqual(line.minimum_deposit, 1000.0)

    def test_14_onchange_minimum_deposit_warning(self):
        """Test onchange for minimum_deposit with warning."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        line.deposited_amount = 200.0
        line.minimum_deposit = 300.0  # Less than deposited amount
        
        # Trigger onchange
        result = line.onchange_minimum_deposit()
        
        # Should return warning
        self.assertIn('warning', result)
        self.assertIn('Deposited Amount Warning', result['warning']['title'])

    def test_15_onchange_minimum_deposit_percent(self):
        """Test onchange for minimum_deposit_percent."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        # Set percent above 100%
        line.minimum_deposit_percent = 1.2
        
        # Trigger onchange
        result = line.onchange_minimum_deposit_percent()
        
        # Should cap at 100%
        self.assertEqual(line.minimum_deposit_percent, 1.0)
        self.assertEqual(line.minimum_deposit, 1000.0)

    def test_16_onchange_minimum_deposit_percent_below_minimum(self):
        """Test onchange for minimum_deposit_percent below minimum."""
        line = self.sale_order.order_line[0]
        line.price_total = 1000.0
        
        # Set percent below 45%
        line.minimum_deposit_percent = 0.3
        
        # Trigger onchange
        result = line.onchange_minimum_deposit_percent()
        
        # Should return warning
        self.assertIn('warning', result)
        self.assertIn('Minimum Deposit Warning', result['warning']['title'])

    def test_17_onchange_price_total(self):
        """Test onchange for price_total."""
        line = self.sale_order.order_line[0]
        line.price_total = 800.0
        line.minimum_deposit_percent = 0.6
        
        # Trigger onchange
        line.onchange_price_total()
        
        expected_deposit = 800.0 * 0.6
        self.assertEqual(line.minimum_deposit, expected_deposit)

    def test_18_onchange_price_total_with_context(self):
        """Test onchange for price_total with context."""
        line = self.sale_order.order_line[0]
        line.price_total = 800.0
        line.minimum_deposit_percent = 0.6
        
        # Trigger onchange with context
        with self.env.context(compute_deposite_amount=True):
            line.onchange_price_total()
        
        expected_deposit = 800.0 * 0.6
        self.assertEqual(line.minimum_deposit, expected_deposit)

    def test_19_payment_allocation_ids_relationship(self):
        """Test payment_allocation_ids relationship."""
        line = self.sale_order.order_line[0]
        
        # Create payment allocation
        payment = self._create_downpayment_payment(self.sale_order, 500.0)
        allocation = self.env['sale.order.line.payment.allocation'].create({
            'payment_id': payment.id,
            'sale_order_line_id': line.id,
            'amount': 300.0,
        })
        
        # Check relationship
        self.assertIn(allocation, line.payment_allocation_ids)
        self.assertEqual(len(line.payment_allocation_ids), 1)

    def test_20_sale_order_line_form_with_deposit(self):
        """Test sale order line form with deposit functionality."""
        with Form(self.env['sale.order.line']) as line_form:
            line_form.order_id = self.sale_order
            line_form.product_id = self.product_downpayment
            line_form.product_uom_qty = 1
            line_form.price_unit = 1000.0
            line_form.minimum_deposit_percent = 0.5
            
            line = line_form.save()
        
        # Check deposit computation
        self.assertEqual(line.minimum_deposit, 500.0)
        self.assertEqual(line.deposited_amount, 0.0)
        self.assertFalse(line.fully_paid_received)
        self.assertTrue(line.is_minimum_deposit_met)  # 50% = 50% 