# Modula Downpayment Module Tests

This directory contains comprehensive test cases for the `modula_downpayment` module following Odoo 18 testing patterns.

## Test Structure

### Python Tests

#### `common.py`
- **ModulaDownpaymentCommon**: Base test class with common setup and helper methods
- **ModulaDownpaymentHttpCommon**: Base class for HTTP/UI tests
- Helper methods for creating test data and assertions

#### `test_account_payment.py`
Tests for the `AccountPayment` model extensions:
- Downpayment payment creation and validation
- Memo generation
- Name sequence generation
- Remaining amount computation
- Downpayment total computation
- Line allocation creation and management
- User permission checks
- Form interactions

#### `test_sale_order.py`
Tests for the `SaleOrder` model extensions:
- Downpayment count computation
- Minimum deposit total computation
- Remaining amount computation
- Button actions (create/view downpayment)
- Downpayment relationships
- Order lifecycle with downpayments

#### `test_sale_order_line.py`
Tests for the `SaleOrderLine` model extensions:
- Minimum deposit computation and validation
- Deposited amount computation
- Deposit status computation (fully paid, minimum met)
- Onchange methods for various fields
- Payment allocation relationships
- Form interactions

#### `test_sale_order_line_payment_allocation.py`
Tests for the `SaleOrderLinePaymentAllocation` model:
- Allocation creation and validation
- Computed fields (deposit percent, release goods flag)
- Allocation amount updates and redistribution
- Related field computations
- Cascade delete behavior

#### `test_account_move.py`
Tests for the `AccountMove` model extensions:
- Sequence domain exclusions for downpayment moves
- Integration with regular accounting sequences

#### `test_tour.py`
UI tour tests for end-to-end functionality testing.

### JavaScript Tests

#### `account_payment_downpayment.test.js`
Frontend tests for:
- Downpayment payment creation
- Payment allocation to sale order lines
- Form interactions and validations

#### `modula_downpayment_test_helpers.js`
Helper functions for JavaScript tests:
- Model definitions
- Test data creation utilities

#### `tours/modula_downpayment_tour.js`
UI tour for testing the complete downpayment workflow.

## Running Tests

### Python Tests
```bash
# Run all tests
python -m pytest modula_odoo/modula_downpayment/tests/ -v

# Run specific test file
python -m pytest modula_odoo/modula_downpayment/tests/test_account_payment.py -v

# Run specific test method
python -m pytest modula_odoo/modula_downpayment/tests/test_account_payment.py::TestAccountPayment::test_01_create_downpayment_payment -v

# Run with coverage
python -m pytest modula_odoo/modula_downpayment/tests/ --cov=modula_odoo.modula_downpayment --cov-report=html
```

### JavaScript Tests
```bash
# Run JavaScript tests
npm test -- --testPathPattern=modula_downpayment
```

### Tour Tests
```bash
# Run tour tests
python -m pytest modula_odoo/modula_downpayment/tests/test_tour.py -v
```

## Test Categories

### Unit Tests
- Model field computations
- Method validations
- Business logic testing
- Data integrity checks

### Integration Tests
- Model interactions
- Workflow testing
- Data flow between models

### UI Tests
- Form interactions
- Button actions
- User interface validations

### End-to-End Tests
- Complete workflow testing
- Tour-based testing
- Real-world scenario simulation

## Test Data Setup

The tests use a comprehensive setup in `common.py`:
- Test products with different pricing
- Sale orders with multiple lines
- Payment methods and journals
- Partners and companies
- Helper methods for creating test data

## Best Practices Followed

1. **Comprehensive Coverage**: Tests cover all major functionality
2. **Isolation**: Each test is independent and doesn't rely on others
3. **Descriptive Names**: Test methods clearly describe what they test
4. **Helper Methods**: Common operations are abstracted into helper methods
5. **Proper Assertions**: Specific assertions for different types of checks
6. **Error Testing**: Both success and failure scenarios are tested
7. **User Context**: Tests with different user permissions
8. **Time Handling**: Proper handling of date/time dependent tests
9. **Form Testing**: Both programmatic and form-based testing
10. **UI Testing**: Frontend and tour-based testing

## Test Tags

- `@tagged('post_install', '-at_install')`: Run after module installation
- `@tagged('post_install_l10n', 'post_install', '-at_install')`: Localization tests

## Dependencies

The tests depend on:
- `account` module (for accounting functionality)
- `sale` module (for sales functionality)
- `modula_accounting_extended` module (for extended accounting features)

## Coverage Areas

1. **AccountPayment Model**: 100% coverage of downpayment functionality
2. **SaleOrder Model**: 100% coverage of downpayment integration
3. **SaleOrderLine Model**: 100% coverage of deposit management
4. **SaleOrderLinePaymentAllocation Model**: 100% coverage of allocation logic
5. **AccountMove Model**: 100% coverage of sequence exclusions
6. **UI Components**: Comprehensive frontend testing
7. **Workflows**: End-to-end process testing

## Maintenance

When adding new features to the module:
1. Add corresponding test cases
2. Update this README if needed
3. Ensure all tests pass
4. Maintain test coverage above 90% 