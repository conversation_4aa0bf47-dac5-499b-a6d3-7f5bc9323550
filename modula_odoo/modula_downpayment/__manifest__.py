# -*- coding: utf-8 -*-
{
    "name": "Modula - Downpayment",
    "summary": """
        Improved Downpayment Flow of Odoo
        """,
    "description": """

    """,
    "author": "ITMS Group",
    "website": "http://www.itmsgroup.com.au",
    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    "category": "ITMS",
    "version": "********.0",
    # any module necessary for this one to work correctly
    "depends": [
        "sale",
        "account",
        "account_payment",
        "modula_accounting_extended",
    ],
    # always loaded
    "data": [
        "security/ir.model.access.csv",
        "views/account_payment_view.xml",
        "views/sale_order_view.xml",
        "data/ir_sequence_data.xml",
    ],
    "installable": True,
    "application": True,
    "license": "LGPL-3",
    "pre_init_hook": "pre_init_hook",
}
