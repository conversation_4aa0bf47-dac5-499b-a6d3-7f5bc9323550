/** @odoo-module **/

import { registry } from "@web/core/registry";
import { stepUtils } from "@web_tour/tour_service/tour_utils";

registry.category("web_tour.tours").add("modula_downpayment_tour", {
    url: "/odoo",
    steps: () => [
        stepUtils.showAppsMenuItem(),
        {
            trigger: ".o_app[data-menu-xmlid='sale.sale_menu_root']",
            content: "open sales app",
            run: "click",
        },
        {
            trigger: ".o-kanban-button-new",
            content: "click create sale order",
            run: "click",
        },
        {
            trigger: ".o_field_widget[name=partner_id] input",
            content: "select partner",
            run: "edit Test Partner",
        },
        {
            trigger: ".o_field_widget[name=order_line] .o_list_add",
            content: "add order line",
            run: "click",
        },
        {
            trigger: ".o_field_widget[name=order_line] .o_data_row:last [name=product_id] input",
            content: "select product",
            run: "edit Test Product",
        },
        {
            trigger: ".o_field_widget[name=order_line] .o_data_row:last [name=product_uom_qty] input",
            content: "set quantity",
            run: "edit 1",
        },
        {
            trigger: ".o_field_widget[name=order_line] .o_data_row:last [name=price_unit] input",
            content: "set price",
            run: "edit 1000",
        },
        {
            trigger: ".o_field_widget[name=order_line] .o_data_row:last [name=minimum_deposit_percent] input",
            content: "set minimum deposit percent",
            run: "edit 0.5",
        },
        {
            trigger: "button.o_form_button_save",
            content: "save sale order",
            run: "click",
        },
        {
            trigger: "button[name=button_create_down_payment]",
            content: "create down payment",
            run: "click",
        },
        {
            trigger: ".o_field_widget[name=amount] input",
            content: "set payment amount",
            run: "edit 500",
        },
        {
            trigger: "button.o_form_button_save",
            content: "save payment",
            run: "click",
        },
        {
            trigger: ".o_field_widget[name=is_downpayment] input",
            content: "verify downpayment flag",
            run: "click",
        },
    ],
}); 