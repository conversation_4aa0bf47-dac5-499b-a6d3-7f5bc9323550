/** @odoo-module **/

import { test } from "@odoo/hoot";
import {
    assertSteps,
    click,
    insertText,
    openFormView,
    start,
    startServer,
    step,
} from "@mail/../tests/mail_test_helpers";
import { onRpc } from "@web/../tests/web_test_helpers";

test("Create downpayment payment", async () => {
    const pyEnv = await startServer();
    const partner = pyEnv["res.partner"].create({ name: "Test Partner" });
    const saleOrder = pyEnv["sale.order"].create({
        partner_id: partner.id,
        order_line: [
            [0, 0, {
                product_id: pyEnv["product.product"].create({ name: "Test Product" }).id,
                product_uom_qty: 1,
                price_unit: 1000.0,
            }]
        ],
    });

    await start();

    onRpc("account.payment", "create", () => {
        step("downpayment created");
    });

    await openFormView("account.payment", null, {
        arch: `<form>
            <sheet>
                <group>
                    <field name="payment_type"/>
                    <field name="partner_type"/>
                    <field name="partner_id"/>
                    <field name="amount"/>
                    <field name="sale_order_id"/>
                    <field name="is_downpayment"/>
                </group>
            </sheet>
        </form>`,
    });

    await click('[name="payment_type"] input');
    await click('.o_field_widget[name="payment_type"] .o_dropdown_item:contains("Inbound")');

    await click('[name="partner_type"] input');
    await click('.o_field_widget[name="partner_type"] .o_dropdown_item:contains("Customer")');

    await click('[name="partner_id"] input');
    await click(`.o_field_widget[name="partner_id"] .o_dropdown_item:contains("${partner.name}")`);

    await insertText('[name="amount"] input', "500");

    await click('[name="sale_order_id"] input');
    await click(`.o_field_widget[name="sale_order_id"] .o_dropdown_item:contains("${saleOrder.name}")`);

    await click('[name="is_downpayment"] input');

    await assertSteps(["downpayment created"]);
});

test("Downpayment payment allocation", async () => {
    const pyEnv = await startServer();
    const partner = pyEnv["res.partner"].create({ name: "Test Partner" });
    const saleOrder = pyEnv["sale.order"].create({
        partner_id: partner.id,
        order_line: [
            [0, 0, {
                product_id: pyEnv["product.product"].create({ name: "Test Product" }).id,
                product_uom_qty: 1,
                price_unit: 1000.0,
                minimum_deposit_percent: 0.5,
            }]
        ],
    });
    const payment = pyEnv["account.payment"].create({
        payment_type: "inbound",
        partner_type: "customer",
        partner_id: partner.id,
        amount: 500.0,
        sale_order_id: saleOrder.id,
        is_downpayment: True,
    });

    await start();

    onRpc("sale.order.line.payment.allocation", "create", () => {
        step("allocation created");
    });

    await openFormView("account.payment", payment, {
        arch: `<form>
            <sheet>
                <notebook>
                    <page string="Line Allocations">
                        <field name="line_allocation_ids">
                            <tree editable="bottom">
                                <field name="sale_order_line_id"/>
                                <field name="amount"/>
                                <field name="is_release_goods"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </sheet>
        </form>`,
    });

    await click('a[name="Line Allocations"]');
    await click('.o_field_widget[name="line_allocation_ids"] .o_list_add');

    await insertText('.o_field_widget[name="line_allocation_ids"] .o_data_row:last [name="amount"] input', "300");

    await assertSteps(["allocation created"]);
}); 