/** @odoo-module **/

import { registry } from "@web/core/registry";

export function defineModulaDownpaymentModels() {
    // Define models for testing
    registry.category("models").add("account.payment", {
        fields: {
            payment_type: { type: "selection" },
            partner_type: { type: "selection" },
            partner_id: { type: "many2one" },
            amount: { type: "monetary" },
            sale_order_id: { type: "many2one" },
            is_downpayment: { type: "boolean" },
            line_allocation_ids: { type: "one2many" },
        },
    });

    registry.category("models").add("sale.order", {
        fields: {
            partner_id: { type: "many2one" },
            order_line: { type: "one2many" },
            downpayment_ids: { type: "one2many" },
            downpayment_count: { type: "integer" },
            remaining_amount: { type: "monetary" },
            minimum_deposit_total: { type: "monetary" },
        },
    });

    registry.category("models").add("sale.order.line", {
        fields: {
            product_id: { type: "many2one" },
            product_uom_qty: { type: "float" },
            price_unit: { type: "monetary" },
            price_total: { type: "monetary" },
            minimum_deposit_percent: { type: "float" },
            minimum_deposit: { type: "monetary" },
            deposited_amount: { type: "monetary" },
            fully_paid_received: { type: "boolean" },
            is_minimum_deposit_met: { type: "boolean" },
            payment_allocation_ids: { type: "one2many" },
        },
    });

    registry.category("models").add("sale.order.line.payment.allocation", {
        fields: {
            payment_id: { type: "many2one" },
            sale_order_line_id: { type: "many2one" },
            amount: { type: "monetary" },
            is_release_goods: { type: "boolean" },
            line_price_total: { type: "monetary" },
            minimum_deposit: { type: "monetary" },
            minimum_deposit_percent: { type: "float" },
            deposit_percent_allocated: { type: "float" },
        },
    });
}

export function createTestPartner(env, name = "Test Partner") {
    return env["res.partner"].create({ name });
}

export function createTestProduct(env, name = "Test Product") {
    return env["product.product"].create({ name });
}

export function createTestSaleOrder(env, partner, product) {
    return env["sale.order"].create({
        partner_id: partner.id,
        order_line: [
            [0, 0, {
                product_id: product.id,
                product_uom_qty: 1,
                price_unit: 1000.0,
                minimum_deposit_percent: 0.5,
            }]
        ],
    });
}

export function createTestDownpaymentPayment(env, partner, saleOrder, amount = 500.0) {
    return env["account.payment"].create({
        payment_type: "inbound",
        partner_type: "customer",
        partner_id: partner.id,
        amount: amount,
        sale_order_id: saleOrder.id,
        is_downpayment: true,
    });
} 