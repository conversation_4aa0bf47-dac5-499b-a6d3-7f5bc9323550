<?xml version ="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_inherit" model="ir.ui.view">
		<field name="name">view.move.form.inherit</field>
		<field name="model">account.move</field>
		<field name="inherit_id" ref="account.view_move_form"/>
		<field name="arch" type="xml">
			<button id="account_invoice_payment_btn" position="attributes">
				<attribute name="invisible">(state != 'posted'
                                    or payment_state not in ('not_paid', 'partial', 'in_payment')
                                    or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')
                                    or invoice_has_outstanding or amount_residual == 0)
									</attribute>
			</button>
			<xpath expr="//widget[@name='web_ribbon'][3]" position="attributes">
				<attribute name="title">Partially Paid</attribute>
			</xpath>
            <xpath expr="//div[@name='journal_div']" position="after">
				<label for="inverse_rate" string="Convert Rate"/>
				<div name="journal_div" class="d-flex" >
					<field name="inverse_rate" digits="[12,12]" style="width: 40% !important" readonly="not show_update_rate_btn or not 'Rate not found' in currency_convert_string"/>
					<!-- hide this button based on date -->
					<button
						name="recompute_currency_rate"
						type="object"
						class="btn-link w-auto"
						invisible="not show_update_rate_btn or not inverse_rate"

					>
						<i role="img"
							title="Update now"
							aria-label="Update now"
							invisible="not show_update_rate_btn or not inverse_rate"
							class="fa fa-fw fa-refresh"/>
					</button>
					<field name="show_update_rate_btn" invisible="1"/>
					<field name="currency_converted" invisible="1"/>
				</div>
				<div>
					<field name="currency_convert_string" decoration-danger="1" invisible="not 'Rate not found' in currency_convert_string"/>
				</div>
            </xpath>
            <xpath expr="//div[@name='currency_conversion_div']" position="attributes">
				<attribute name="invisible">1</attribute>
            </xpath>
			<xpath expr="//list//field[@name='name']" position="after">
                <field name="is_subcontract_service" optional="hide"/>
                <button name="edit_service_note" string="Note" type="object" class="btn btn-secondary" invisible="not is_subcontract_service"/>
			</xpath>
            <xpath expr="//list/field[@name='name']" position="attributes">
                <attribute name="invisible">is_subcontract_service</attribute>
            </xpath>
            <!-- <xpath expr="//field[@name='invoice_outstanding_credits_debits_widget']" position="after">
                <field name="extra_outstanding_credits_debits_widget" invisible="1"/>
            </xpath> -->
            <xpath expr="//div[@name='button_box']" position="inside">
				<button name="open_assets" class="oe_stat_button" icon="fa-bars" type="object" invisible="bill_asset_count == 0">
                    <field string="Assets" name="bill_asset_count" widget="statinfo"/>
					<field name="bill_asset_count" invisible="1"/>
				</button>
            </xpath>
			<xpath expr="//group[@id='header_left_group']/div/field" position="attributes">
                <attribute name="context">{'res_partner_search_mode': (context.get('default_move_type', 'entry') in ('out_invoice', 'out_refund', 'out_receipt') and 'customer') or False,'show_address': 1, 'default_is_company': True, 'show_vat': True}</attribute>
                <attribute name="invisible">move_type not in ('out_invoice', 'out_refund', 'out_receipt')</attribute>
                <attribute name="domain">[('company_id', 'in', (False, company_id)), ('customer','=',True)]</attribute>
            </xpath>
			<xpath expr="//group[@id='header_left_group']/div/field" position="after">
                <field name="partner_id" widget="res_partner_many2one" nolabel="1"
                    context="{'res_partner_search_mode': (context.get('default_move_type', 'entry') in ('in_invoice', 'in_refund', 'in_receipt') and 'supplier') or False,'show_address': 1, 'default_is_company': True, 'show_vat': True}"
                    domain="[('company_id', 'in', (False, company_id)), ('supplier','=',True)]"
                    options="{&quot;no_quick_create&quot;: True}"
                    invisible="move_type not in ('in_invoice', 'in_refund', 'in_receipt')"
                    readonly="state != 'draft'"/>
            </xpath>
			<xpath expr="//group[@id='header_left_group']" position="inside">
				<field name="matched" readonly="1"/>
				<field name="approved_for_payment"/>
				<field name="reverse_date" help="If set, the move will be reversed on this date"/>
            </xpath>

			<xpath expr="//field[@name='delivery_date']" position="after">
				<field name="is_account_manager" invisible="1"/>
				<field name="collection_code" string="Collection Code" readonly="not is_account_manager"/>
			</xpath>
			<xpath expr="//page[@name='invoice_tab']//list//field[@name='tax_ids']" position="after">
				<field name="gst_amount" optional="show"/>
			</xpath>
			<xpath expr="//page[@name='invoice_tab']//list//field[@name='product_id']" position="attributes">
				<attribute name="options">{'no_create_edit': True, 'no_quick_create': True}</attribute>
			</xpath>
			<xpath expr="//div[@name='due_date']" position="after">
				<field name="invoice_date_due" invisible="not invoice_payment_term_id"/>
			</xpath>
			<xpath expr="//group[@id='header_right_group']//field[@name='date']" position="attributes">
				<attribute name="groups">base.group_no_one</attribute>
			</xpath>
			<group id="header_right_group" position="inside">
				<xpath expr="//page[@id='other_tab_entry']//field[@name='narration']" position="move"/>
			</group>
			<xpath expr="//group[@id='header_right_group']//field[@name='narration']" position="attributes">
				<attribute name="colspan">2</attribute>
				<attribute name="nolabel">False</attribute>
				<attribute name="string">Note</attribute>
				<attribute name="invisible">move_type != 'entry'</attribute>
			</xpath>
 		</field>
	</record>
    <record id="view_in_invoice_bill_tree_inherit" model="ir.ui.view">
		<field name="name">account.out.invoice.tree.inherit</field>
		<field name="model">account.move</field>
		<field name="inherit_id" ref="account.view_in_invoice_bill_tree"/>
		<field name="arch" type="xml">
            <field name="status_in_payment" position="before">
                <field name="matched" optional="show"/>
                <field name="approved_for_payment" optional="show"/>
            </field>
            <list position="attributes">
                <attribute name="multi_edit">1</attribute>
            </list>
			<button name="action_force_register_payment" position="attributes">
				<attribute name="groups">base.group_no_one</attribute>
			</button>
 		</field>
	</record>

    <record id="view_move_subledger" model="ir.ui.view">
		<field name="name">view.move.subledger</field>
		<field name="model">account.move</field>
		<field name="inherit_id" ref="account.view_move_form"/>
		<field name="mode">primary</field>
		<field name="arch" type="xml">
			<field name="partner_shipping_id" position="before">
				<field name="partner_id" invisible="not journal_id.subledger_required_fields" required="'partner_id' in subledger_required_fields"/>
				<field name="subledger_required_fields" invisible="1"/>
				<!-- <field name="sale_order_count" invisible="0"/> -->
			</field>
			<field name="ref" position="before">
				<field
					name="subledger_sale_id"
					widget="many2one"
					invisible="not journal_id.subledger_required_fields"
					required="'subledger_sale_id' in subledger_required_fields"/>
				<field
					name="subledger_payment_id"
					widget="many2one"
					invisible="not subledger_sale_id"
					required="'subledger_payment_id' in subledger_required_fields"/>
			</field>
			<field name="receipt_number" position="attributes">
				<attribute name="required">
					'receipt_number' in subledger_required_fields
				</attribute>
			</field>
			<button name="action_view_source_sale_orders" position="attributes">
				<attribute name="invisible">sale_order_count == 0</attribute>
				<attribute name="name">action_ledger_view_source_sale_orders</attribute>
			</button>
			<!-- <group id="header_right_group" position="inside">
				<xpath expr="//page[@id='other_tab_entry']//field[@name='narration']" position="move"/>
			</group>
			<xpath expr="//group[@id='header_right_group']//field[@name='narration']" position="attributes">
				<attribute name="colspan">2</attribute>
				<attribute name="nolabel">False</attribute>
				<attribute name="string">Note</attribute>
			</xpath> -->
 		</field>
	</record>
    <record id="view_move_subledger_list" model="ir.ui.view">
		<field name="name">view.move.subledger.list</field>
		<field name="model">account.move</field>
		<field name="inherit_id" ref="account.view_move_tree"/>
		<field name="mode">extension</field>
		<field name="arch" type="xml">
			<field name="name" position="after">
				<field name="narration" string="Note" optional="show"/>
			</field>
 		</field>
	</record>




    <record id="view_invoice_tree_inherit" model="ir.ui.view">
		<field name="name">view.move.tree.inherit</field>
		<field name="model">account.move</field>
		<field name="inherit_id" ref="account.view_invoice_tree"/>
		<field name="arch" type="xml">
            <xpath expr="//field[@name='ref']" position="after">
				<!-- <field name="container_ids" widget="many2many_tags" optional="show"/> -->
            </xpath>
 		</field>
	</record>
    <record id="account_move_line_service_note_form" model="ir.ui.view">
        <field name="name">account.move.line.service.note.form</field>
        <field name="model">account.move.line</field>
        <field name="priority">10000</field>
        <field name="arch" type="xml">
            <form string="Service Note">
				<sheet string="Service Note">
                    <field name="name" />
                </sheet>
            </form>
        </field>
    </record>

	<record id="action_move_sub_ledger" model="ir.actions.act_window">
		<field name="name">Sub Ledger Journals</field>
		<field name="res_model">account.move</field>
		<field name="view_mode">list,kanban,form,activity</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('view_move_subledger_list')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_move_subledger')}),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('account.view_account_move_kanban')}),
		]"/>
		<field name="search_view_id" ref="account.view_account_move_filter"/>
		<field name="context" eval="{'journal_code':'SUB','search_default_posted':1}"></field>
		<field name="domain" eval="[('journal_id.code', '=', 'SUB')]"></field>
	</record>
    <record id="view_account_invoice_filter_inherit" model="ir.ui.view">
        <field name="name">view_account_invoice_filter.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter" />
        <field name="arch" type="xml">
            <filter name="open" position="before">
                <filter name="matched" string="Not Matched" domain="[('matched', '=', False)]"/>
                <filter name="matched" string="Matched" domain="[('matched', '=', True)]"/>
				<separator/>
                <filter name="approved_for_payment" string="Not Approved for Payment" domain="[('approved_for_payment', '=', False)]"/>
                <filter name="approved_for_payment" string="Approved for Payment" domain="[('approved_for_payment', '=', True)]"/>
				<separator/>
            </filter>
        </field>
    </record>
    <record id="view_move_form_from_asset_modify_inherit" model="ir.ui.view">
        <field name="name">view.move.form.from.asset.modify.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form" />
		<field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
			<xpath expr="//sheet" position="inside">
				<footer>
					<button name="action_confirm_asset_modify" string="Confirm &amp; Edit Asset" type="object" class="btn-primary"/>
                    <button string="Cancel"
                            class="btn-secondary"
                            special="cancel"/>
				</footer>
			</xpath>
        </field>
    </record>
	<menuitem
		id="menu_action_move_sub_ledger"
		action="action_move_sub_ledger"
		groups="account.group_account_readonly"
		parent="account.menu_finance_entries"
		sequence="20"/>

    <record id="account.action_move_journal_line" model="ir.actions.act_window">
        <field name="context">{'check_allow_post': True, 'default_move_type': 'entry', 'search_default_posted':1, 'view_no_maturity': True}</field>
    </record>
</odoo>
