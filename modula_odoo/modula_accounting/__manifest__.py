# -*- coding: utf-8 -*-
{
    "name": "Modula - Accounting",
    "version": "********.0",
    "category": "Purchase",
    "description": """
    """,
    "author": "ITMS Group",
    "website": "http://www.itmsgroup.com.au",
    "depends": [
        "base",
        "account",
        "account_reports",
        "currency_rate_live",
        "account_asset",
        "modula_asset_extended",
        "modula_accounting_fiscal_year",
        "modula_sale",
        "modula_payment",
        "account_online_synchronization",
    ],
    "sequence": 0,
    "data": [
        "security/ir.model.access.csv",
        "data/account_journal_data.xml",
        "data/modula_balance_sheet.xml",
        "data/modula_profit_and_loss.xml",
        "views/account_move_views.xml",
        # "views/container_views.xml",
        "views/account_asset_views.xml",
        "views/account_auto_journal_views.xml",
        "views/account_invoice_repot_view.xml",
        "views/res_config_settings_views.xml",
        "views/asset_modify_views.xml",
        "views/account_journal_views.xml",
        "views/account_segment_views.xml",
        "views/account_account_views.xml",
        "views/account_header_views.xml",
        "views/account_account_segment_views.xml",
        "views/account_report_views.xml",
        # 'views/asset_report_dm_202_views.xml',
        # 'reports/account_asset_report_action.xml',
        "data/ir_cron_data.xml",
        "data/dm202_report_data.xml",
        "data/dm205_report_data.xml",
        "data/dm210_report_data.xml",
        "data/dm211_report_data.xml",
        "data/dm212_report_data.xml",
        "data/assets_report.xml",
        "data/modula_trial_balance.xml",
        "data/dm210_menu_data.xml",
        "wizard/account_payment_register_view.xml",
    ],
    "assets": {
        "account_reports.assets_pdf_export": [
            "modula_accounting/static/src/scss/*",
        ],
        "web.report_assets_common": [
            "modula_accounting/static/src/scss/account_pdf_export_template.scss",
        ],
        "web.assets_backend": [
            "modula_accounting/static/src/components/**/*",
            "modula_accounting/static/src/scss/*",
        ],
    },
    "test": [],
    "demo": [],
    "installable": True,
    "application": True,
    "license": "LGPL-3",
}
