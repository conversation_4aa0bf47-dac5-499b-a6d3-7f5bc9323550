# -*- coding: utf-8 -*-
from . import account_move

# from . import container
from . import account_asset
from . import account_auto_journal
from . import account_invoice_report
from . import company
from . import res_config_settings
from . import account_report
from . import bank_rec_widget
from . import account_required_fields
from . import stock_valuation_layer
from . import account_assets_dm202_report
from . import account_assets_dm205_report
from . import account_assets_dm210_report
from . import account_assets_dm211_report
from . import account_assets_dm212_report
from . import account_account
from . import account_report_aged_receivable
from . import account_header
from . import account_segment
from . import account_segment_range
from . import asset_account_mapping
from . import asset_tax_line
from . import account_trial_balance_report
from . import modula_balance_sheet_report

# def register_dynamic_onchange_methods():
#     """Register dynamic onchange methods for account segments."""
#     AccountAccount = models.get('account.account')
#     if AccountAccount:
#         for field_name, method in AccountAccount._get_segment_onchange_methods().items():
#             setattr(AccountAccount, f'_onchange_{field_name}', method)

# api.model('account.account')._patch_method('_get_segment_onchange_methods', register_dynamic_onchange_methods)
