from odoo import _, models
from odoo.tools.float_utils import float_compare, float_is_zero, float_repr


class ModulaAccountBalanceSheetReportHandler(models.AbstractModel):
    _name = "modula.account.balance.sheet.report.handler"
    _inherit = "account.balance.sheet.report.handler"
    _description = "Modula Balance Sheet Report Handler"

    # def _query_values(self, options, prefix_to_match=None, forced_account_id=None):
    #     return super()._query_values(options, prefix_to_match, forced_account_id)

    def _compute_column_percent_comparison_data(
        self, options, value1, value2, green_on_positive=True
    ):
        """Helper to get the additional columns due to the growth comparison feature. When only one comparison is
        requested, an additional column is there to show the percentage of growth based on the compared period.
        :param options:             The report options.
        :param value1:              The value in the current period.
        :param value2:              The value in the compared period.
        :param green_on_positive:   A flag customizing the value with a green color depending if the growth is positive.
        :return:                    The new columns to add to line['columns'].
        """
        if (
            value1 is None
            or value2 is None
            or float_is_zero(value2, precision_rounding=0.1)
        ):
            return {"name": _("n/a"), "mode": "muted"}

        comparison_type = options["column_percent_comparison"]
        if comparison_type == "growth":

            values_diff = value1 - value2
            growth = round(values_diff, 2)

            # In case the comparison is made on a negative figure, the color should be the other
            # way around. For example:
            #                       2018         2017           %
            # Product Sales      1000.00     -1000.00     -200.0%
            #
            # The percentage is negative, which is mathematically correct, but my sales increased
            # => it should be green, not red!
            if float_is_zero(growth, 1):
                return {"name": "0", "mode": "muted"}
            else:
                return {
                    "name": f"{float_repr(growth, 1)}",
                    "mode": "red"
                    if ((values_diff > 0) ^ green_on_positive)
                    else "green",
                }

        elif comparison_type == "budget":
            percentage_value = value1 / value2 * 100
            if float_is_zero(percentage_value, 1):
                # To avoid negative 0
                return {"name": "0.0", "mode": "green"}

            comparison_value = float_compare(value1, value2, 1)
            return {
                "name": f"{float_repr(percentage_value, 1)}",
                "mode": "green"
                if (comparison_value >= 0 and green_on_positive)
                or (comparison_value == -1 and not green_on_positive)
                else "red",
            }

    def _get_custom_display_config(self):
        return {
            "templates": {
                "AccountReportHeader": "modula_accounting.ModulaTrialBalanceHeader",
            },
            "pdf_export": {
                # "pdf_export_main_table_header": "modula_accounting.asset_report_dm202_pdf_export_main_table_header",
                # 'pdf_export_filters': 'account_reports.journal_report_pdf_export_filters',
                # "pdf_export_main": "modula_accounting.asset_report_dm202_pdf_export_main",
                # "pdf_export_main_table_body": "modula_accounting.asset_report_dm202_pdf_export_main_table_body",
            },
        }


class ModulaProfitLossCustomHandler(models.AbstractModel):
    _name = "modula.account.profit.loss.report.handler"
    _inherit = "account.report.custom.handler"
    _description = "Profit And Loss Custom Handler"

    # def _query_values(self, options, prefix_to_match=None, forced_account_id=None):
    #     return super()._query_values(options, prefix_to_match, forced_account_id)

    def _compute_column_percent_comparison_data(
        self, options, value1, value2, green_on_positive=True
    ):
        """Helper to get the additional columns due to the growth comparison feature. When only one comparison is
        requested, an additional column is there to show the percentage of growth based on the compared period.
        :param options:             The report options.
        :param value1:              The value in the current period.
        :param value2:              The value in the compared period.
        :param green_on_positive:   A flag customizing the value with a green color depending if the growth is positive.
        :return:                    The new columns to add to line['columns'].
        """
        if (
            value1 is None
            or value2 is None
            or float_is_zero(value2, precision_rounding=0.1)
        ):
            return {"name": _("n/a"), "mode": "muted"}

        comparison_type = options["column_percent_comparison"]
        if comparison_type == "growth":

            values_diff = value1 - value2
            growth = round(values_diff, 2)

            # In case the comparison is made on a negative figure, the color should be the other
            # way around. For example:
            #                       2018         2017           %
            # Product Sales      1000.00     -1000.00     -200.0%
            #
            # The percentage is negative, which is mathematically correct, but my sales increased
            # => it should be green, not red!
            if float_is_zero(growth, 1):
                return {"name": "0", "mode": "muted"}
            else:
                return {
                    "name": f"{float_repr(growth, 1)}",
                    "mode": "red"
                    if ((values_diff > 0) ^ green_on_positive)
                    else "green",
                }

        elif comparison_type == "budget":
            percentage_value = value1 / value2 * 100
            if float_is_zero(percentage_value, 1):
                # To avoid negative 0
                return {"name": "0", "mode": "green"}

            comparison_value = float_compare(value1, value2, 1)
            return {
                "name": f"{float_repr(percentage_value, 1)}",
                "mode": "green"
                if (comparison_value >= 0 and green_on_positive)
                or (comparison_value == -1 and not green_on_positive)
                else "red",
            }

    def _get_custom_display_config(self):
        return {
            "templates": {
                "AccountReportHeader": "modula_accounting.ModulaTrialBalanceHeader",
            },
            "pdf_export": {
                # "pdf_export_main_table_header": "modula_accounting.asset_report_dm202_pdf_export_main_table_header",
                # 'pdf_export_filters': 'account_reports.journal_report_pdf_export_filters',
                # "pdf_export_main": "modula_accounting.asset_report_dm202_pdf_export_main",
                # "pdf_export_main_table_body": "modula_accounting.asset_report_dm202_pdf_export_main_table_body",
            },
        }
