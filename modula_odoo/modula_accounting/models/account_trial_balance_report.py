# -*- coding: utf-8 -*-

import ast
import base64
import datetime
import io
import json
import logging
import re
from ast import literal_eval
from collections import defaultdict
from functools import cmp_to_key
from itertools import groupby
from wsgiref import headers

import markupsafe
from dateutil.relativedelta import relativedelta
from odoo import _, api, fields, models, osv
from odoo.addons.web.controllers.utils import clean_action
from odoo.exceptions import RedirectWarning, UserError, ValidationError
from odoo.service.model import get_public_method
from odoo.tools import (
    SQL,
    Query,
    date_utils,
    float_compare,
    float_is_zero,
    float_repr,
    get_lang,
    parse_version,
)
from odoo.tools.float_utils import float_compare, float_round
from odoo.tools.misc import (
    DEFAULT_SERVER_DATE_FORMAT,
    file_path,
    format_date,
    formatLang,
    split_every,
    xlsxwriter,
)
from odoo.tools.safe_eval import expr_eval, safe_eval
from PIL import ImageFont

# TRIAL_BALANCE_END_COLUMN_GROUP_KEY = '_trial_balance_end_column_group'
LINE_ID_HIERARCHY_DELIMITER = "|"


class TrialBalanceCustomHandler(models.AbstractModel):
    _inherit = "account.trial.balance.report.handler"
    _name = "account.trial.balance.report.handler.modula"

    def _filter_out_folded_children(self, lines):
        return lines

    def _dynamic_lines_generator(
        self, report, options, all_column_groups_expression_totals, warnings=None
    ):

        lines = super()._dynamic_lines_generator(
            report, options, all_column_groups_expression_totals, warnings=warnings
        )
        lines = [line for (dummy, line) in lines]
        header_level_dict = []
        level = 1
        account_ids = []
        for line in lines:
            # find all account_id in lines to find all account_header
            model, res_id = report._get_model_info_from_id(line["id"])
            if model == "account.account":
                account_ids.append(res_id)

        account_ids = self.env["account.account"].search([("id", "in", account_ids)])
        account_header_dict = {}
        for account in account_ids:
            account_header_dict[account.id] = account.header_account_id.id

        header_ids = account_ids.mapped("header_account_id")
        if not header_ids:
            return [(0, line) for line in lines]
        while header_ids:
            header_level_dict.append(header_ids.ids)
            level += 1
            header_ids = header_ids.mapped("parent_id")
        level = 3
        for line in lines[:-1]:
            line["level"] = level
        while level > 1:
            line_to_process = [line for line in lines if line["level"] == level]
            line_update_parent_id = [
                line for line in lines if line["level"] == level + 1
            ]
            res_lines, id_map_dict = self._group_by_field(
                report, line_to_process, options, level
            )
            # properly insert the res_lines into lines with correct index
            # remove line_to_process from lines
            if level == 3:
                lines = res_lines
            else:
                line_to_add = [line for line in res_lines if line not in lines]
                while line_to_add:
                    line = line_to_add.pop(0)
                    id = line["id"]
                    for child_line in lines:
                        if child_line.get("parent_id") == id:
                            lines.insert(lines.index(child_line), line)
                            break
                for line in line_update_parent_id:
                    line["parent_id"] = id_map_dict.get(
                        line["parent_id"], line["parent_id"]
                    )
                    line["id"] = line.get("parent_id") + "|" + line["id"].split("|")[1]
            level -= 1
        # for line in lines:
        # if line.get("parent_id", False):
        #     line["parent_id"] = line.get("parent_id").split('|')[1] if '|' in line.get("parent_id") else line.get("parent_id")
        # if line.get("id", False):
        # line["id"] = line.get("id").split('|')[1] if '|' in line.get("id") else line.get("id")
        for line in lines:
            line["level"] = line.get("level") ** 2
        options["order_column"] = {"expression_label": "debit", "direction": "DESC"}
        lines = report.sort_lines(lines, options)
        return [(0, line) for line in lines]

    def _custom_line_postprocessor(self, report, options, lines):
        lines = super()._custom_line_postprocessor(report, options, lines)
        # remove all debit and credit from lines and options
        options["columns"] = [
            col
            for col in options["columns"]
            if col["expression_label"] not in ["debit", "credit"]
        ]
        for line in lines:
            line["columns"] = [
                col
                for col in line["columns"]
                if col["expression_label"] not in ["debit", "credit"]
            ]
        return lines

    def _get_custom_display_config(self):
        return {
            "templates": {
                "AccountReportHeader": "modula_accounting.ModulaTrialBalanceHeader",
            },
            "pdf_export": {
                # "pdf_export_main_table_header": "modula_accounting.asset_report_dm202_pdf_export_main_table_header",
                # 'pdf_export_filters': 'account_reports.journal_report_pdf_export_filters',
                # "pdf_export_main": "modula_accounting.asset_report_dm202_pdf_export_main",
                # "pdf_export_main_table_body": "modula_accounting.asset_report_dm202_pdf_export_main_table_body",
            },
        }

    def _group_by_field(
        self,
        report,
        lines,
        options,
        level,
    ):
        """
        this function group the lines by account_header
        """
        if not lines:
            return lines
        id_map_dict = {}
        line_vals_per_grouping_field_id = {}
        account_1 = None
        parent_model = "account.header"
        account_ids = []
        child_header_ids = []
        for line in lines:
            # find all account_id in lines to find all account_header
            model, res_id = report._get_model_info_from_id(line["id"])
            if model == "account.account":
                account_ids.append(res_id)
            if model == "account.header":
                child_header_ids.append(res_id)
        account_ids = self.env["account.account"].search([("id", "in", account_ids)])
        child_header_ids = self.env["account.header"].search(
            [("id", "in", child_header_ids)]
        )
        account_header_dict = {}
        header_header_dict = {}
        for account in account_ids:
            account_header_dict[account.id] = account.header_account_id.id
        for child_header in child_header_ids:
            header_header_dict[child_header.id] = child_header.parent_id.id
        header_ids = []
        header_ids += account_ids.mapped("header_account_id")
        header_ids += child_header_ids.mapped("parent_id")
        if not header_ids:
            return lines
        for line in lines:
            model, res_id = report._get_model_info_from_id(line["id"])
            if model == "account.account":
                parent_id = account_header_dict.get(res_id)
            elif model == "account.header":
                parent_id = header_header_dict.get(res_id)
            else:
                parent_id = None
            # if not parent_id or parent_id is None:
            #     continue
            if account_1 is None:
                account_1 = parent_id
            id_map_dict[line["id"]] = report._build_line_id(
                [(None, parent_model, parent_id), (None, model, res_id)]
            )
            # replace the line['id'] to add the parent id
            line["id"] = report._build_line_id(
                [(None, parent_model, parent_id), (None, model, res_id)]
            )

            is_parent_in_unfolded_lines = any(
                report._get_model_info_from_id(unfolded_line_id)
                == (parent_model, parent_id)
                for unfolded_line_id in options.get("unfolded_lines")
            )
            is_parent_in_folded_lines = any(
                report._get_model_info_from_id(folded_line_id)
                == (parent_model, parent_id)
                for folded_line_id in options.get("folded_lines")
            )
            line_vals_per_grouping_field_id.setdefault(
                parent_id,
                {
                    # We don't assign a name to the line yet, so that we can batch the browsing of the parent objects
                    "id": report._build_line_id([(None, parent_model, parent_id)]),
                    "columns": [],  # Filled later
                    "unfoldable": True,
                    "unfolded": not is_parent_in_folded_lines
                    and (is_parent_in_unfolded_lines or options.get("unfold_all")),
                    "level": level - 1,
                    # "page_break": account_1 != parent_id,
                    # This value is stored here for convenience; it will be removed from the result
                    "group_lines": [],
                },
            )["group_lines"].append(line)

        # Generate the result
        rslt_lines = []
        idx_monetary_columns = [
            idx_col
            for idx_col, col in enumerate(options["columns"])
            if col["figure_type"] == "monetary"
        ]
        parent_recordset = self.env[parent_model].browse(
            line_vals_per_grouping_field_id.keys()
        )

        for parent_field in parent_recordset:
            parent_line_vals = line_vals_per_grouping_field_id[parent_field.id]
            model = parent_field
            if model:
                parent_line_name = (
                    f"{model.name}({len(parent_line_vals.get('group_lines', []))})"
                )
                parent_line_vals["name"] = parent_line_name
            else:
                parent_line_vals["name"] = f"{parent_field.code} {parent_field.name}"

            rslt_lines.append(parent_line_vals)

            group_totals = {column_index: 0 for column_index in idx_monetary_columns}
            group_lines = report._regroup_lines_by_name_prefix(
                options,
                parent_line_vals.pop("group_lines"),
                "_report_expand_unfoldable_line_assets_report_prefix_group",
                parent_line_vals["level"],
                parent_line_dict_id=parent_line_vals["id"],
            )

            for parent_subline in group_lines:
                # Add this line to the group totals
                for column_index in idx_monetary_columns:
                    group_totals[column_index] += parent_subline["columns"][
                        column_index
                    ].get("no_format", 0)

                # Setup the parent and add the line to the result
                parent_subline["parent_id"] = parent_line_vals["id"]
                rslt_lines.append(parent_subline)

            # Add totals (columns) to the parent line
            for column_index in range(len(options["columns"])):
                parent_line_vals["columns"].append(
                    report._build_column_dict(
                        group_totals.get(column_index, ""),
                        options["columns"][column_index],
                        options=options,
                    )
                )
        return rslt_lines, id_map_dict


class TrialBalanceCustomHandler2(models.AbstractModel):
    _inherit = "account.trial.balance.report.handler"
    _name = "account.trial.balance.report.handler.modula2"

    def _custom_line_postprocessor(self, report, options, lines):
        lines = super()._custom_line_postprocessor(report, options, lines)
        # remove all debit and credit from lines and options
        options["columns"] = [
            col
            for col in options["columns"]
            if col["expression_label"] not in ["debit", "credit"]
        ]
        for line in lines:
            line["columns"] = [
                col
                for col in line["columns"]
                if col["expression_label"] not in ["debit", "credit"]
            ]
        return lines

    def _custom_options_initializer(self, report, options, previous_options):
        super()._custom_options_initializer(report, options, previous_options)
        options["column_headers"][0][1]["name"] = "Movement"

    def _get_custom_display_config(self):
        return {
            "templates": {
                "AccountReportHeader": "modula_accounting.ModulaTrialBalanceHeader",
            },
            "pdf_export": {
                # "pdf_export_main_table_header": "modula_accounting.asset_report_dm202_pdf_export_main_table_header",
                # 'pdf_export_filters': 'account_reports.journal_report_pdf_export_filters',
                # "pdf_export_main": "modula_accounting.asset_report_dm202_pdf_export_main",
                # "pdf_export_main_table_body": "modula_accounting.asset_report_dm202_pdf_export_main_table_body",
            },
        }
