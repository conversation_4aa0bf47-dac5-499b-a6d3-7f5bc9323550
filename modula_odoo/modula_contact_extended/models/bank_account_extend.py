# -*- coding: utf-8 -*-
from odoo import _, api, fields, models, tools
from odoo.exceptions import UserError, ValidationError


class ResPartnerBankExtended(models.Model):
    _name = "res.partner.bank"
    _inherit = "res.partner.bank"
    _description = "Res Partner Bank Extended"

    aba_status = fields.Selection(
        [
            ("no", "Don't use for ABA"),
            ("to", "Allow ABA payments to this Account (partner bank accounts"),
            ("from", "Account for making ABA payments (your bank accout)"),
        ],
        string="ABA Payments",
    )
    bsb = fields.Char("BSB")
    account_title = fields.Char("Account / Payee Name")

    fic = fields.Char("Financial Institution Code")
    aba_user_spec = fields.Char("Supplying User Name")
    aba_user_number = fields.Char("Identification Number (APCA)")
    aba_self_balancing = fields.Boolean("Include Self Balancing Transaction")
    customer_deposits = fields.Boolean("Customer Deposits", default=False)
