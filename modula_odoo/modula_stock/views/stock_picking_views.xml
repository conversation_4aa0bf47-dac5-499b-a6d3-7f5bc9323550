<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_picking_form_inherit_add_payment_details" model="ir.ui.view">
        <field name="name">view.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <!-- Hide original Validate buttons -->
            <xpath expr="//button[@name='button_validate'][1]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='button_validate'][2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <!-- Add new Validate button with employee selection -->
            <xpath expr="//button[@name='button_validate'][1]" position="after">
                <button type="object"
                        string="Validate"
                        name="action_validate_with_employee_selection"
                        class="oe_highlight"
                        data-hotkey="v"
                        groups="stock.group_stock_user" 
                        invisible="state in ('draft', 'waiting_payment','confirmed', 'done', 'cancel')"/>
            </xpath>

            <xpath expr="//button[@name='button_validate'][2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@name='button_validate'][2]" position="after">
                <button type="object"
                        string="Validate"
                        name="action_validate_with_employee_selection"
                        class="o_btn_validate"
                        groups="stock.group_stock_user" 
                        data-hotkey="v"
                        invisible="state in ('waiting', 'assigned', 'done', 'cancel')"/>
            </xpath>
            <field name="backorder_id" position="after">
                <field name="required_payment_before_delivery" invisible="1"/>
                <field name="has_been_paid_before_delivery" invisible="1"/>
            </field>
            <xpath expr="//field[@name='origin']" position="after">
                <field name="employee_id" readonly="1"/>
                <field name="is_sale_manager" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="readonly">not is_sale_manager or state in ('done', 'cancel')</attribute>
            </xpath>
            <!-- <xpath expr="//field[@name='partner_id']" position="after">
                <field name="delivery_address" invisible="picking_type_code != 'outgoing'"/>
            </xpath> -->
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="widget">res_partner_many2one</attribute>
                <attribute name="context">{'res_partner_search_mode': 'customer', 'show_address': 1, 'show_vat': True}</attribute>
            </xpath>
            <page name='note' position="after">
                <page string="Delivery Checklist" name="checklist" invisible="not is_checklist_mandatory">
                    <div class="o_field_custom checklist_form_custom" id="checklist_form">
                        <field name="checklist_input_html"/>
                    </div>
                </page>
			</page>
        </field>
    </record>

    <record id="stock.stock_split_picking" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="stock.action_scrap" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="stock.action_toggle_is_locked" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="stock.action_unreserve_picking" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="quality_control.stock_picking_action_quality_alert" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="quality_control.stock_picking_action_quality_check_on_demand" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="stock.action_validate_picking" model="ir.actions.server">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="stock.action_lead_mass_mail" model="ir.actions.act_window">
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
</odoo>
