# -*- coding: utf-8 -*-

import re
from collections import defaultdict

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.tools.float_utils import float_is_zero


class StockPicking(models.Model):
    _inherit = "stock.picking"

    required_payment_before_delivery = fields.Boolean(
        string="Required Payment Before Delivery",
        compute="_compute_required_payment_before_delivery",
    )
    has_been_paid_before_delivery = fields.<PERSON><PERSON>an(
        string="Has Been Paid Before Delivery",
        compute="_compute_has_been_paid_before_delivery",
    )
    checklist_input_html = fields.Html(related="sale_id.checklist_input_html")
    is_checklist_mandatory = fields.Boolean(
        string="Is Checklist Mandatory",
        related="sale_id.is_checklist_mandatory",
    )
    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
        tracking=True,
    )
    state = fields.Selection(
        selection=[
            ("draft", "Draft"),
            ("waiting", "Waiting Another Operation"),
            ("confirmed", "Waiting"),
            ("waiting_payment", "Waiting Payment"),
            ("assigned", "Ready"),
            ("done", "Done"),
            ("cancel", "Cancelled"),
        ],
    )
    is_sale_manager = fields.Boolean(
        string="Is Sale Manager",
        compute="_compute_is_sale_manager",
    )

    def _compute_is_sale_manager(self):
        for picking in self:
            admin_user = self.env.ref("base.user_admin")
            picking.is_sale_manager = (
                self.env.user == admin_user
                or self.env.user.has_group("sales_team.group_sale_manager")
            )

    @api.depends(
        "move_type",
        "move_ids.state",
        "move_ids.picking_id",
        "sale_id.order_line.fully_paid_received",
    )
    def _compute_state(self):
        super(StockPicking, self)._compute_state()
        self._compute_required_payment_before_delivery()
        self._compute_has_been_paid_before_delivery()
        for picking in self:
            if picking.picking_type_code != "outgoing" or not picking.sale_id:
                continue
            if (
                picking.required_payment_before_delivery
                and not picking.has_been_paid_before_delivery
            ):
                if picking.state == "assigned":
                    picking.state = "waiting_payment"
            elif picking.state == "waiting_payment":
                picking.state = "waiting"

    def _compute_required_payment_before_delivery(self):
        for picking in self:
            if picking.sale_id and picking.picking_type_code == "outgoing":
                payment_term = picking.sale_id.payment_term_id
                if payment_term.payment_required_before_delivery:
                    picking.required_payment_before_delivery = True
                else:
                    picking.required_payment_before_delivery = False
            else:
                picking.required_payment_before_delivery = False

    def _compute_has_been_paid_before_delivery(self):
        for picking in self:
            if picking.required_payment_before_delivery:
                if picking.sale_id and picking._validate_payment_before_delivery():
                    picking.has_been_paid_before_delivery = True
                else:
                    picking.has_been_paid_before_delivery = False
            else:
                picking.has_been_paid_before_delivery = False

    def _validate_payment_before_delivery(self):
        """Validate if payment requirements are met before delivery."""
        if not self.sale_id:
            return False
        
        if self.sale_id.is_fully_paid:
            return True
        
        for move in self.move_ids:
            sale_line = move.sale_line_id
            if not sale_line:
                continue
            
            if sale_line.fully_paid_received:
                continue
            
            if move.quantity <= 0:
                continue
            
            if move.quantity > sale_line.get_available_fully_paid_quantity():
                return False
        
        return True

    def button_validate(self):
        if (
            self.required_payment_before_delivery
            and not self.has_been_paid_before_delivery
        ):
            unpaid_products = []
            paid_possible = False

            for move in self.move_ids:
                sale_line = move.sale_line_id
                if move.quantity <= 0 or not sale_line:
                    continue

                if sale_line.fully_paid_received:
                    paid_possible = True
                else:
                    product_name = (
                        sale_line.product_id.display_name or sale_line.product_id.name
                    )
                    unpaid_products.append("- %s" % product_name)

            if paid_possible and unpaid_products:
                if len(unpaid_products) == 1:
                    product_info = _("Unpaid product:\n%s") % unpaid_products[0]
                else:
                    product_info = _("Unpaid products:\n%s") % "\n".join(
                        unpaid_products
                    )

                raise UserError(
                    _(
                        "Payment requirement is not met.\n%s\n\n"
                        ". Please update 'Done Quantities'."
                    )
                    % product_info
                )

            elif unpaid_products and not paid_possible:
                raise UserError(
                    _(
                        "Payment requirement is not met. Delivery Order cannot be validated until fully paid."
                    )
                )

        return super(StockPicking, self).button_validate()

    def get_sequence_next(self, picking):
        sequence = ""
        if picking.name:
            # Tìm tất cả pickings có prefix là self.name + '/' (backorder)
            original_name = "/".join(picking.name.split("/")[:-1])
            if len(picking.name.split("/")) == 3:
                original_name = picking.name
            pickings = self.env["stock.picking"].search(
                [
                    ("name", "like", f"{original_name}/%"),
                ]
            )

            max_suffix = 0
            pattern = re.compile(re.escape(original_name) + r"/(\d+)$")

            for picking in pickings:
                match = pattern.match(picking.name)
                if match:
                    suffix_num = int(match.group(1))
                    max_suffix = max(max_suffix, suffix_num)

            sequence = f"{original_name}/{(max_suffix + 1):02d}"

        return sequence

    # module stock_picking_batch có inherit hàm này, nếu có cài stock_picking_batch cần check lại
    # module quality_control có inherit hàm này, nếu có gỡ cài quality_control cần check lại
    # Override
    def _create_backorder(self, backorder_moves=None):
        """This method is called when the user chose to create a backorder. It will create a new
        picking, the backorder, and move the stock.moves that are not `done` or `cancel` into it.
        """
        self = self.with_context(erase_employee_id=True)
        backorders = self.env["stock.picking"]
        bo_to_assign = self.env["stock.picking"]
        for picking in self:
            if backorder_moves:
                moves_to_backorder = backorder_moves.filtered(
                    lambda m: m.picking_id == picking
                )
            else:
                moves_to_backorder = picking.move_ids.filtered(
                    lambda x: x.state not in ("done", "cancel")
                )
            moves_to_backorder._recompute_state()
            if moves_to_backorder:
                backorder_picking = picking.copy(
                    {
                        "name": self.get_sequence_next(picking),
                        "move_ids": [],
                        "move_line_ids": [],
                        "backorder_id": picking.id,
                    }
                )
                moves_to_backorder.write(
                    {"picking_id": backorder_picking.id, "picked": False}
                )
                moves_to_backorder.move_line_ids.package_level_id.write(
                    {"picking_id": backorder_picking.id}
                )
                moves_to_backorder.mapped("move_line_ids").write(
                    {"picking_id": backorder_picking.id}
                )
                backorders |= backorder_picking
                backorder_picking.user_id = False
                picking.message_post(
                    body=_(
                        "The backorder %s has been created.",
                        backorder_picking._get_html_link(),
                    )
                )
                if backorder_picking.picking_type_id.reservation_method == "at_confirm":
                    bo_to_assign |= backorder_picking
        if bo_to_assign:
            bo_to_assign.action_assign()
        if self.env.context.get("skip_check"):
            return backorders
        for backorder in backorders:
            # Do not link the QC of move lines with quantity of 0 in backorder.
            backorder.move_line_ids.filtered(
                lambda ml: not float_is_zero(
                    ml.quantity, precision_rounding=ml.product_uom_id.rounding
                )
            ).check_ids.picking_id = backorder
            backorder.backorder_id.check_ids.filtered(
                lambda qc: qc.quality_state == "none"
            ).sudo().unlink()
            backorder.move_ids._create_quality_checks()
        return backorders
