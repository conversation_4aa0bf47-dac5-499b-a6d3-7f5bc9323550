# -*- coding: utf-8 -*-

from collections import defaultdict

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError


class StockPicking(models.Model):
    _inherit = "stock.picking"

    required_payment_before_delivery = fields.<PERSON><PERSON>an(
        string="Required Payment Before Delivery",
        compute="_compute_required_payment_before_delivery",
    )
    has_been_paid_before_delivery = fields.<PERSON><PERSON><PERSON>(
        string="Has Been Paid Before Delivery",
        compute="_compute_has_been_paid_before_delivery",
    )
    checklist_input_html = fields.Html(related="sale_id.checklist_input_html")
    is_checklist_mandatory = fields.<PERSON><PERSON>an(
        string="Is Checklist Mandatory",
        related="sale_id.is_checklist_mandatory",
    )
    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
        tracking=True,
    )
    state = fields.Selection(
        selection=[
            ("draft", "Draft"),
            ("waiting", "Waiting Another Operation"),
            ("confirmed", "Waiting"),
            ("waiting_payment", "Waiting Payment"),
            ("assigned", "Ready"),
            ("done", "Done"),
            ("cancel", "Cancelled"),
        ],
    )
    is_sale_manager = fields.<PERSON><PERSON><PERSON>(
        string="Is Sale Manager",
        compute="_compute_is_sale_manager",
    )

    def _compute_is_sale_manager(self):
        for picking in self:
            admin_user = self.env.ref("base.user_admin")
            picking.is_sale_manager = (
                self.env.user == admin_user
                or self.env.user.has_group("sales_team.group_sale_manager")
            )

    @api.depends(
        "move_type",
        "move_ids.state",
        "move_ids.picking_id",
        "sale_id.order_line.fully_paid_received",
    )
    def _compute_state(self):
        super(StockPicking, self)._compute_state()
        self._compute_required_payment_before_delivery()
        self._compute_has_been_paid_before_delivery()
        for picking in self:
            if picking.picking_type_code != "outgoing" or not picking.sale_id:
                continue
            if (
                picking.required_payment_before_delivery
                and not picking.has_been_paid_before_delivery
            ):
                if picking.state == "assigned":
                    picking.state = "waiting_payment"
            elif picking.state == "waiting_payment":
                picking.state = "waiting"

    def _compute_required_payment_before_delivery(self):
        for picking in self:
            if picking.sale_id and picking.picking_type_code == "outgoing":
                payment_term = picking.sale_id.payment_term_id
                if payment_term.payment_required_before_delivery:
                    picking.required_payment_before_delivery = True
                else:
                    picking.required_payment_before_delivery = False
            else:
                picking.required_payment_before_delivery = False

    def _compute_has_been_paid_before_delivery(self):
        for picking in self:
            if picking.required_payment_before_delivery:
                if picking.sale_id and picking._validate_payment_before_delivery():
                    picking.has_been_paid_before_delivery = True
                else:
                    picking.has_been_paid_before_delivery = False
            else:
                picking.has_been_paid_before_delivery = False

    def _validate_payment_before_delivery(self):
        """Validate if payment requirements are met before delivery."""
        if not self.sale_id:
            return False
        
        if self.sale_id.is_fully_paid:
            return True
        
        for move in self.move_ids:
            sale_line = move.sale_line_id
            if not sale_line:
                continue
            
            if sale_line.fully_paid_received:
                continue
            
            if move.quantity <= 0:
                continue
            
            if move.quantity > sale_line.get_available_fully_paid_quantity():
                return False
        
        return True

    def button_validate(self):
        if (
            self.required_payment_before_delivery
            and not self.has_been_paid_before_delivery
        ):
            unpaid_products = []
            paid_possible = False

            for move in self.move_ids:
                sale_line = move.sale_line_id
                if move.quantity <= 0 or not sale_line:
                    continue

                if sale_line.fully_paid_received:
                    paid_possible = True
                else:
                    product_name = (
                        sale_line.product_id.display_name or sale_line.product_id.name
                    )
                    unpaid_products.append("- %s" % product_name)

            if paid_possible and unpaid_products:
                if len(unpaid_products) == 1:
                    product_info = _("Unpaid product:\n%s") % unpaid_products[0]
                else:
                    product_info = _("Unpaid products:\n%s") % "\n".join(
                        unpaid_products
                    )

                raise UserError(
                    _(
                        "Payment requirement is not met.\n%s\n\n"
                        ". Please update 'Done Quantities'."
                    )
                    % product_info
                )

            elif unpaid_products and not paid_possible:
                raise UserError(
                    _(
                        "Payment requirement is not met. Delivery Order cannot be validated until fully paid."
                    )
                )

        return super(StockPicking, self).button_validate()
