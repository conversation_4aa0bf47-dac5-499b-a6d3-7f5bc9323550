<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Sale Order Form View -->
    <record id="view_order_form_inherit_multi_delivery" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.multi.delivery</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Add Multi-Delivery button in header -->
            <xpath expr="//div[@name='so_button_below_order_lines']" position="inside">
                <button name="action_open_multi_delivery_wizard"
                        string="Multiple Deliveries"
                        type="object"
                        class="btn-primary"
                        invisible="not can_use_multi_delivery or state not in ('draft', 'sent')"
                        help="Configure multiple delivery charges based on product routing"/>
                <button name="action_disable_multi_delivery"
                        string="Disable Multi-Delivery"
                        type="object"
                        class="btn-secondary"
                        invisible="not multi_delivery_enabled"
                        confirm="This will remove all delivery groups and revert to single delivery. Continue?"
                        help="Disable multiple delivery charges"/>
            </xpath>

            <!-- Add Multi-Delivery fields in a safe location -->
            <xpath expr="//field[@name='partner_shipping_id']" position="after">
                <field name="multi_delivery_enabled" invisible="1"/>
                <field name="can_use_multi_delivery" invisible="1"/>
                <field name="has_mto_products" invisible="1"/>
                <field name="has_stock_products" invisible="1"/>

                <!-- <div class="alert alert-info" role="alert"
                     invisible="not multi_delivery_enabled">
                    <strong>Multiple Delivery Charges Enabled</strong><br/>
                    This order uses multiple delivery charges based on product routing.
                    <button name="action_view_delivery_groups"
                            string="View Delivery Groups"
                            type="object"
                            class="btn btn-sm btn-secondary ms-2"/>
                </div>

                <div class="alert alert-warning" role="alert"
                     invisible="not can_use_multi_delivery or multi_delivery_enabled or state not in ('draft', 'sent')">
                    <strong>Multiple Delivery Available</strong><br/>
                    This order contains both MTO and stock products. You can configure separate delivery charges.
                </div> -->
            </xpath>

            <!-- Add delivery group information in notebook -->
            <xpath expr="//notebook" position="inside">
                <page string="Delivery Groups" name="delivery_groups"
                      invisible="not multi_delivery_enabled">
                    <field name="delivery_group_ids">
                        <list>
                            <field name="sequence" widget="handle"/>
                            <field name="name"/>
                            <field name="group_type"/>
                            <field name="carrier_id"/>
                            <field name="delivery_price"/>
                            <field name="total_weight"/>
                            <field name="state"/>
                            <button name="calculate_delivery_rate"
                                    string="Recalculate"
                                    type="object"
                                    icon="fa-refresh"
                                    context="{'recalculate_delivery_rate': True}"
                                    invisible="state != 'draft'"/>
                        </list>
                        <form>
                            <group>
                                <field name="group_type"/>
                                <field name="carrier_id"/>
                                <field name="delivery_price"/>
                                <field name="state"/>
                            </group>
                            <field name="order_line_ids">
                                <list>
                                    <field name="product_id"/>
                                    <field name="name"/>
                                    <field name="product_uom_qty"/>
                                    <field name="price_unit"/>
                                    <field name="price_subtotal"/>
                                </list>
                            </field>
                        </form>
                    </field>
                </page>
            </xpath>

            <!-- Enhance order lines to show delivery group info -->
            <xpath expr="//field[@name='order_line']/list/field[@name='name']" position="after">
                <field name="delivery_group_type" optional="hide"/>
                <field name="delivery_group_id" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- Note: Sale order line form inheritance removed due to non-existent external ID -->
    <!-- The delivery group information is available in the delivery groups tab instead -->

    <!-- Smart button for delivery groups -->
    <!-- <record id="view_order_form_delivery_groups_button" model="ir.ui.view">
        <field name="name">sale.order.form.delivery.groups.button</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_delivery_groups"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-truck"
                        invisible="not multi_delivery_enabled">
                    <field name="delivery_group_ids" widget="statinfo" string="Delivery Groups"/>
                </button>
            </xpath>
        </field>
    </record> -->
</odoo>
