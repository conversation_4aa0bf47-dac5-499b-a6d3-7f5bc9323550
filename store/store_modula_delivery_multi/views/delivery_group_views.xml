<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Delivery Group Form View -->
    <record id="view_delivery_group_form" model="ir.ui.view">
        <field name="name">delivery.group.form</field>
        <field name="model">delivery.group</field>
        <field name="arch" type="xml">
            <form string="Delivery Group">
                <header>
                    <button name="calculate_delivery_rate" string="Calculate Rate" 
                            type="object" class="btn-primary"
                            invisible="state != 'draft'"/>
                    <button name="action_confirm" string="Confirm" 
                            type="object" class="btn-primary"
                            invisible="state != 'draft'"/>
                    <button name="action_cancel" string="Cancel" 
                            type="object" class="btn-secondary"
                            invisible="state not in ('draft', 'confirmed')"/>
                    <button name="action_reset_to_draft" string="Reset to Draft" 
                            type="object" class="btn-secondary"
                            invisible="state != 'cancelled'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="order_id" readonly="1"/>
                            <field name="group_type"/>
                            <field name="carrier_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="delivery_price"/>
                            <field name="total_weight"/>
                            <field name="total_value"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Order Lines" name="order_lines">
                            <field name="order_line_ids" readonly="1">
                                <list>
                                    <field name="product_id"/>
                                    <field name="name"/>
                                    <field name="product_uom_qty"/>
                                    <field name="product_uom"/>
                                    <field name="price_unit"/>
                                    <field name="price_subtotal"/>
                                    <field name="delivery_group_type"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Delivery Group List View -->
    <record id="view_delivery_group_list" model="ir.ui.view">
        <field name="name">delivery.group.list</field>
        <field name="model">delivery.group</field>
        <field name="arch" type="xml">
            <list string="Delivery Groups">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="order_id"/>
                <field name="group_type"/>
                <field name="carrier_id"/>
                <field name="delivery_price"/>
                <field name="total_weight"/>
                <field name="state"/>
            </list>
        </field>
    </record>

    <!-- Delivery Group Search View -->
    <record id="view_delivery_group_search" model="ir.ui.view">
        <field name="name">delivery.group.search</field>
        <field name="model">delivery.group</field>
        <field name="arch" type="xml">
            <search string="Delivery Groups">
                <field name="name"/>
                <field name="order_id"/>
                <field name="carrier_id"/>
                <field name="group_type"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Confirmed" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                <separator/>
                <filter string="MTO Groups" name="mto" domain="[('group_type', '=', 'mto')]"/>
                <filter string="Stock Groups" name="stock" domain="[('group_type', '=', 'stock')]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Order" name="group_order" context="{'group_by': 'order_id'}"/>
                    <filter string="Carrier" name="group_carrier" context="{'group_by': 'carrier_id'}"/>
                    <filter string="Group Type" name="group_type" context="{'group_by': 'group_type'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Delivery Group Action -->
    <record id="action_delivery_group" model="ir.actions.act_window">
        <field name="name">Delivery Groups</field>
        <field name="res_model">delivery.group</field>
        <field name="path">delivery-groups</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_delivery_group_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a delivery group
            </p>
            <p>
                Delivery groups allow you to organize order lines into separate
                delivery charges based on product routing (MTO vs Stock).
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_delivery_group"
              name="Delivery Groups"
              parent="stock.menu_stock_root"
              action="action_delivery_group"
              sequence="50"/>
</odoo>
