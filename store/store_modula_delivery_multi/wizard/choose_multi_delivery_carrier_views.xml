<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Multi-Delivery Carrier Wizard Form View -->
    <record id="view_choose_multi_delivery_carrier_form" model="ir.ui.view">
        <field name="name">choose.multi.delivery.carrier.form</field>
        <field name="model">choose.multi.delivery.carrier</field>
        <field name="arch" type="xml">
            <form string="Select Multiple Delivery Methods">
                <sheet>
                    <div class="oe_title">
                        <h1>Configure Multiple Delivery Charges</h1>
                        <p>Select delivery methods for each product group in your order.</p>
                    </div>
                    
                    <group>
                        <group>
                            <field name="order_id" readonly="1"/>
                            <field name="group_count"/>
                        </group>
                        <group>
                            <field name="total_delivery_cost" widget="monetary"/>
                        </group>
                    </group>

                    <separator string="Delivery Groups"/>
                    <field name="delivery_group_ids">
                        <list>
                            <field name="sequence" widget="handle"/>
                            <field name="group_type" />
                            <field name="line_count" />
                            <field name="total_weight" />
                            <field name="order_id" invisible="1"/>
                            <field name="total_value"  widget="monetary"/>
                            <field name="available_carrier_ids" invisible="1"/>
                            <field name="carrier_id"
                                   required="1"/>
                            <field name="delivery_price" readonly="1" widget="monetary"/>
                        </list>
                        <form string="Delivery Group">
                            <group>
                                <group>
                                    <field name="group_type"/>
                                    <field name="carrier_id" 
                                           required="1"/>
                                    <field name="delivery_price" readonly="1"/>
                                    <field name="sequence"/>
                                </group>
                                <group>
                                    <field name="line_count" readonly="1"/>
                                    <field name="total_weight" readonly="1"/>
                                    <field name="total_value" readonly="1"/>
                                    <field name="order_id" invisible="1"/>
                                </group>
                            </group>
                            
                            <separator string="Order Lines in this Group"/>
                            <field name="order_line_ids" domain="[('order_id', '=', order_id)]" >
                                <list>
                                    <field name="product_id"/>
                                    <field name="name"/>
                                    <field name="product_uom_qty"/>
                                    <field name="product_uom"/>
                                    <field name="price_unit"/>
                                    <field name="price_subtotal"/>
                                    <field name="delivery_group_type"/>
                                </list>
                            </field>
                            
                            <field name="available_carrier_ids" invisible="1"/>
                        </form>
                    </field>

                    <!-- Summary section -->
                    <group class="mt-3">
                        <group>
                            <div class="alert alert-info" role="alert">
                                <strong>Summary:</strong><br/>
                                <span invisible="group_count == 0">
                                    <field name="group_count"/> delivery groups configured
                                </span>
                                <span invisible="total_delivery_cost == 0">
                                    <br/>Total delivery cost: <field name="total_delivery_cost" widget="monetary"/>
                                </span>
                            </div>
                        </group>
                    </group>
                </sheet>
                
                <footer>
                    <button name="button_calculate_rates" 
                            string="Calculate All Rates" 
                            type="object" 
                            class="btn-secondary"/>
                    <button name="button_confirm" 
                            string="Apply Delivery Charges" 
                            type="object" 
                            class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Delivery Group Wizard List View (for embedded use) -->
    <record id="view_delivery_group_wizard_list" model="ir.ui.view">
        <field name="name">delivery.group.wizard.list</field>
        <field name="model">delivery.group.wizard</field>
        <field name="arch" type="xml">
            <list string="Delivery Groups" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="group_type" readonly="1"/>
                <field name="display_name" readonly="1"/>
                <field name="carrier_id" required="1"/>
                <field name="delivery_price" readonly="1"/>
                <field name="total_weight" readonly="1"/>
                <field name="line_count" readonly="1"/>
            </list>
        </field>
    </record>

    <!-- Action for Multi-Delivery Wizard -->
    <record id="action_choose_multi_delivery_carrier" model="ir.actions.act_window">
        <field name="name">Select Multiple Delivery Methods</field>
        <field name="res_model">choose.multi.delivery.carrier</field>
        <field name="path">multi-delivery-wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_choose_multi_delivery_carrier_form"/>
    </record>
</odoo>
