# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class ChooseMultiDeliveryCarrier(models.TransientModel):
    _name = 'choose.multi.delivery.carrier'
    _description = 'Multi-Delivery Carrier Selection Wizard'

    order_id = fields.Many2one(
        'sale.order', 
        string='Sale Order',
        required=True,
        help="Sale order for which to configure multiple deliveries"
    )
    delivery_group_ids = fields.One2many(
        'delivery.group.wizard', 
        'wizard_id',
        string='Delivery Groups',
        help="Delivery groups with carrier selection"
    )
    
    # Summary fields
    total_delivery_cost = fields.Float(
        string='Total Delivery Cost',
        compute='_compute_totals',
        help="Total cost of all delivery groups"
    )
    group_count = fields.Integer(
        string='Number of Groups',
        compute='_compute_totals',
        help="Number of delivery groups"
    )
    
    @api.depends('delivery_group_ids.delivery_price')
    def _compute_totals(self):
        """Compute total delivery cost and group count"""
        for wizard in self:
            wizard.total_delivery_cost = sum(wizard.delivery_group_ids.mapped('delivery_price'))
            wizard.group_count = len(wizard.delivery_group_ids)
    
    @api.model
    def default_get(self, fields_list):
        """Pre-populate delivery groups based on order lines"""
        res = super().default_get(fields_list)
        
        if 'order_id' in self.env.context:
            order = self.env['sale.order'].browse(self.env.context['order_id'])
            groups = order._group_order_lines_by_delivery()
            
            group_lines = []
            sequence = 10
            
            for group_type, lines in groups.items():
                if lines:  # Only create groups for non-empty line sets
                    group_lines.append((0, 0, {
                        'group_type': group_type,
                        'order_line_ids': [(6, 0, lines.ids)],
                        'sequence': sequence,
                    }))
                    sequence += 10
            
            res['delivery_group_ids'] = group_lines
        
        return res
    
    def button_confirm(self):
        """Apply multiple delivery charges"""
        self.ensure_one()
        
        if not self.delivery_group_ids:
            raise UserError(self.env._('No delivery groups configured. Please add at least one delivery group.'))
        
        # Validate that all groups have carriers
        groups_without_carrier = self.delivery_group_ids.filtered(lambda g: not g.carrier_id)
        if groups_without_carrier:
            raise UserError(self.env._(
                'Please select a delivery method for all groups. '
                'Groups without carrier: %s'
            ) % ', '.join(groups_without_carrier.mapped('display_name')))
        
        # Create delivery groups
        delivery_groups = []
        for wizard_group in self.delivery_group_ids:
            if wizard_group.carrier_id and wizard_group.order_line_ids:
                group = self.env['delivery.group'].create({
                    'order_id': self.order_id.id,
                    'carrier_id': wizard_group.carrier_id.id,
                    'group_type': wizard_group.group_type,
                    'delivery_price': wizard_group.delivery_price,
                    'order_line_ids': [(6, 0, wizard_group.order_line_ids.ids)],
                    'sequence': wizard_group.sequence,
                })
                delivery_groups.append(group)
        
        if not delivery_groups:
            raise UserError(self.env._('No valid delivery groups could be created.'))
        
        # Apply delivery groups to order
        self.order_id.set_multi_delivery_lines(delivery_groups)
        
        # Show success message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': self.env._('Multiple Delivery Charges Applied'),
                'message': self.env._(
                    'Successfully configured %d delivery groups with total cost of %s'
                ) % (len(delivery_groups), self.order_id.currency_id.symbol + str(self.total_delivery_cost)),
                'type': 'success',
                'next': {'type': 'ir.actions.act_window_close'},
            }
        }
    
    def button_calculate_rates(self):
        """Calculate rates for all groups"""
        self.ensure_one()
        
        for group in self.delivery_group_ids:
            if group.carrier_id and group.order_line_ids:
                result = group._calculate_delivery_rate()
                if result.get('success'):
                    group.delivery_price = result['price']
                else:
                    group.delivery_price = 0.0
                    if result.get('error_message'):
                        # Could show warning to user
                        pass
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': self.env._('Rates Calculated'),
                'message': self.env._('Delivery rates have been calculated for all groups.'),
                'type': 'success',
            }
        }
