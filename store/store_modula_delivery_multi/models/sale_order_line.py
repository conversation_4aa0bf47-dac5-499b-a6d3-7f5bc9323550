# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    delivery_group_id = fields.Many2one(
        'delivery.group', 
        string="Delivery Group",
        help="Delivery group this line belongs to (for delivery lines only)"
    )
    delivery_group_type = fields.Selection([
        ('mto', 'Make to Order'),
        ('stock', 'In Stock'),
        ('custom', 'Custom')
    ], 
        string="Delivery Group Type",
        compute='_compute_delivery_group_type',
        store=True,
        help="Type of delivery group based on product routing"
    )
    
    @api.depends('product_id', 'route_id', 'is_mto', 'is_delivery')
    def _compute_delivery_group_type(self):
        """Determine delivery group based on product routing"""
        for line in self:
            if line.is_delivery:
                # Delivery lines don't have a group type based on routing
                line.delivery_group_type = False
            elif line.is_mto:
                line.delivery_group_type = 'mto'
            else:
                line.delivery_group_type = 'stock'
    

    
    @api.model_create_multi
    def create(self, vals_list):
        """Override create to handle delivery group assignment"""
        lines = super().create(vals_list)

        # Process each created line
        for line in lines:
            # If this is a delivery line and order has multi-delivery enabled
            if line.is_delivery and line.order_id.multi_delivery_enabled:
                # The delivery_group_id should be set by the calling code
                pass

        return lines
    
    def write(self, vals):
        """Override write to handle delivery group changes"""
        result = super().write(vals)
        
        # If product or routing changed, recompute delivery group type
        if any(field in vals for field in ['product_id', 'route_id']):
            self._compute_delivery_group_type()
        
        return result
    
    def unlink(self):
        """Override unlink to handle delivery group cleanup"""
        # If deleting delivery lines, clean up delivery groups
        delivery_lines = self.filtered('is_delivery')
        if delivery_lines:
            groups_to_check = delivery_lines.mapped('delivery_group_id')
            
        result = super().unlink()
        
        # Clean up empty delivery groups
        if delivery_lines and groups_to_check:
            for group in groups_to_check:
                if group.exists():
                    # Check if group still has associated delivery lines
                    remaining_delivery_lines = group.order_id.order_line.filtered(
                        lambda l: l.is_delivery and l.delivery_group_id == group
                    )
                    if not remaining_delivery_lines:
                        # If no delivery lines left, we might want to keep the group
                        # for potential re-calculation, so just mark it as draft
                        group.write({'state': 'draft', 'delivery_price': 0.0})
        
        return result
