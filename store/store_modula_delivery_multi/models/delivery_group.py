# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class DeliveryGroup(models.Model):
    _name = 'delivery.group'
    _description = 'Delivery Group for Multiple Charges'
    _order = 'sequence, id'

    # Core fields
    order_id = fields.Many2one(
        'sale.order', 
        string='Sale Order',
        required=True, 
        ondelete='cascade',
        help="Sale order this delivery group belongs to"
    )
    carrier_id = fields.Many2one(
        'delivery.carrier', 
        string='Delivery Method',
        required=True,
        help="Carrier used for this delivery group"
    )
    group_type = fields.Selection([
        ('mto', 'Make to Order'),
        ('stock', 'In Stock'),
        ('custom', 'Custom')
    ], string='Group Type', required=True, help="Type of delivery group based on product routing")
    
    # Pricing and logistics
    delivery_price = fields.Float(
        string="Delivery Cost",
        help="Calculated delivery cost for this group"
    )
    order_line_ids = fields.Many2many(
        'sale.order.line', 
        'delivery_group_sale_order_line_rel',
        'delivery_group_id',
        'sale_order_line_id',
        string="Order Lines",
        help="Order lines included in this delivery group"
    )
    total_weight = fields.Float(
        string="Total Weight",
        compute='_compute_totals',
        store=True,
        help="Total weight of products in this group"
    )
    total_value = fields.Float(
        string="Total Value",
        compute='_compute_totals',
        store=True,
        help="Total value of products in this group"
    )
    
    # UI and organization
    sequence = fields.Integer(string='Sequence', default=10, help="Sequence for ordering groups")
    name = fields.Char(
        string='Name',
        compute='_compute_name',
        store=True,
        help="Display name for the delivery group"
    )
    active = fields.Boolean(string='Active', default=True)
    
    # Status and tracking
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled')
    ], string='State', default='draft')
    
    @api.depends('group_type', 'carrier_id')
    def _compute_name(self):
        """Compute display name for delivery group"""
        for group in self:
            if group.group_type and group.carrier_id:
                group_type_label = dict(group._fields['group_type'].selection)[group.group_type]
                group.name = f"{group_type_label} - {group.carrier_id.name}"
            else:
                group.name = group.env._("Delivery Group")
    
    @api.depends('order_line_ids.product_qty', 'order_line_ids.product_id.weight', 'order_line_ids.price_subtotal')
    def _compute_totals(self):
        """Compute total weight and value for the delivery group"""
        for group in self:
            total_weight = 0.0
            total_value = 0.0
            
            for line in group.order_line_ids:
                if line.product_id:
                    total_weight += line.product_qty * line.product_id.weight
                    total_value += line.price_subtotal
            
            group.total_weight = total_weight
            group.total_value = total_value
    
    @api.constrains('order_line_ids')
    def _check_order_lines_consistency(self):
        """Ensure all order lines belong to the same order"""
        for group in self:
            if group.order_line_ids:
                orders = group.order_line_ids.mapped('order_id')
                if len(orders) > 1 or (orders and orders[0] != group.order_id):
                    raise ValidationError(self.env._(
                        "All order lines in a delivery group must belong to the same sale order."
                    ))
    
    def calculate_delivery_rate(self):
        """Calculate delivery rate for this group using carrier logic"""
        self.ensure_one()
        
        if not self.carrier_id or not self.order_line_ids:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('Missing carrier or order lines'),
            }
        
        # Use the enhanced carrier method for group-based calculation
        result = self.carrier_id.rate_shipment_for_lines(self.order_id, self.order_line_ids)
        
        if result.get('success'):
            self.delivery_price = result['price']
        if self.env.context.get('recalculate_delivery_rate'):
            delivery_order_line = self.order_id.order_line.filtered(lambda l: l.is_delivery and l.delivery_group_id == self)
            delivery_order_line.price_unit = result['price']
        return result
    
    def action_confirm(self):
        """Confirm the delivery group"""
        self.write({'state': 'confirmed'})
    
    def action_cancel(self):
        """Cancel the delivery group"""
        self.write({'state': 'cancelled'})
    
    def action_reset_to_draft(self):
        """Reset delivery group to draft"""
        self.write({'state': 'draft'})
    
    def unlink(self):
        """Override unlink to handle delivery line cleanup"""
        # Remove associated delivery lines when group is deleted
        for group in self:
            delivery_lines = group.order_id.order_line.filtered(
                lambda l: l.is_delivery and l.delivery_group_id == group
            )
            if delivery_lines:
                delivery_lines.unlink()
        
        return super().unlink()
