# Odoo 18 Python Development Guidelines

This guide provides comprehensive best practices and migration guidelines for developing Python code in Odoo 18 modules, focusing on breaking changes and new patterns introduced in Odoo 18.

## ⚠️ CRITICAL: Breaking Changes in Odoo 18

### 1. `user_has_groups` Method Removed ❌

**Problem**: The `user_has_groups` method has been completely removed from Odoo 18.

**Migration**: Replace with `self.env.user.has_group()` method.

#### ✅ Correct Odoo 18 Pattern
```python
# CORRECT: Use self.env.user.has_group()
if self.env.user.has_group('stock.group_stock_manager'):
    # User has stock manager rights
    pass

# For multiple groups check
if self.env.user.has_group('sale.group_sale_user'):
    # User has sales rights
    pass

# Check if user belongs to any of multiple groups
user = self.env.user
if user.has_group('stock.group_stock_user') or user.has_group('stock.group_stock_manager'):
    # User has stock access
    pass
```

#### ❌ Deprecated Odoo 17 Pattern
```python
# WRONG: user_has_groups no longer exists
if self.user_has_groups('stock.group_stock_manager'):  # ❌ AttributeError
    pass

# WRONG: Old pattern
if self.env.user.user_has_groups('sale.group_sale_user'):  # ❌ AttributeError
    pass
```

#### 🔍 Real-World Examples from Odoo 18 Core
```python
# From hr_attendance/models/hr_attendance.py
if not self.env.user.has_group("hr_attendance.group_hr_attendance_manager"):
    return True

# From purchase_requisition/models/purchase.py
if self.env.user.has_group('purchase_requisition.group_purchase_alternatives'):
    self.filtered(lambda po: po.purchase_group_id).has_alternatives = True

# From im_livechat/models/res_users.py
user.has_access_livechat = user.has_group('im_livechat.im_livechat_group_user')
```

### 2. Access Control Methods Consolidated 🔄

**Problem**: Multiple access control methods have been replaced by unified methods.

**Migration**: Use the new consolidated methods for better performance and consistency.

#### ✅ New Odoo 18 Access Control Methods
```python
# CORRECT: Use check_access() instead of separate methods
def my_method(self):
    # Replaces both check_access_rights() and check_access_rule()
    self.check_access('read')  # or 'write', 'create', 'unlink'

    # For filtering records with access
    accessible_records = self._filter_access('read')
    return accessible_records
```

#### ❌ Deprecated Odoo 17 Methods
```python
# WRONG: These methods are deprecated
self.check_access_rights('read')  # ❌ Use check_access('read')
self.check_access_rule('write')   # ❌ Use check_access('write')
self._filter_access_rule('read')  # ❌ Use _filter_access('read')
self._filter_access_rule_python('write')  # ❌ Use _filter_access('write')
```

#### 🔍 Real-World Examples from Odoo 18 Core
```python
# From website_slides/models/ir_binary.py
def _find_record_check_access(self, record, access_token, field):
    if record._name == "slide.slide":
        record.check_access('read')  # ✅ New unified method
    return super()._find_record_check_access(record, access_token, field)

# From mail/models/mail_activity.py
def _check_access(self, operation: str) -> tuple | None:
    result = super()._check_access(operation)  # ✅ New pattern
    if not self:
        return result
```

### 3. `_name_search` Replaced by `_search_display_name` 🔍

**Problem**: The `_name_search` method has been removed and replaced.

**Migration**: Use `_search_display_name` method for custom search behavior.

#### ✅ Correct Odoo 18 Pattern
```python
# CORRECT: Use _search_display_name
@api.model
def _search_display_name(self, operator, value):
    """Custom search logic for display name"""
    domain = []
    if value and operator == 'ilike':
        domain = [
            '|', ('name', operator, value),
            ('code', operator, value)
        ]
    return domain
```

#### ❌ Deprecated Odoo 17 Pattern
```python
# WRONG: _name_search no longer exists
@api.model
def _name_search(self, name='', args=None, operator='ilike', limit=100, name_get_uid=None):
    # ❌ This method is completely removed
    pass
```

#### 🔍 Real-World Examples from Odoo 18 Core
```python
# From product/models/product_template.py
@api.model
def _search_display_name(self, operator, value):
    domain = super()._search_display_name(operator, value)
    if self.env.context.get('search_product_product', bool(value)):
        combine = expression.OR if operator not in expression.NEGATIVE_TERM_OPERATORS else expression.AND
        domain = combine([domain, [('product_variant_ids', operator, value)]])
    return domain

# From account/models/account_account.py
@api.model
def _search_display_name(self, operator, value):
    domain = []
    if operator != 'ilike' or (value or '').strip():
        criteria_operator = ['|'] if operator not in expression.NEGATIVE_TERM_OPERATORS else ['&', '!']
        name_domain = criteria_operator + [('code_prefix_start', '=ilike', value + '%'), ('name', operator, value)]
        domain = expression.AND([name_domain, domain])
    return domain
```

### 4. `_check_recursion` Replaced by `_has_cycle` 🔄

**Problem**: The `_check_recursion` method is deprecated.

**Migration**: Use `_has_cycle` method for cycle detection.

#### ✅ Correct Odoo 18 Pattern
```python
# CORRECT: Use _has_cycle()
@api.constrains('parent_id')
def _check_parent_cycle(self):
    """Check for cycles in parent hierarchy"""
    if self._has_cycle():
        raise ValidationError(_("You cannot create recursive hierarchies."))
```

#### ❌ Deprecated Odoo 17 Pattern
```python
# WRONG: _check_recursion is deprecated
@api.constrains('parent_id')
def _check_parent_recursion(self):
    if not self._check_recursion():  # ❌ Deprecated method
        raise ValidationError(_("You cannot create recursive hierarchies."))
```

### 5. `copy` and `copy_data` Multi-Record Support 📋

**Problem**: `copy` and `copy_data` methods now work on multiple records and return different data structures.

**Migration**: Handle the new multi-record behavior and list return values.

#### ✅ Correct Odoo 18 Pattern
```python
# CORRECT: Handle multi-record copy operations
def duplicate_records(self):
    """Duplicate multiple records at once"""
    # copy_data now returns a list of dictionaries
    copy_data_list = self.copy_data()

    # Create multiple records from the copy data
    new_records = self.create(copy_data_list)
    return new_records

def duplicate_single_record(self):
    """Duplicate a single record"""
    self.ensure_one()
    # Even for single record, copy_data returns a list
    copy_data_list = self.copy_data()
    new_record = self.create(copy_data_list[0])  # Take first element
    return new_record
```

#### ❌ Old Odoo 17 Pattern
```python
# WRONG: Assuming copy_data returns a single dictionary
def duplicate_record_old(self):
    self.ensure_one()
    copy_data = self.copy_data()  # ❌ This now returns a list
    new_record = self.create(copy_data)  # ❌ Will fail - expecting dict, got list
    return new_record
```

### 6. Field `group_operator` Replaced by `aggregator` 📊

**Problem**: The `group_operator` attribute on fields has been replaced.

**Migration**: Use `aggregator` attribute instead of `group_operator`.

#### ✅ Correct Odoo 18 Pattern
```python
# CORRECT: Use aggregator attribute
class MyModel(models.Model):
    _name = 'my.model'

    total_amount = fields.Float(
        string='Total Amount',
        aggregator='sum'  # ✅ New attribute name
    )

    average_score = fields.Float(
        string='Average Score',
        aggregator='avg'  # ✅ Use aggregator
    )

    max_value = fields.Float(
        string='Maximum Value',
        aggregator='max'  # ✅ Supported aggregators: sum, avg, max, min, count
    )
```

#### ❌ Deprecated Odoo 17 Pattern
```python
# WRONG: group_operator is obsolete
class MyModel(models.Model):
    _name = 'my.model'

    total_amount = fields.Float(
        string='Total Amount',
        group_operator='sum'  # ❌ Obsolete attribute
    )
```

### 7. Non-Stored Related Field Search Restrictions ⚠️

**Problem**: Searches on non-stored related fields now throw exceptions instead of warnings.

**Migration**: Either store the related field or implement custom search methods.

#### ✅ Correct Odoo 18 Patterns
```python
# OPTION 1: Store the related field
class MyModel(models.Model):
    _name = 'my.model'

    partner_name = fields.Char(
        related='partner_id.name',
        store=True  # ✅ Store to enable search
    )

# OPTION 2: Implement custom search method
class MyModel(models.Model):
    _name = 'my.model'

    partner_name = fields.Char(
        related='partner_id.name',
        search='_search_partner_name'  # ✅ Custom search method
    )

    def _search_partner_name(self, operator, value):
        """Custom search for non-stored related field"""
        return [('partner_id.name', operator, value)]
```

#### ❌ Problematic Odoo 17 Pattern
```python
# WRONG: This will throw an exception in Odoo 18
class MyModel(models.Model):
    _name = 'my.model'

    partner_name = fields.Char(
        related='partner_id.name'
        # ❌ No store=True and no search method
        # Searching on this field will raise an exception
    )
```

### 8. `create` Method Must Use `@api.model_create_multi` ⚠️ **CRITICAL**

**Problem**: Overriding the `create` method without `@api.model_create_multi` decorator causes deprecation warnings and performance issues.

**Migration**: Always use `@api.model_create_multi` decorator when overriding `create` method.

#### ✅ Correct Odoo 18 Pattern
```python
# CORRECT: Use @api.model_create_multi for batch processing
class MyModel(models.Model):
    _inherit = 'base.model'

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to handle batch creation"""
        # Process each record in the batch
        for vals in vals_list:
            # Pre-processing logic for each record
            if 'custom_field' not in vals:
                vals['custom_field'] = self._get_default_value()

        # Call parent create with batch
        records = super().create(vals_list)

        # Post-processing for each created record
        for record in records:
            # Custom logic after creation
            if record.needs_processing:
                record._process_after_create()

        return records

    def _get_default_value(self):
        """Helper method for default value calculation"""
        return 'default_value'
```

#### ❌ Deprecated Odoo 17 Pattern
```python
# WRONG: Using @api.model without multi-record support
class MyModel(models.Model):
    _inherit = 'base.model'

    @api.model
    def create(self, vals):  # ❌ DeprecationWarning: not overriding create in batch
        """This will cause deprecation warnings"""
        # Pre-processing
        if 'custom_field' not in vals:
            vals['custom_field'] = 'default'

        # Call parent create
        record = super().create(vals)  # ❌ Not batch-aware

        # Post-processing
        record._process_after_create()

        return record
```

#### 🔍 Real-World Examples from Odoo 18 Core
```python
# From sale/models/sale_order_line.py
@api.model_create_multi
def create(self, vals_list):
    for vals in vals_list:
        if vals.get('display_type'):
            vals.update(product_id=False, price_unit=0, product_uom_qty=0, product_uom=False, customer_lead=0)
    lines = super().create(vals_list)
    for line in lines:
        if line.product_id and line.order_id.state == 'sale':
            msg = _("Extra line with %s", line.product_id.display_name)
            line.order_id.message_post(body=msg)
    return lines

# From account/models/account_move_line.py
@api.model_create_multi
def create(self, vals_list):
    # BUSINESS FIELDS
    for vals in vals_list:
        if 'balance' in vals:
            vals['amount_currency'] = vals.get('amount_currency', vals['balance'])
    return super().create(vals_list)
```

#### 📋 Migration Checklist for `create` Method

**Before Migration:**
- [ ] **Identify all `create` method overrides** in your module
- [ ] **Check current decorator** (`@api.model` vs `@api.model_create_multi`)
- [ ] **Review parameter handling** (`vals` vs `vals_list`)
- [ ] **Check return value handling** (single record vs recordset)

**Migration Steps:**
1. **Change decorator** from `@api.model` to `@api.model_create_multi`
2. **Update parameter** from `vals` to `vals_list`
3. **Handle batch processing** with loops for pre/post-processing
4. **Update return value** to handle recordset instead of single record
5. **Test batch creation** to ensure functionality works correctly

**After Migration:**
- [ ] **Test single record creation** (`Model.create({...})`)
- [ ] **Test batch record creation** (`Model.create([{...}, {...}])`)
- [ ] **Verify no deprecation warnings** in logs
- [ ] **Check performance** with large batch operations

### 9. Performance Optimization with `self.env._` 🚀

**Problem**: String translation can be optimized in certain contexts.

**Migration**: Use `self.env._` instead of `_` for better performance in specific cases.

#### ✅ Performance-Optimized Odoo 18 Pattern
```python
# CORRECT: Use self.env._ for performance improvement
def get_error_message(self):
    """Get localized error message with performance optimization"""
    if self.state == 'error':
        return self.env._('An error occurred during processing')  # ✅ Better performance
    return self.env._('Operation completed successfully')

def validate_data(self):
    """Validation with optimized translations"""
    if not self.name:
        raise ValidationError(self.env._('Name is required'))  # ✅ Optimized
```

#### 🔍 When to Use `self.env._` vs `_`
```python
# Use self.env._ when:
# 1. Inside model methods
# 2. Performance is critical
# 3. Context is available

def model_method(self):
    message = self.env._('Optimized translation')  # ✅ Use self.env._

# Use _ when:
# 1. In module-level code
# 2. In decorators
# 3. When self.env is not available

@api.constrains('field')
def _check_field(self):
    if not self.field:
        raise ValidationError(_('Field is required'))  # ✅ Use _ here
```

## 🔧 Best Practices for Odoo 18 Development

### 1. Template Method Pattern for Multi-Module Architecture ⭐ **RECOMMENDED**

**Problem**: When developing multiple modules that depend on each other, you need extensible methods that can be overridden by dependent modules while maintaining OOP principles.

**Solution**: Implement template methods in base modules that can be extended by dependent modules.

#### ✅ Template Method Pattern Implementation

**Base Module** (`modula_sale_employee_selection`):
```python
class HrEmployee(models.Model):
    _inherit = "hr.employee"

    def get_all_employees(self, login=False):
        """Main method that uses template method pattern"""
        self = self.sudo()
        if login:
            self.login_user_employee()

        all_employees = self.search_read(
            [("employee_type", "=", "employee")], ["id", "name", "barcode"]
        )

        # 🆕 TEMPLATE METHOD: Add is_show field with default value True
        # This can be overridden by dependent modules for specific logic
        for employee in all_employees:
            employee["is_show"] = self._get_employee_is_show(employee)

        # ... rest of method logic ...
        return {"all": all_employees, "connected": connected, "admin": admin}

    def _get_employee_is_show(self, employee_data):
        """Template method to determine if employee should be shown in selection

        Base implementation returns True for all employees.
        Override this method in dependent modules for specific logic.

        Args:
            employee_data (dict): Employee data with id, name, barcode

        Returns:
            bool: True if employee should be shown, False otherwise
        """
        return True  # Default: show all employees
```

**Dependent Module** (`modula_sale`):
```python
class HrEmployee(models.Model):
    _inherit = "hr.employee"

    def _get_employee_is_show(self, employee_data):
        """Override template method to show only store managers

        This override extends the base template with module-specific logic:
        - Shows employee if their job_id is store manager
        - Hides all other employees

        Args:
            employee_data (dict): Employee data with id, name, barcode

        Returns:
            bool: True if employee is store manager, False otherwise
        """
        try:
            # Get the employee record to check job_id
            employee = self.browse(employee_data["id"])

            if not employee.exists():
                return False

            # Check if employee's job is store manager
            # You can adjust this condition based on your job naming convention
            if employee.job_id and employee.job_id.name:
                job_name = employee.job_id.name.lower()
                # Check for various store manager job titles
                store_manager_keywords = [
                    'store manager',
                    'shop manager',
                    'retail manager',
                    'branch manager'
                ]

                for keyword in store_manager_keywords:
                    if keyword in job_name:
                        return True

            return False

        except Exception as e:
            # Log error and return False for safety
            self.env['ir.logging'].sudo().create({
                'name': 'hr.employee._get_employee_is_show',
                'type': 'server',
                'level': 'ERROR',
                'message': f"Error checking employee is_show for ID {employee_data.get('id')}: {str(e)}",
                'path': 'modula_sale.models.hr_employee',
                'line': '0',
                'func': '_get_employee_is_show'
            })
            return False
```

#### 🎯 Benefits of Template Method Pattern

1. **Extensibility**: Other modules can override and extend functionality
2. **OOP Compliance**: Follows inheritance and polymorphism principles
3. **Maintainability**: Base functionality is preserved and extended
4. **Reusability**: Template methods can be used across multiple modules
5. **Consistency**: Ensures consistent interface across module hierarchy

#### 📋 Template Method Pattern Guidelines

```python
# ✅ GOOD: Template method in base module
class BaseModel(models.Model):
    _inherit = 'base.model'

    def template_method(self, param):
        """Template method that can be overridden"""
        # Minimal base implementation
        return {'success': True, 'data': param}

# ✅ GOOD: Override in dependent module
class ExtendedModel(models.Model):
    _inherit = 'base.model'

    def template_method(self, param):
        """Override with enhanced functionality"""
        # Call parent template
        result = super().template_method(param)

        # Add specific logic
        result['enhanced'] = True
        result['module'] = 'extended'

        return result

# ❌ AVOID: Duplicate implementation without inheritance
class BadModel(models.Model):
    _inherit = 'base.model'

    def template_method(self, param):
        """Bad: Reimplemented without calling super()"""
        # This breaks the template pattern
        return {'success': True, 'data': param, 'bad': True}
```

### 2. Form Controller Action Button Patterns ⚠️ **CRITICAL**

**Problem**: Custom action buttons need to preserve form state while executing custom logic.

**Solution**: Follow Odoo's standard `beforeExecuteActionButton` pattern with proper form saving.

#### ✅ Correct Form Controller Pattern

```javascript
// CORRECT: Follow Odoo's standard save-before-action pattern
class CustomFormController extends FormController {
    async beforeExecuteActionButton(clickParams) {
        if (clickParams.name === "my_custom_action") {
            // ✅ CRITICAL: Save form first (like Odoo standard behavior)
            const record = this.model.root;
            let saved = false;

            // Save the form to preserve field changes
            const params = { reload: !(this.env.inDialog && clickParams.close) };
            saved = await record.save(params);

            if (saved !== false) {
                // Only execute custom logic if save was successful
                await this.customLogic();
            }

            // Prevent default action execution (we handled it manually)
            return false;
        } else {
            return super.beforeExecuteActionButton(clickParams);
        }
    }
}
```

#### ❌ Problematic Pattern (Causes Data Loss)

```javascript
// WRONG: This pattern loses form changes
class BadFormController extends FormController {
    async beforeExecuteActionButton(clickParams) {
        if (clickParams.name === "my_custom_action") {
            // ❌ PROBLEM: No save before custom logic
            await this.customLogic();
            // ❌ PROBLEM: Returning false prevents Odoo's default save behavior
            return false;
        }
    }
}
```

#### 🔍 Why This Pattern is Critical

From `@odoo/addons/web/static/src/views/form/form_controller.js`:
```javascript
async beforeExecuteActionButton(clickParams) {
    const record = this.model.root;
    if (clickParams.special !== "cancel") {
        // ✅ Odoo ALWAYS saves the form before executing action buttons
        const params = { reload: !(this.env.inDialog && clickParams.close) };
        saved = await record.save(params);
        return saved;
    }
}
```

**Key Insight**: Odoo's standard behavior automatically saves forms before action execution. Custom overrides must replicate this behavior to prevent data loss.

### 3. Access Control Best Practices
```python
class MyModel(models.Model):
    _name = 'my.model'

    def secure_operation(self):
        """Example of proper access control"""
        # Check access before operation
        self.check_access('write')

        # Filter records user can access
        accessible_records = self._filter_access('read')

        # Perform operation only on accessible records
        return accessible_records.write({'processed': True})
```

### 2. Search Method Implementation
```python
class MyModel(models.Model):
    _name = 'my.model'

    @api.model
    def _search_display_name(self, operator, value):
        """Comprehensive search implementation"""
        domain = super()._search_display_name(operator, value)

        if value and operator in ('ilike', '=like', 'like'):
            # Add custom search logic
            custom_domain = [
                '|', ('code', operator, value),
                ('description', operator, value)
            ]
            domain = expression.OR([domain, custom_domain])

        return domain
```

### 3. Multi-Record Copy Operations
```python
class MyModel(models.Model):
    _name = 'my.model'

    def bulk_duplicate(self):
        """Efficient bulk duplication"""
        # Get copy data for all records
        copy_data_list = self.copy_data()

        # Modify copy data if needed
        for data in copy_data_list:
            data['name'] = f"Copy of {data.get('name', '')}"

        # Create all duplicates at once
        return self.create(copy_data_list)

## 📋 Migration Checklist

### Pre-Migration Audit
- [ ] **Search for `user_has_groups`** usage and replace with `self.env.user.has_group()`
- [ ] **Find `check_access_rights`/`check_access_rule`** and replace with `check_access()`
- [ ] **Locate `_name_search`** methods and convert to `_search_display_name`
- [ ] **Replace `_check_recursion`** with `_has_cycle`
- [ ] **Update `group_operator`** to `aggregator` in field definitions
- [ ] **Review non-stored related fields** for search compatibility
- [ ] **Consider `self.env._`** optimization opportunities

### Code Quality Validation
- [ ] All access control uses new unified methods
- [ ] Search methods follow new `_search_display_name` pattern
- [ ] Copy operations handle list return values correctly
- [ ] Field aggregators use correct attribute names
- [ ] Related field searches are properly implemented

### Testing Requirements
- [ ] Test access control with different user groups
- [ ] Verify search functionality works as expected
- [ ] Test copy operations with single and multiple records
- [ ] Validate field aggregation in reports/views
- [ ] Check related field search behavior

## 🚨 Common Migration Errors

### Error 1: AttributeError on user_has_groups
```python
# ERROR: AttributeError: 'res.users' object has no attribute 'user_has_groups'
# FIX: Replace with has_group()
if self.env.user.has_group('base.group_user'):  # ✅
```

### Error 2: copy_data Type Error
```python
# ERROR: TypeError: create() argument must be dict, not list
# FIX: Handle list return value
copy_data_list = self.copy_data()
new_records = self.create(copy_data_list)  # ✅
```

### Error 3: Search on Non-Stored Related Field
```python
# ERROR: Exception when searching on non-stored related field
# FIX: Add store=True or custom search method
partner_name = fields.Char(related='partner_id.name', store=True)  # ✅
```

## 🔍 Advanced Patterns and Examples

### 1. Complex Access Control Scenarios
```python
class DeliveryCarrier(models.Model):
    _inherit = 'delivery.carrier'

    def action_configure_postcode_pricing(self):
        """Configure postcode pricing with proper access control"""
        # Check if user has delivery management rights
        if not self.env.user.has_group('stock.group_stock_manager'):
            raise AccessError(self.env._('You need delivery management rights to configure pricing.'))

        # Filter carriers user can modify
        accessible_carriers = self._filter_access('write')

        return {
            'type': 'ir.actions.act_window',
            'name': self.env._('Configure Postcode Pricing'),
            'res_model': 'delivery.postcode.pricelist',
            'view_mode': 'list,form',
            'domain': [('delivery_carrier_id', 'in', accessible_carriers.ids)],
            'context': {'default_delivery_carrier_id': self.id}
        }
```

### 2. Advanced Search Implementation
```python
class DeliveryPostcodePricelist(models.Model):
    _name = 'delivery.postcode.pricelist'

    @api.model
    def _search_display_name(self, operator, value):
        """Enhanced search supporting postcode, city, and carrier name"""
        domain = super()._search_display_name(operator, value)

        if value and operator in ('ilike', '=like', 'like'):
            # Search across multiple fields
            search_domain = [
                '|', '|', '|',
                ('postcode_from', operator, value),
                ('postcode_to', operator, value),
                ('city', operator, value),
                ('delivery_carrier_id.name', operator, value)
            ]

            # Combine with parent domain
            if operator not in expression.NEGATIVE_TERM_OPERATORS:
                domain = expression.OR([domain, search_domain])
            else:
                domain = expression.AND([domain, search_domain])

        return domain
```

### 3. Optimized Translation Usage
```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _get_postcode_delivery_price(self, carrier):
        """Get delivery price with optimized error messages"""
        if not self.partner_shipping_id or not self.partner_shipping_id.zip:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('No delivery address or postcode specified.'),
                'warning_message': False
            }

        # Use optimized translation for frequently called method
        postcode = self.partner_shipping_id.zip
        price = self.env['delivery.postcode.pricelist'].find_delivery_price(
            carrier, postcode,
            self.partner_shipping_id.country_id,
            self.partner_shipping_id.state_id,
            self.partner_shipping_id.city
        )

        if price is not False:
            return {
                'success': True,
                'price': price,
                'error_message': False,
                'warning_message': False
            }
        else:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('No delivery price found for postcode %s.') % postcode,
                'warning_message': False
            }
```

## 📚 Reference Examples from Odoo 18 Core

### Access Control Examples
```python
# From mail/models/mail_activity.py - New access control pattern
def _check_access(self, operation: str) -> tuple | None:
    result = super()._check_access(operation)
    if not self:
        return result
    # Custom access logic here
    return result

# From various models - User group checking
if self.env.user.has_group('base.group_system'):
    # System admin operations
    pass
```

### Search Method Examples
```python
# From product/models/product_template.py
@api.model
def _search_display_name(self, operator, value):
    domain = super()._search_display_name(operator, value)
    if self.env.context.get('search_product_product', bool(value)):
        combine = expression.OR if operator not in expression.NEGATIVE_TERM_OPERATORS else expression.AND
        domain = combine([domain, [('product_variant_ids', operator, value)]])
    return domain
```

### Field Definition Examples
```python
# Modern field definitions with aggregator
total_amount = fields.Float(
    string='Total Amount',
    aggregator='sum',  # ✅ New aggregator attribute
    help="Sum of all amounts"
)

# Proper related field with search support
partner_name = fields.Char(
    related='partner_id.name',
    store=True,  # ✅ Enable search
    string='Partner Name'
)
```

### 3. Multi-Module Dependency Architecture 🏗️

**Problem**: Managing dependencies between multiple custom modules while maintaining clean separation of concerns.

**Solution**: Use template method pattern with proper module dependency hierarchy.

#### ✅ Module Dependency Best Practices

**Module Structure**:
```
modula_sale_employee_selection (base module)
├── Provides: Employee selection infrastructure
├── Template methods: complete_employee_approval(), need_employee_selection()
└── Minimal business logic

modula_sale (dependent module)
├── Depends on: modula_sale_employee_selection
├── Overrides: complete_employee_approval(), need_employee_selection()
└── Specific business logic: discount approval, validation
```

**Manifest Dependencies**:
```python
# modula_sale/__manifest__.py
{
    'name': 'Modula Sale',
    'depends': [
        'sale',
        'modula_sale_employee_selection',  # ✅ Explicit dependency
    ],
    # ...
}

# modula_sale_employee_selection/__manifest__.py
{
    'name': 'Modula Sale Employee Selection',
    'depends': [
        'sale',
        'hr',  # ✅ Only core dependencies
    ],
    # ...
}
```

#### 📋 Multi-Module Method Override Pattern

```python
# Base module: modula_sale_employee_selection
class SaleOrder(models.Model):
    _inherit = "sale.order"

    def need_employee_selection(self, **kwargs):
        """Template method for employee selection logic"""
        # Base implementation - always returns False
        return False

    def complete_employee_approval(self, employee_id):
        """Template method for approval completion"""
        # Minimal base implementation
        employee = self.env['hr.employee'].browse(employee_id)
        return {
            'success': True,
            'message': _('Approval completed successfully'),
            'employee_name': employee.name,
            'need_approve': False
        }

# Dependent module: modula_sale
class SaleOrder(models.Model):
    _inherit = "sale.order"

    def need_employee_selection(self, **kwargs):
        """Override with order line array processing"""
        if kwargs.get("order_line"):
            order_lines = kwargs.get("order_line")
            return self.is_exist_line_need_approve(order_lines)
        else:
            # Call parent template for other cases
            res = super().need_employee_selection(**kwargs)
            return res

    def is_exist_line_need_approve(self, order_lines):
        """Check if any order line needs approval"""
        need_approve = []
        sale_order_line = self.env["sale.order.line"]

        for order_line in order_lines:
            if isinstance(order_line, list):
                # Handle list format [operation, id, values]
                if order_line[0] == 1:
                    sale_order_line = sale_order_line.browse(order_line[1])

                if order_line[2].get('product_id'):
                    product_id = order_line[2].get('product_id')
                else:
                    product_id = sale_order_line.product_id.id
                discount = order_line[2].get('discount')
            else:
                # Handle dict format
                product_id = order_line.get("product_id")
                discount = order_line.get("discount")
                sale_order_line = sale_order_line.browse(order_line.get("id"))

            session_owner_employee_id = request.session.get("session_owner", False)
            if session_owner_employee_id and product_id:
                res = sale_order_line.is_this_line_need_approve(product_id, session_owner_employee_id, discount)
                if res:
                    need_approve.append(True)
                else:
                    need_approve.append(False)
        return any(need_approve)
```

## 🎯 AI Development Guidelines

### When Requesting Python Code from AI

#### ✅ Good Prompts for Odoo 18 Multi-Module Development
```
Create a Python model for Odoo 18 with template method pattern:
- Base module provides template methods that can be overridden
- Dependent modules override template methods using super()
- Use self.env.user.has_group() for access control
- Implement _search_display_name for custom search
- Use check_access() for access validation
- Handle copy_data() returning lists
- Use aggregator instead of group_operator
- Include proper error handling with self.env._
- Follow OOP inheritance principles with super() calls
```

#### ❌ Avoid These Patterns in Prompts
```
Use user_has_groups for access control
Implement _name_search method
Use check_access_rights and check_access_rule
Assume copy_data returns a dictionary
Use group_operator for field aggregation
Create duplicate methods without inheritance
Ignore module dependencies
```

#### 🔧 Specific Instructions to Include
1. **Specify Odoo 18 compliance**: "Ensure code follows Odoo 18 patterns"
2. **Request modern patterns**: "Use new access control methods"
3. **Include error handling**: "Add proper exception handling"
4. **Performance optimization**: "Use self.env._ where appropriate"
5. **Template method pattern**: "Use template methods for extensible functionality"
6. **Module dependencies**: "Consider multi-module architecture with proper inheritance"

## 🔧 Troubleshooting Common Issues

### Issue 1: Migration from Odoo 17
```bash
# Search for deprecated patterns
grep -r "user_has_groups" --include="*.py" .
grep -r "check_access_rights" --include="*.py" .
grep -r "_name_search" --include="*.py" .
grep -r "group_operator" --include="*.py" .
```

### Issue 2: Testing Access Control
```python
# Test access control in different contexts
def test_access_control(self):
    # Test with different user groups
    user = self.env['res.users'].create({
        'name': 'Test User',
        'login': '<EMAIL>',
        'groups_id': [(6, 0, [self.env.ref('stock.group_stock_user').id])]
    })

    # Test access with specific user
    records = self.env['my.model'].with_user(user)
    accessible = records._filter_access('read')
    self.assertTrue(accessible)
```

### Issue 3: Validating Search Methods
```python
# Test search functionality
def test_search_display_name(self):
    # Test custom search implementation
    domain = self.env['my.model']._search_display_name('ilike', 'test')
    self.assertTrue(domain)

    # Test search results
    results = self.env['my.model'].search(domain)
    self.assertTrue(results)
```

---

**Guide Status**: ✅ **COMPREHENSIVE ODOO 18 PYTHON COMPLIANCE**
**Focus**: Breaking changes, new patterns, and best practices for Odoo 18
**Usage**: Essential reference for Odoo 18 Python development and migration
**Companion**: Use with `odoo_xml_view_guide_line.md` for complete Odoo 18 development
