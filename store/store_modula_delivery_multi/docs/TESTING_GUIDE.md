# Multiple Delivery Charges - Testing Guide

## Overview

This guide provides comprehensive testing procedures for the Multiple Delivery Charges module, including unit tests, integration tests, and manual testing scenarios.

## Test Structure

### Test Files Location
```
modula_delivery_multi/tests/
├── __init__.py
├── test_delivery_group.py          # Unit tests for delivery groups
├── test_multi_delivery_wizard.py   # Wizard functionality tests
└── test_integration.py             # End-to-end integration tests
```

## Running Tests

### Command Line Testing

#### Run All Module Tests
```bash
# Run all tests for the module
odoo-bin -d test_database --test-enable --stop-after-init -i modula_delivery_multi

# Run with specific test tags
odoo-bin -d test_database --test-enable --stop-after-init --test-tags modula_delivery_multi
```

#### Run Specific Test Classes
```bash
# Run only delivery group tests
odoo-bin -d test_database --test-enable --stop-after-init --test-tags modula_delivery_multi.test_delivery_group

# Run only wizard tests
odoo-bin -d test_database --test-enable --stop-after-init --test-tags modula_delivery_multi.test_multi_delivery_wizard

# Run only integration tests
odoo-bin -d test_database --test-enable --stop-after-init --test-tags modula_delivery_multi.test_integration
```

#### Run with Verbose Output
```bash
# Detailed test output
odoo-bin -d test_database --test-enable --stop-after-init -i modula_delivery_multi --log-level=test
```

### IDE Testing

#### VS Code with Odoo Extension
1. Install Odoo extension for VS Code
2. Configure test runner in settings
3. Use "Run Tests" command in command palette
4. View results in integrated terminal

#### PyCharm with Odoo Plugin
1. Configure Odoo project structure
2. Set up test runner configuration
3. Use built-in test runner
4. Debug tests with breakpoints

## Unit Tests

### Delivery Group Tests (`test_delivery_group.py`)

#### Test Coverage
- ✅ **Model Creation**: Basic delivery group creation and validation
- ✅ **Name Computation**: Automatic name generation based on type and carrier
- ✅ **Totals Computation**: Weight and value calculations
- ✅ **Constraints**: Data integrity validation
- ✅ **State Management**: Draft/confirmed/cancelled transitions
- ✅ **Rate Calculation**: Delivery price computation
- ✅ **Cleanup Logic**: Proper cleanup on deletion

#### Key Test Methods
```python
def test_delivery_group_creation(self):
    """Test basic delivery group creation"""
    
def test_delivery_group_name_computation(self):
    """Test delivery group name computation"""
    
def test_delivery_group_totals_computation(self):
    """Test total weight and value computation"""
    
def test_order_lines_consistency_constraint(self):
    """Test that order lines must belong to the same order"""
    
def test_delivery_group_state_transitions(self):
    """Test delivery group state transitions"""
    
def test_calculate_delivery_rate(self):
    """Test delivery rate calculation"""
```

### Wizard Tests (`test_multi_delivery_wizard.py`)

#### Test Coverage
- ✅ **Auto-population**: Automatic delivery group creation
- ✅ **Totals Computation**: Wizard-level calculations
- ✅ **Display Names**: User-friendly group naming
- ✅ **Rate Calculation**: Automatic pricing in wizard context
- ✅ **Confirmation Logic**: Successful wizard completion
- ✅ **Validation**: Error handling for invalid configurations
- ✅ **Data Consistency**: Order line validation

#### Key Test Methods
```python
def test_wizard_default_get(self):
    """Test wizard auto-population of delivery groups"""
    
def test_wizard_group_totals_computation(self):
    """Test delivery group wizard totals computation"""
    
def test_wizard_rate_calculation(self):
    """Test automatic rate calculation in wizard"""
    
def test_wizard_confirm_success(self):
    """Test successful wizard confirmation"""
    
def test_wizard_confirm_no_carriers(self):
    """Test wizard confirmation fails without carriers"""
```

## Integration Tests

### End-to-End Tests (`test_integration.py`)

#### Test Coverage
- ✅ **Complete Workflow**: Order creation to delivery application
- ✅ **Backward Compatibility**: Single delivery still works
- ✅ **Multi-delivery Management**: Enable/disable functionality
- ✅ **Rate Calculation**: Group-based pricing accuracy
- ✅ **Order Line Grouping**: Automatic MTO vs Stock detection
- ✅ **UI Integration**: Action and view functionality
- ✅ **Postcode Integration**: Integration with modula_delivery
- ✅ **Error Handling**: Non-eligible order scenarios

#### Key Test Methods
```python
def test_complete_multi_delivery_workflow(self):
    """Test complete multi-delivery workflow from order to confirmation"""
    
def test_backward_compatibility_single_delivery(self):
    """Test that single delivery still works when multi-delivery is not used"""
    
def test_disable_multi_delivery(self):
    """Test disabling multi-delivery functionality"""
    
def test_carrier_rate_calculation_for_lines(self):
    """Test carrier rate calculation for specific order lines"""
    
def test_postcode_integration_fallback(self):
    """Test integration with postcode pricing and fallback"""
```

## Manual Testing Scenarios

### Scenario 1: Basic Multi-Delivery Setup

#### Prerequisites
- Sale order with both MTO and Stock products
- Multiple carriers configured
- Postcode pricing rules set up

#### Test Steps
1. **Create Sale Order**
   - Add 2x MTO Product (Custom Cabinet)
   - Add 3x Stock Product (Standard Shelf)
   - Verify "Multiple Deliveries" button appears

2. **Open Multi-Delivery Wizard**
   - Click "Multiple Deliveries" button
   - Verify groups are auto-populated
   - Check product distribution is correct

3. **Configure Carriers**
   - Select Express Carrier for MTO group
   - Select Standard Carrier for Stock group
   - Verify rates calculate automatically

4. **Apply Delivery Charges**
   - Click "Apply Delivery Charges"
   - Verify success notification
   - Check delivery lines are created

#### Expected Results
- ✅ Two delivery groups created
- ✅ Two delivery lines in order
- ✅ Multi-delivery enabled flag set
- ✅ Delivery Groups tab populated

### Scenario 2: Postcode Pricing Integration

#### Prerequisites
- Postcode carrier configured
- Postcode pricing rules for test address
- Sale order with shipping address

#### Test Steps
1. **Create Order with Postcode Carrier**
   - Set shipping address with known postcode
   - Add products to order
   - Open multi-delivery wizard

2. **Select Postcode Carrier**
   - Choose postcode carrier for a group
   - Verify rate calculates from postcode rules
   - Check for fallback pricing if needed

3. **Test Different Postcodes**
   - Change shipping address postcode
   - Recalculate rates
   - Verify pricing updates correctly

#### Expected Results
- ✅ Postcode pricing applied correctly
- ✅ Fallback pricing used when appropriate
- ✅ Rate updates with address changes

### Scenario 3: Error Handling

#### Test Cases

**Case 1: No Carriers Selected**
1. Open multi-delivery wizard
2. Leave carrier fields empty
3. Click "Apply Delivery Charges"
4. **Expected**: Error message about missing carriers

**Case 2: Non-Eligible Order**
1. Create order with only one product type
2. Try to open multi-delivery wizard
3. **Expected**: Error message about eligibility

**Case 3: Invalid Postcode**
1. Set shipping address with invalid postcode
2. Select postcode carrier
3. **Expected**: Fallback pricing or error message

## Performance Testing

### Load Testing

#### Large Order Testing
```python
def test_large_order_performance(self):
    """Test performance with orders containing many lines"""
    # Create order with 100+ lines
    # Measure wizard performance
    # Verify rate calculation speed
```

#### Bulk Operations
- Test with multiple orders simultaneously
- Measure wizard opening time
- Check rate calculation performance
- Monitor memory usage

### Database Performance

#### Query Optimization
- Monitor SQL queries during wizard operations
- Check for N+1 query problems
- Verify proper indexing usage
- Measure database response times

## Test Data Setup

### Basic Test Data
```python
# Standard test setup
self.partner = self.env['res.partner'].create({
    'name': 'Test Customer',
    'street': '123 Test Street',
    'city': 'Test City',
    'zip': '12345',
    'country_id': self.env.ref('base.us').id,
})

self.product_mto = self.env['product.product'].create({
    'name': 'MTO Product',
    'type': 'product',
    'weight': 1.0,
    'list_price': 100.0,
})

self.carrier = self.env['delivery.carrier'].create({
    'name': 'Test Carrier',
    'delivery_type': 'fixed',
    'fixed_price': 10.0,
})
```

### Postcode Test Data
```python
# Postcode pricing setup
self.postcode_carrier = self.env['delivery.carrier'].create({
    'name': 'Postcode Carrier',
    'delivery_type': 'postcode',
    'postcode_fixed_price': 5.0,
})

self.env['delivery.postcode.pricelist'].create({
    'carrier_id': self.postcode_carrier.id,
    'postcode': '12345',
    'price': 15.0,
})
```

## Continuous Integration

### CI/CD Pipeline Integration

#### GitHub Actions Example
```yaml
name: Odoo Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Odoo
        run: |
          # Setup Odoo environment
          # Install dependencies
      - name: Run Tests
        run: |
          odoo-bin -d test_db --test-enable --stop-after-init -i modula_delivery_multi
```

#### Test Coverage Reporting
- Use coverage.py for Python coverage
- Generate HTML reports
- Set minimum coverage thresholds
- Integrate with CI/CD pipeline

## Debugging Test Failures

### Common Issues

#### Test Database Issues
```bash
# Reset test database
dropdb test_database
createdb test_database
odoo-bin -d test_database -i base --stop-after-init
```

#### Module Loading Issues
```bash
# Check module dependencies
odoo-bin -d test_database --test-enable -i modula_delivery_multi --log-level=debug
```

#### Test Data Issues
- Verify test data creation
- Check for data conflicts
- Ensure proper cleanup

### Debug Tools

#### Python Debugger
```python
import pdb; pdb.set_trace()  # Add breakpoint in test
```

#### Odoo Debug Mode
```bash
# Run with debug logging
odoo-bin -d test_database --test-enable --log-level=debug
```

## Test Maintenance

### Regular Maintenance Tasks
1. **Update test data** when business logic changes
2. **Add new test cases** for new features
3. **Review test coverage** regularly
4. **Update documentation** when tests change
5. **Performance monitoring** for regression detection

### Best Practices
- ✅ Keep tests independent and isolated
- ✅ Use descriptive test method names
- ✅ Include both positive and negative test cases
- ✅ Test edge cases and error conditions
- ✅ Maintain test data consistency
- ✅ Document complex test scenarios

---

**Conclusion**: Comprehensive testing ensures the Multiple Delivery Charges module works reliably across all scenarios and integrates properly with existing Odoo functionality.
