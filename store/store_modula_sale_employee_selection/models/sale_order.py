# -*- coding: utf-8 -*-
from odoo.http import request
from odoo.exceptions import ValidationError

from odoo import api, fields, models, _


class SaleOrder(models.Model):
    _inherit = "sale.order"

    need_approve = fields.Boolean(string="Need Approve", default=False)

    @api.model_create_multi
    def create(self, vals_list):
        records = super(SaleOrder, self).create(vals_list)
        for record in records:
            if request.session.get("session_owner", False):
                record.employee_id = request.session.get("session_owner", False)
        return records

    def need_employee_selection(self, **kwargs):
        return False


    def action_approve_sale_order(self):
        """Template inheritance Approve button action - same functionality as original Approve button

        This method provides the same functionality as action_approve_sale_order
        but is designed to work with the template inheritance approach.
        The actual approval logic is handled in the frontend JavaScript.
        """
        # The approval logic is handled in the frontend JavaScript
        # This method exists to satisfy the button's action requirement
        return {
            'type': 'ir.actions.act_window_close',
        }
