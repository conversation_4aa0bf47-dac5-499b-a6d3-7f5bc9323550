# Odoo 18 Button Implementation Patterns

## 🎯 **Overview**

This guide covers two main approaches for implementing custom buttons in Odoo 18, based on real-world experience with the employee approval system.

## 📋 **Pattern Comparison**

### **Pattern 1: XML View Buttons (*.xml for *.py)**
**Use Case**: Simple buttons with direct backend actions

```xml
<!-- views/sale_order_views.xml -->
<record id="view_order_form_inherit" model="ir.ui.view">
    <field name="name">sale.order.form.inherit</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <xpath expr="//header" position="inside">
            <button name="action_approve_sale_order" 
                    string="Approve" 
                    type="object" 
                    class="btn-primary"
                    attrs="{'invisible': [('need_approve', '=', False)]}"/>
        </xpath>
    </field>
</record>
```

```python
# models/sale_order.py
class SaleOrder(models.Model):
    _inherit = "sale.order"
    
    def action_approve_sale_order(self):
        # Direct backend processing
        self.need_approve = False
        return {'type': 'ir.actions.act_window_close'}
```

### **Pattern 2: Template Inheritance Buttons (*.xml for *.js)**
**Use Case**: Complex buttons with frontend logic, async validation, reactive behavior

```xml
<!-- static/src/views/form/status_bar_buttons/approve_button.xml -->
<templates xml:space="preserve">
    <t t-name="web.StatusBarButtons" t-inherit="web.StatusBarButtons" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('o_statusbar_buttons')]" position="inside">
            <t t-if="shouldShowApproveButton">
                <button name="action_approve_sale_order" 
                        string="Approve" 
                        type="button" 
                        class="btn btn-primary"
                        t-on-click="onApproveClick">
                    Approve
                </button>
            </t>
        </xpath>
    </t>
</templates>
```

```javascript
// static/src/views/form/status_bar_buttons/approve_button.js
import { StatusBarButtons } from "@web/views/form/status_bar_buttons/status_bar_buttons";
import { patch } from "@web/core/utils/patch";
import { useState, useBus, useEffect } from "@odoo/owl";

patch(StatusBarButtons.prototype, {
    setup() {
        super.setup();
        if (this.env.model?.root.resModel == 'sale.order') {
            // Reactive state management
            this.needApprove = useState({ value: false });
            
            // Listen to model changes
            useBus(this.env.model.bus, "update", async () => {
                await this.updateNeedApproveFromBackend();
            });
        }
    },
    
    async updateNeedApproveFromBackend() {
        // Complex async validation logic
        const result = await this.env.services.orm.call(
            "sale.order", "need_employee_selection", [recordId], params
        );
        this.needApprove.value = result;
    },
    
    get shouldShowApproveButton() {
        // Synchronous getter using reactive state
        return this.needApprove?.value || this.env.model.root.data.need_approve;
    },
    
    async onApproveClick(ev) {
        // Complex frontend workflow
        ev.preventDefault();
        await this.openEmployeeSelection();
    }
});
```

## 🔄 **When to Use Each Pattern**

### **Use XML View Buttons When**:
- ✅ **Simple backend actions**: Direct model updates, reports, wizards
- ✅ **Standard Odoo patterns**: Following typical form button behavior
- ✅ **No complex frontend logic**: Minimal JavaScript requirements
- ✅ **Static visibility**: Button visibility based on simple field conditions

**Example Use Cases**:
- Confirm order button
- Generate report button
- Open wizard button
- Simple status change buttons

### **Use Template Inheritance When**:
- ✅ **Complex frontend workflows**: Multi-step processes, popups, validations
- ✅ **Async backend validation**: Real-time validation with backend calls
- ✅ **Reactive behavior**: Button visibility based on complex conditions
- ✅ **Custom UI interactions**: Non-standard button behavior

**Example Use Cases**:
- Employee approval workflows
- Real-time validation buttons
- Multi-step approval processes
- Dynamic button behavior

## 🚀 **Migration Path: XML View → Template Inheritance**

### **Step 1: Identify Migration Triggers**
```
Migrate when you need:
- Async backend validation in button visibility
- Complex frontend workflows
- Reactive state management
- Custom popup integrations
```

### **Step 2: Create Template Inheritance Structure**
```
static/src/views/form/status_bar_buttons/
├── button_name.xml          # Template inheritance
├── button_name.js           # JavaScript patch
└── button_name.scss         # Optional styling
```

### **Step 3: Implement Reactive Pattern**
```javascript
// Always follow this pattern for template inheritance buttons
patch(StatusBarButtons.prototype, {
    setup() {
        super.setup();
        // Initialize reactive state
        // Set up event listeners
    },
    
    async updateStateFromBackend() {
        // Async backend validation
        // Update reactive state
    },
    
    get shouldShowButton() {
        // Synchronous getter
        // Use reactive state
    },
    
    async onButtonClick(ev) {
        // Handle button click
        // Complex frontend logic
    }
});
```

### **Step 4: Update Manifest**
```python
# __manifest__.py
'assets': {
    'web.assets_backend': [
        'module_name/static/src/views/form/status_bar_buttons/button_name.js',
        'module_name/static/src/views/form/status_bar_buttons/button_name.xml',
    ],
}
```

## 🎯 **Best Practices**

### **Template Inheritance Best Practices**:

#### **1. Reactive State Management**
```javascript
// ✅ CORRECT: Use reactive state for button visibility
this.needApprove = useState({ value: false });

// ❌ WRONG: Direct model data access in getter
get shouldShow() {
    return this.env.model.root.data.some_field; // Not reactive
}
```

#### **2. Async Validation Pattern**
```javascript
// ✅ CORRECT: Separate async method for backend calls
async updateStateFromBackend() {
    const result = await this.env.services.orm.call(...);
    this.reactiveState.value = result;
}

// ❌ WRONG: Async operations in getter
get shouldShow() {
    this.env.services.orm.call(...).then(result => {
        return result; // Won't work - getter already returned
    });
}
```

#### **3. Event Listener Setup**
```javascript
// ✅ CORRECT: Multiple reactive triggers
useBus(this.env.model.bus, "update", async () => {
    await this.updateStateFromBackend();
});

useEffect(() => {
    this.updateStateFromBackend();
}, () => [this.env.model.root.data.relevant_field]);
```

#### **4. Error Handling**
```javascript
// ✅ CORRECT: Graceful error handling
async updateStateFromBackend() {
    try {
        const result = await this.env.services.orm.call(...);
        this.needApprove.value = result;
    } catch (error) {
        console.error("Validation error:", error);
        // Fallback to model data
        this.needApprove.value = this.env.model.root.data.need_approve;
    }
}
```

## 🧪 **Testing Patterns**

### **XML View Button Testing**:
```python
# Test backend method directly
def test_approve_button_action(self):
    order = self.env['sale.order'].create({...})
    result = order.action_approve_sale_order()
    self.assertFalse(order.need_approve)
```

### **Template Inheritance Button Testing**:
```javascript
// Test reactive behavior
QUnit.test("button visibility updates on state change", async function (assert) {
    const component = await createComponent();
    
    // Test initial state
    assert.strictEqual(component.shouldShowApproveButton, false);
    
    // Update state
    component.needApprove.value = true;
    await nextTick();
    
    // Test reactive update
    assert.strictEqual(component.shouldShowApproveButton, true);
});
```

## 📊 **Performance Considerations**

### **Template Inheritance Optimizations**:

#### **1. Debouncing Backend Calls**
```javascript
// Avoid excessive backend calls
const debouncedUpdate = debounce(this.updateStateFromBackend.bind(this), 300);
useBus(this.env.model.bus, "update", debouncedUpdate);
```

#### **2. Early Return Optimization**
```javascript
// Stop processing when approval found
for (const lineData of orderLines) {
    const result = await this.validateLine(lineData);
    if (result) {
        this.needApprove.value = true;
        return; // Early exit
    }
}
```

#### **3. Caching Strategy**
```javascript
// Cache validation results
this.validationCache = new Map();
const cacheKey = `${lineId}-${discount}-${productId}`;
if (this.validationCache.has(cacheKey)) {
    return this.validationCache.get(cacheKey);
}
```

## 🎯 **Decision Matrix**

| Requirement | XML View | Template Inheritance |
|-------------|----------|---------------------|
| Simple backend action | ✅ Preferred | ❌ Overkill |
| Async validation | ❌ Limited | ✅ Excellent |
| Reactive behavior | ❌ Basic | ✅ Advanced |
| Complex workflows | ❌ Difficult | ✅ Natural |
| Standard Odoo patterns | ✅ Perfect | ❌ Custom |
| Development speed | ✅ Fast | ❌ Slower |
| Maintenance | ✅ Simple | ❌ Complex |
| Flexibility | ❌ Limited | ✅ High |

## 🚀 **Success Criteria**

### **XML View Buttons** ✅
- [ ] Button appears/disappears based on field conditions
- [ ] Backend action executes correctly
- [ ] Standard Odoo behavior maintained
- [ ] Simple implementation and maintenance

### **Template Inheritance Buttons** ✅
- [ ] Reactive state updates correctly
- [ ] Async backend validation works
- [ ] Complex workflows function properly
- [ ] Performance optimized
- [ ] Error handling implemented
- [ ] Testing coverage adequate

## 📚 **Related Documentation**

- **JavaScript Guidelines**: `ODOO18_JAVASCRIPT_GUIDELINES.md`
- **XML Template Guidelines**: `ODOO18_XML_TEMPLATE_GUIDELINES.md`
- **Async Patterns**: `ASYNC_GETTER_PATTERN.md`
- **Reactive Components**: `REACTIVE_BUTTON_IMPLEMENTATION.md`

---

**Key Takeaway**: Choose the pattern based on complexity requirements. Start with XML view buttons for simple cases, migrate to template inheritance when you need advanced frontend features.
