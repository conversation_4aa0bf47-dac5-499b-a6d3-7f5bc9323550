# Odoo 18 Async Patterns Guide

## 🎯 **Overview**

This guide covers proper patterns for handling async operations in non-async contexts, based on real-world experience with the employee approval system.

## ⚠️ **The Fundamental Problem**

### **Common Scenario**:
You need to call an async backend method in a synchronous context (like a getter):

```javascript
// ❌ WRONG: This won't work
get shouldShowButton() {
    // Getters cannot be async in JavaScript
    const result = await this.env.services.orm.call(...); // SyntaxError
    return result;
}
```

### **Why This Fails**:
- **Getters must be synchronous**: JavaScript getters cannot use `await`
- **Template rendering**: Templates expect immediate return values
- **Reactive systems**: OWL reactive system needs synchronous getters

## ✅ **Solution: Reactive State Pattern**

### **Core Principle**:
```
Async Operation → Update State → Synchronous Getter → Template Re-render
```

## 🔄 **Implementation Patterns**

### **Pattern 1: Event-Driven Updates**
```javascript
import { useState, useBus } from "@odoo/owl";

patch(Component.prototype, {
    setup() {
        super.setup();
        
        // 1. Create reactive state
        this.asyncState = useState({ value: null, loading: false });
        
        // 2. Listen to events that trigger async operations
        useBus(this.env.model.bus, "update", async () => {
            await this.updateAsyncState();
        });
        
        // 3. Initial load
        this.updateAsyncState();
    },
    
    // 4. Async method to update state
    async updateAsyncState() {
        this.asyncState.loading = true;
        try {
            const result = await this.env.services.orm.call(
                "model.name", "method_name", [params]
            );
            this.asyncState.value = result;
        } catch (error) {
            console.error("Async operation failed:", error);
            this.asyncState.value = null; // Fallback
        } finally {
            this.asyncState.loading = false;
        }
    },
    
    // 5. Synchronous getter using state
    get computedValue() {
        return this.asyncState.value || this.fallbackValue;
    }
});
```

### **Pattern 2: Field Change Triggers**
```javascript
import { useEffect } from "@odoo/owl";

patch(Component.prototype, {
    setup() {
        super.setup();
        this.validationState = useState({ isValid: false });
        
        // Watch specific fields for changes
        useEffect(
            () => {
                this.validateWithBackend();
            },
            () => [
                this.env.model.root.data.field1,
                this.env.model.root.data.field2
            ]
        );
    },
    
    async validateWithBackend() {
        if (!this.shouldValidate()) return;
        
        try {
            const isValid = await this.env.services.orm.call(
                "model.name", "validate_method", 
                [this.env.model.root.resId],
                {
                    field1: this.env.model.root.data.field1,
                    field2: this.env.model.root.data.field2
                }
            );
            this.validationState.isValid = isValid;
        } catch (error) {
            this.validationState.isValid = false;
        }
    },
    
    get isFormValid() {
        return this.validationState.isValid;
    }
});
```

### **Pattern 3: Manual Trigger with Caching**
```javascript
patch(Component.prototype, {
    setup() {
        super.setup();
        this.cache = new Map();
        this.loadingState = useState({ loading: false });
    },
    
    async triggerAsyncOperation(params) {
        const cacheKey = JSON.stringify(params);
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        this.loadingState.loading = true;
        try {
            const result = await this.env.services.orm.call(
                "model.name", "method_name", [params]
            );
            
            // Cache result
            this.cache.set(cacheKey, result);
            return result;
            
        } finally {
            this.loadingState.loading = false;
        }
    },
    
    get isLoading() {
        return this.loadingState.loading;
    }
});
```

## 🎯 **Real-World Example: Button Visibility**

### **Problem**: 
Button visibility needs async backend validation with product_id

### **Solution**:
```javascript
// Employee approval button implementation
patch(StatusBarButtons.prototype, {
    setup() {
        super.setup();
        if (this.env.model?.root.resModel == 'sale.order') {
            // Reactive state for button visibility
            this.needApprove = useState({ value: false });
            
            // Listen to model changes
            useBus(this.env.model.bus, "update", async () => {
                await this.updateNeedApproveFromBackend();
            });
            
            // Watch order line changes
            useEffect(
                () => {
                    this.updateNeedApproveFromBackend();
                },
                () => [this.env.model.root.data.order_line]
            );
        }
    },
    
    async updateNeedApproveFromBackend() {
        try {
            const record = this.env.model.root;
            const orderLines = record.data.order_line;
            
            if (!orderLines || orderLines.length === 0) {
                this.needApprove.value = false;
                return;
            }
            
            // Check each order line for approval requirements
            let needsApproval = false;
            for (const lineData of orderLines) {
                if (lineData.data?.discount && lineData.data?.product_id) {
                    const result = await this.env.services.orm.call(
                        "sale.order", 
                        "need_employee_selection",
                        [record.resId],
                        {
                            'order_line': lineData.resId,
                            'input': lineData.data.discount,
                            'field_name': 'discount',
                            'product_id': lineData.data.product_id[0]
                        }
                    );
                    
                    if (result) {
                        needsApproval = true;
                        break; // Early exit optimization
                    }
                }
            }
            
            this.needApprove.value = needsApproval;
            
        } catch (error) {
            console.error("Error checking approval:", error);
            // Fallback to model data
            this.needApprove.value = this.env.model.root.data.need_approve;
        }
    },
    
    // Synchronous getter - no async operations
    get shouldShowApproveButton() {
        if (this.env.model?.root.resModel !== 'sale.order') {
            return false;
        }
        
        // Use reactive state as primary source
        const stateValue = this.needApprove?.value;
        const modelValue = this.env.model.root.data.need_approve;
        
        return stateValue || modelValue;
    }
});
```

## 🚀 **Performance Optimizations**

### **1. Debouncing**
```javascript
import { debounce } from "@web/core/utils/timing";

setup() {
    // Debounce rapid changes
    const debouncedUpdate = debounce(this.updateAsyncState.bind(this), 300);
    useBus(this.env.model.bus, "update", debouncedUpdate);
}
```

### **2. Early Returns**
```javascript
async updateAsyncState() {
    // Quick validation checks first
    if (!this.shouldPerformAsyncOperation()) {
        this.asyncState.value = false;
        return;
    }
    
    // Expensive async operation only when needed
    const result = await this.expensiveBackendCall();
    this.asyncState.value = result;
}
```

### **3. Batch Operations**
```javascript
async validateMultipleItems(items) {
    // Batch multiple validations in single backend call
    const results = await this.env.services.orm.call(
        "model.name", "batch_validate", [items.map(item => item.id)]
    );
    
    // Update state for all items at once
    items.forEach((item, index) => {
        item.validationState.value = results[index];
    });
}
```

## ⚠️ **Common Anti-Patterns**

### **❌ Anti-Pattern 1: Async Getters**
```javascript
// NEVER DO THIS
async get shouldShow() {
    const result = await this.env.services.orm.call(...);
    return result;
}
```

### **❌ Anti-Pattern 2: Promise in Getter**
```javascript
// NEVER DO THIS
get shouldShow() {
    this.env.services.orm.call(...).then(result => {
        return result; // This won't work - getter already returned
    });
    return false; // This is what actually gets returned
}
```

### **❌ Anti-Pattern 3: Blocking Operations**
```javascript
// NEVER DO THIS
get shouldShow() {
    // Synchronous blocking call - will freeze UI
    const result = this.blockingBackendCall();
    return result;
}
```

## 🧪 **Testing Async Patterns**

### **Test Reactive State Updates**:
```javascript
QUnit.test("async state updates correctly", async function (assert) {
    const component = await createComponent();
    
    // Mock backend call
    const mockCall = sinon.stub(component.env.services.orm, 'call')
        .resolves(true);
    
    // Trigger async update
    await component.updateAsyncState();
    
    // Verify state updated
    assert.strictEqual(component.asyncState.value, true);
    assert.ok(mockCall.calledOnce);
});
```

### **Test Error Handling**:
```javascript
QUnit.test("handles async errors gracefully", async function (assert) {
    const component = await createComponent();
    
    // Mock backend error
    sinon.stub(component.env.services.orm, 'call')
        .rejects(new Error("Backend error"));
    
    // Trigger async update
    await component.updateAsyncState();
    
    // Verify fallback behavior
    assert.strictEqual(component.asyncState.value, null);
});
```

## 📊 **Pattern Selection Guide**

| Use Case | Pattern | When to Use |
|----------|---------|-------------|
| Form field validation | Event-Driven | Real-time validation needed |
| Button visibility | Field Change Triggers | Depends on specific fields |
| Data loading | Manual Trigger | User-initiated operations |
| Search/filtering | Debounced Event-Driven | Rapid user input |
| Batch operations | Manual with Caching | Multiple related items |

## 🎯 **Best Practices Summary**

### **✅ DO**:
- Use reactive state (`useState`) for async results
- Implement proper error handling with fallbacks
- Optimize with debouncing and early returns
- Cache results when appropriate
- Use multiple triggers (useBus + useEffect) for reliability

### **❌ DON'T**:
- Make getters async
- Use promises directly in getters
- Block the UI with synchronous operations
- Ignore error handling
- Make excessive backend calls

## 🚀 **Success Criteria**

### **Implementation** ✅
- [ ] Async operations properly separated from getters
- [ ] Reactive state management implemented
- [ ] Error handling with fallbacks
- [ ] Performance optimizations applied
- [ ] Testing coverage adequate

### **User Experience** ✅
- [ ] No UI blocking or freezing
- [ ] Immediate feedback on state changes
- [ ] Graceful error recovery
- [ ] Consistent behavior across scenarios

---

**Key Takeaway**: Never use async operations directly in getters. Always use the reactive state pattern to bridge async operations with synchronous template requirements.
