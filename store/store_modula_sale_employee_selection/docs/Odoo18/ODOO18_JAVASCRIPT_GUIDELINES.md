# Odoo 18 JavaScript Development Guidelines for AI

## 🎯 **Purpose**
Comprehensive guidelines for AI when working with Odoo 18 JavaScript files, based on real development experience with the employee selection module.

## 📋 **Core Principles**

### **1. Always Reference @odoo/addons/web/**
All Odoo 18 JavaScript components are built on the foundation provided in `@odoo/addons/web/`. 

#### **Key Reference Points:**
```javascript
// Form Controllers
import { FormController } from "@web/views/form/form_controller";

// Field Components  
import { FloatField } from "@web/views/fields/float/float_field";

// Services
import { useService } from "@web/core/utils/hooks";

// Dialog System
import { Dialog } from "@web/core/dialog/dialog";

// Utilities
import { patch } from "@web/core/utils/patch";
```

### **2. Follow Odoo 18 Component Patterns**
Use modern JavaScript patterns consistent with Odoo 18 architecture.

#### **Component Structure:**
```javascript
import { Component } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class MyComponent extends Component {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
    }
    
    // Methods here
}

MyComponent.template = "my_module.MyComponent";
```

## 🔧 **JavaScript File Patterns**

### **1. Field Extensions**

#### **Correct Pattern (Patching):**
```javascript
import { FloatField } from "@web/views/fields/float/float_field";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";

patch(FloatField.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.notification = useService("notification");
    },
    
    async onFocusOut() {
        // Custom logic here
        if (customCondition) {
            // Handle custom behavior
        } else {
            return super.onFocusOut();
        }
    }
});
```

#### **❌ Avoid:**
- Creating entirely new field components when patching is sufficient
- Overriding without calling `super.method()`
- Missing service imports

### **2. Form Controller Extensions**

#### **Correct Pattern:**
```javascript
import { FormController } from "@web/views/form/form_controller";
import { useService } from "@web/core/utils/hooks";
import { onWillStart, useSubEnv } from "@odoo/owl";

export class CustomFormController extends FormController {
    setup() {
        super.setup();
        
        // Services
        this.actionService = useService('action');
        this.notification = useService('notification');
        
        // Lifecycle hooks
        onWillStart(async () => {
            // Initialization logic
        });
        
        // Environment setup
        useSubEnv({
            // Custom environment variables
        });
    }
    
    async beforeExecuteActionButton(clickParams) {
        if (clickParams.name === "custom_action") {
            // Custom handling
            return false; // Prevent default
        }
        return super.beforeExecuteActionButton(clickParams);
    }
}
```

### **3. Service Usage Patterns**

#### **Essential Services:**
```javascript
// ORM for backend calls
this.orm = useService("orm");

// User notifications
this.notification = useService("notification");

// Dialog management
this.dialog = useService("dialog");

// Action execution
this.action = useService("action");
```

#### **Backend Communication:**
```javascript
// Correct ORM usage
const result = await this.orm.call("model.name", "method_name", [recordIds], {
    context: this.context,
    additional_param: value
});

// Error handling
try {
    const result = await this.orm.call(...);
    this.notification.add("Success message", { type: "success" });
} catch (error) {
    this.notification.add("Error message", { type: "danger" });
    console.error("Detailed error:", error);
}
```

### **4. Dialog and Popup Patterns**

#### **Dialog Service Usage:**
```javascript
import { Dialog } from "@web/core/dialog/dialog";

// Open dialog
this.dialog.add(DialogComponent, {
    title: "Dialog Title",
    body: "Dialog content",
    confirm: async () => {
        // Confirm action
    },
    cancel: () => {
        // Cancel action
    }
});
```

#### **Custom Popup Components:**
```javascript
export class CustomPopup extends Component {
    setup() {
        this.props = this.props;
    }
    
    onConfirm() {
        this.props.confirm?.();
        this.props.close();
    }
    
    onCancel() {
        this.props.cancel?.();
        this.props.close();
    }
}

CustomPopup.template = "module_name.CustomPopup";
CustomPopup.components = { Dialog };
```

## 📄 **XML Template Guidelines**

### **1. Template Naming Convention**

#### **Correct Pattern:**
```xml
<!-- File: static/src/components/my_component.xml -->
<templates xml:space="preserve">
    <t t-name="module_name.ComponentName">
        <!-- Template content -->
    </t>
</templates>
```

#### **JavaScript Reference:**
```javascript
// In JavaScript file
MyComponent.template = "module_name.ComponentName";
```

### **2. Template Structure**

#### **Dialog Templates:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.MyDialog">
        <Dialog title="props.title" size="'lg'">
            <div class="modal-body">
                <!-- Content here -->
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="onConfirm">
                    Confirm
                </button>
                <button class="btn btn-secondary" t-on-click="onCancel">
                    Cancel
                </button>
            </t>
        </Dialog>
    </t>
</templates>
```

#### **Field Templates:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.CustomField" t-inherit="web.FloatField" t-inherit-mode="extension">
        <xpath expr="//input" position="after">
            <button t-if="needsApproval" class="btn btn-sm btn-warning">
                Approve
            </button>
        </xpath>
    </t>
</templates>
```

## 🚨 **Common Pitfalls and Solutions**

### **1. Template Reference Errors**

#### **❌ Problem:**
```javascript
// Wrong template reference
MyComponent.template = 'wrong_module.TemplateName';
```

#### **✅ Solution:**
```javascript
// Correct template reference matching XML
MyComponent.template = 'correct_module.TemplateName';
```

#### **Verification:**
- Template name in XML must exactly match JavaScript reference
- Module name prefix must be consistent
- Check for typos in template names

### **2. Service Import Issues**

#### **❌ Problem:**
```javascript
// Missing service import
this.orm.call(...); // Error: orm is undefined
```

#### **✅ Solution:**
```javascript
// Proper service setup
setup() {
    super.setup();
    this.orm = useService("orm");
}
```

### **3. Async/Await Patterns**

#### **❌ Problem:**
```javascript
// Incorrect async handling
onFocusOut() {
    this.orm.call(...).then(result => {
        // Nested callbacks
    });
}
```

#### **✅ Solution:**
```javascript
// Proper async/await
async onFocusOut() {
    try {
        const result = await this.orm.call(...);
        // Handle result
    } catch (error) {
        // Handle error
    }
}
```

### **4. Form Save Integration**

#### **❌ Problem:**
```javascript
// Direct model access
this.model.save(); // May not work in all contexts
```

#### **✅ Solution:**
```javascript
// Proper form save
if (this.env.model.root.isDirty) {
    await this.env.model.root.save();
}
```

## 📊 **Best Practices from Experience**

### **1. Error Handling**

#### **Comprehensive Error Management:**
```javascript
async performAction() {
    try {
        // Main logic
        const result = await this.orm.call(...);

        // Success feedback
        this.notification.add("Operation successful", { type: "success" });

        return result;
    } catch (error) {
        // User-friendly error
        this.notification.add("Operation failed", { type: "danger" });

        // Developer error logging
        console.error("Detailed error:", error);

        // Re-throw if needed
        throw error;
    }
}
```

### **2. Form State Management - Critical Patterns**

#### **Preserving Field Values Without Saving:**
```javascript
// ✅ CRITICAL: Use _askChanges() to preserve field values in memory
async beforeExecuteActionButton(clickParams) {
    if (clickParams.name === "custom_action") {
        // Commit pending field changes to record data (NO backend save)
        await this.model._askChanges();

        // Now field values are preserved in memory
        // Continue with custom logic...

        return false; // Prevent default action
    }
    return super.beforeExecuteActionButton(clickParams);
}
```

#### **Form Save Prevention Patterns:**
```javascript
// When you need form to stay dirty but preserve values
// Use _askChanges() instead of save()

// ❌ WRONG: This saves to backend
await this.model.root.save();

// ✅ CORRECT: This preserves values in memory only
await this.model._askChanges();
```

### **3. DOM Manipulation for UI Control**

#### **Direct Button Visibility Control:**
```javascript
// When backend state changes won't persist (form stays dirty)
// Use direct DOM manipulation for immediate UI feedback
const hideApproveButton = () => {
    const buttons = document.getElementsByName('action_name');
    for (let i = 0; i < buttons.length; i++) {
        buttons[i].style.display = 'none';
    }
};

const showApproveButton = () => {
    const buttons = document.getElementsByName('action_name');
    for (let i = 0; i < buttons.length; i++) {
        buttons[i].style.display = '';
    }
};
```

### **4. Template Method Pattern Implementation**

#### **Backend Template Method:**
```python
# Base module - template method
def _get_employee_is_show(self, employee_data):
    """Template method - override in dependent modules"""
    return True  # Default behavior

def get_all_employees(self, login=False):
    employees = self.search_read([...], ["id", "name", "barcode"])
    for employee in employees:
        employee["is_show"] = self._get_employee_is_show(employee)
    return {"all": employees}
```

#### **Frontend Integration:**
```javascript
// Update employee list with backend filtering
async beforeExecuteActionButton(clickParams) {
    if (clickParams.name === "action_approve_sale_order") {
        await this.model._askChanges();

        // Get filtered employees from backend
        await this.useEmployee.getAllEmployees();
        this.useEmployee.filterEmployeesByIsShow();

        this.useEmployee.popupAddEmployee();
        return false;
    }
    return super.beforeExecuteActionButton(clickParams);
}
```

### **5. Employee Hooks Pattern**

#### **Service-Based Hook Implementation:**
```javascript
export function pickUseConnectedEmployee(controllerType, context, workcenterId, env) {
    const orm = useService("orm");
    const notification = useService("notification");

    // State management
    const employees = useState({
        connected: [],
        all: [],
        admin: {},
    });

    // Backend integration
    const getAllEmployees = async () => {
        const res = await orm.call("hr.employee", "get_all_employees", [false]);
        if (res.all) {
            employees.all = res.all;
        }
    };

    // Frontend filtering with data enhancement
    const filterEmployeesByIsShow = () => {
        if (employees.all && Array.isArray(employees.all)) {
            // Filter employees with is_show = true
            employees.all = employees.all.filter(employee => employee.is_show === true);

            // Add default action_approve_sale_order field to each employee
            employees.all = employees.all.map(employee => ({
                ...employee,
                action_approve_sale_order: true  // Default value for approval action
            }));
        }
    };

    return {
        getAllEmployees,
        filterEmployeesByIsShow,
        employees,
        // ... other methods
    };
}
```

### **6. Component Lifecycle Management**

#### **Proper Setup and Cleanup:**
```javascript
setup() {
    super.setup();

    // Services
    this.orm = useService("orm");
    this.notification = useService("notification");

    // Lifecycle hooks
    onWillStart(async () => {
        // Initialization
        await this.loadInitialData();
    });

    // Cleanup on destroy
    onWillDestroy(() => {
        this.cleanup();
    });
}
```

## 🚨 **Critical Patterns from Real Experience**

### **1. Form Save vs Field Preservation**
```javascript
// ❌ WRONG: When you need form to stay dirty
await this.model.root.save(); // This saves to backend

// ✅ CORRECT: Preserve field values without saving
await this.model._askChanges(); // Commits to memory only
```

### **2. Button Hiding in No-Save Workflows**
```javascript
// ❌ WRONG: Backend state change won't persist without save
await orm.call("model", "write", [[id], {need_approve: false}]);

// ✅ CORRECT: Direct DOM manipulation for immediate feedback
document.getElementsByName('action_name')[0].style.display = 'none';
```

### **3. Popup Close Callback Timing**
```javascript
// ❌ WRONG: closePopup() called before callback runs
selectEmployee() {
    // ... logic ...
    closePopup("SelectionPopup"); // Prevents onClosePopup from running
}

// ✅ CORRECT: Action before popup close
selectEmployee() {
    // ... logic ...
    await hideApproveButton(); // Do action first
    closePopup("SelectionPopup"); // Then close
}
```

## 🎯 **AI Development Checklist**

### **Before Writing JavaScript Code:**
- [ ] **Identify base component** from @web/addons/web/
- [ ] **Check existing patterns** in similar Odoo components
- [ ] **Plan service dependencies** (orm, notification, dialog, etc.)
- [ ] **Design error handling** strategy
- [ ] **Consider template requirements**
- [ ] **Determine if form saves are needed** or should be prevented

### **During Implementation:**
- [ ] **Import all required services** in setup()
- [ ] **Call super methods** when extending
- [ ] **Use async/await** for backend calls
- [ ] **Add comprehensive error handling**
- [ ] **Include user feedback** (notifications)
- [ ] **Use _askChanges() for field preservation** without saving
- [ ] **Use DOM manipulation** for UI changes in no-save workflows

### **After Implementation:**
- [ ] **Verify template references** match XML
- [ ] **Test error scenarios** (network issues, invalid data)
- [ ] **Check browser console** for JavaScript errors
- [ ] **Validate integration** with other components
- [ ] **Test form dirty state** behavior
- [ ] **Verify button visibility** logic works correctly
- [ ] **Document any custom patterns** used

## 📋 **AI Prompt Templates**

### **For JavaScript Analysis:**
```markdown
"Analyze the JavaScript implementation in @[module_path]/ focusing on:
1. Integration with @odoo/addons/web/ patterns
2. Service usage (orm, notification, dialog)
3. Component lifecycle and error handling
4. Template references and XML integration
Identify any issues and suggest improvements following Odoo 18 best practices."
```

### **For JavaScript Implementation:**
```markdown
"Implement [specific feature] in Odoo 18 JavaScript following these requirements:
- Base component: [FormController/FloatField/etc.]
- Services needed: [orm, notification, dialog]
- Integration with: @odoo/addons/web/ patterns
- Template name: [module_name.ComponentName]
- Error handling: Comprehensive with user feedback
- Preserve existing: [specific functionality]"
```

### **For Template Issues:**
```markdown
"Fix template reference error in [file_path]:
- JavaScript template reference: [current_reference]
- XML template name: [current_xml_name]
- Expected behavior: [description]
Ensure template names match exactly and follow Odoo 18 patterns."
```

## 🎯 **Advanced Patterns from Experience**

### **1. Template Inheritance for Custom Buttons**
```javascript
// Pattern for complex button behaviors
import { StatusBarButtons } from "@web/views/form/status_bar_buttons/status_bar_buttons";
import { patch } from "@web/core/utils/patch";
import { useState, useBus, useEffect } from "@odoo/owl";

patch(StatusBarButtons.prototype, {
    setup() {
        super.setup();
        if (this.env.model?.root.resModel == 'sale.order') {
            // Reactive state for button visibility
            this.needApprove = useState({ value: false });

            // Listen to model changes
            useBus(this.env.model.bus, "update", async () => {
                await this.updateNeedApproveFromBackend();
            });
        }
    },

    async updateNeedApproveFromBackend() {
        // Async backend validation with product_id context
        const result = await this.env.services.orm.call(
            "sale.order", "need_employee_selection", [recordId],
            { product_id: productId, field_name: 'discount', input: value }
        );
        this.needApprove.value = result;
    },

    get shouldShowApproveButton() {
        // Synchronous getter using reactive state
        return this.needApprove?.value || this.env.model.root.data.need_approve;
    }
});
```

### **2. Async Operations in Non-Async Contexts**
```javascript
// ❌ WRONG: Cannot use async in getters
get shouldShow() {
    const result = await this.orm.call(...); // SyntaxError
    return result;
}

// ✅ CORRECT: Reactive state pattern
setup() {
    this.validationState = useState({ isValid: false });

    useBus(this.env.model.bus, "update", async () => {
        const result = await this.orm.call(...);
        this.validationState.isValid = result;
    });
}

get shouldShow() {
    return this.validationState.isValid; // Synchronous
}
```

### **3. Order Line Array Processing**
```javascript
// Process all order lines in single backend call
async onFocusOut() {
    if (parent_model === 'sale.order' && this.props.record.dirty) {
        const record = this.env.model.root;
        const orderLines = record.data.order_line.records;

        // Collect all order line data
        const order_line_data = orderLines.map(line => ({
            id: line.resId,
            product_id: line.data.product_id[0],
            discount: line.data.discount
        }));

        // Single backend call for all lines
        const result = await this.orm.call("sale.order", "need_employee_selection",
            [parent_id],
            { 'order_line': order_line_data }
        );

        this.env.model.root.data.need_approve = result;
        this.env.model.notify(); // Trigger reactive updates
    }
}
```

## 🚀 **Conclusion**

Following these guidelines ensures:
- **Consistency** with Odoo 18 architecture
- **Reliability** through proper error handling
- **Maintainability** through standard patterns
- **Integration** with existing Odoo components
- **Reactive behavior** for complex UI interactions
- **Proper async handling** in all contexts

### **Key Takeaways**:
- Always reference `@odoo/addons/web/` for base components
- Use `_askChanges()` for field preservation without saving
- Use DOM manipulation for UI changes in no-save workflows
- Follow template method pattern for extensibility
- **Use reactive state pattern for async operations in getters**
- **Template inheritance for complex button behaviors**
- **Event-driven updates with useBus and useEffect**

Always reference `@odoo/addons/web/` for standard patterns and maintain consistency with Odoo's modern JavaScript architecture.
